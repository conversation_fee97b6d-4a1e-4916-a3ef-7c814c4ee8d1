package crypto

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/pem"
	"time"
	"fmt"
)

// 只硬编码公钥
const rsapub = `-----BEGIN RSA PUBLIC KEY-----
MIIBCgKCAQEA1C7...YOUR_PUB_KEY_HERE...IDAQAB
-----END RSA PUBLIC KEY-----`

// 基于时间和域名生成动态引导密钥
func GenerateDynamicBootstrapKey(c2Address string) []byte {
	// 获取当前时间的小时数 (每小时变化一次)
	currentHour := time.Now().UTC().Hour()

	// 基于C2地址和时间生成种子
	seed := fmt.Sprintf("%s_%d_%s", c2Address, currentHour, "goc2_bootstrap_2025")

	// 使用SHA256生成32字节密钥
	hash := sha256.Sum256([]byte(seed))
	return hash[:]
}

// 生成随机32字节会话密钥并用 RSA-OAEP 加密
func BuildBootstrapPacket() (plainKey []byte, encrypted []byte, err error) {
	block, _ := pem.Decode([]byte(rsapub))
	pub, _ := x509.ParsePKCS1PublicKey(block.Bytes)

	plainKey = make([]byte, 32)
	rand.Read(plainKey)

	encrypted, err = rsa.EncryptOAEP(sha256.New(), rand.Reader, pub, plainKey, nil)
	return
}