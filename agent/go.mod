module agent

go 1.20

require (
	github.com/atotto/clipboard v0.1.4
	github.com/creack/pty v1.1.24
	github.com/glebarez/go-sqlite v1.22.0
	github.com/go-ole/go-ole v1.2.6
	github.com/go-sql-driver/mysql v1.9.3
	github.com/google/uuid v1.6.0
	github.com/iamacarpet/go-winpty v1.0.4
	github.com/kbinani/screenshot v0.0.0-20250118074034-a3924b7bbc8c
	github.com/lib/pq v1.10.9
	github.com/malfunkt/iprange v0.9.0
	github.com/mitchellh/go-ps v1.0.0
	github.com/parsiya/golnk v0.0.0-20221103095132-740a4c27c4ff
	github.com/refraction-networking/utls v1.7.3
	github.com/shirou/gopsutil/v3 v3.24.5
	github.com/xtaci/smux v1.5.34
	github.com/yusufpapurcu/wmi v1.2.4
	golang.org/x/crypto v0.39.0
	golang.org/x/net v0.41.0
	golang.org/x/sys v0.33.0
	golang.org/x/text v0.26.0
	golang.org/x/time v0.12.0
)

require (
	github.com/andybalholm/brotli v1.0.6 // indirect
	github.com/cloudflare/circl v1.5.0 // indirect
	github.com/dustin/go-humanize v1.0.1 // indirect
	github.com/gen2brain/shm v0.1.0 // indirect
	github.com/godbus/dbus/v5 v5.1.0 // indirect
	github.com/jezek/xgb v1.1.1 // indirect
	github.com/klauspost/compress v1.17.4 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/lxn/win v0.0.0-20210218163916-a377121e959e // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mattn/go-runewidth v0.0.9 // indirect
	github.com/ncruces/go-strftime v0.1.9 // indirect
	github.com/olekukonko/tablewriter v0.0.5 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/quic-go/quic-go v0.40.1 // indirect
	github.com/remyoudompheng/bigfft v0.0.0-20230129092748-24d4a6f8daec // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	modernc.org/libc v1.41.0 // indirect
	modernc.org/mathutil v1.6.0 // indirect
	modernc.org/memory v1.7.2 // indirect
	modernc.org/sqlite v1.28.0 // indirect
)

replace (
	github.com/cloudflare/circl => github.com/cloudflare/circl v1.3.7
	github.com/go-sql-driver/mysql => github.com/go-sql-driver/mysql v1.7.1
	github.com/refraction-networking/utls => github.com/refraction-networking/utls v1.6.3
	// 在这里添加指令，因为 replace 的优先级更高
	golang.org/x/crypto => golang.org/x/crypto v0.17.0 //go:garble no-garble
	golang.org/x/net => golang.org/x/net v0.17.0
	golang.org/x/sys => golang.org/x/sys v0.15.0
	golang.org/x/text => golang.org/x/text v0.14.0
	golang.org/x/time => golang.org/x/time v0.3.0
)
