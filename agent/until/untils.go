//go:build windows
// +build windows

package until

import (
	"fmt"
	"os"
	"os/user"
)

// GetHostAndUserInfo 获取机器名和用户名的函数
func GetHostAndUserInfo() (string, string, error) {
	// 获取机器名
	hostname, err := os.Hostname()
	if err != nil {
		return "", "", fmt.Errorf("无法获取机器名: %v", err)
	}

	// 获取用户名
	currentUser, err := user.Current()
	if err != nil {
		return "", "", fmt.<PERSON><PERSON><PERSON>("无法获取用户名: %v", err)
	}

	return hostname, currentUser.Username, nil
}
