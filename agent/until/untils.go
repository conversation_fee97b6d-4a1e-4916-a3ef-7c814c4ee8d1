//go:build windows
// +build windows

package until

import (
	"fmt"
	"golang.org/x/sys/windows"
	"os"
	"os/user"
	"unsafe"
)

// GetHostAndUserInfo 获取机器名和用户名的函数
func GetHostAndUserInfo() (string, string, error) {
	// 获取机器名
	hostname, err := os.Hostname()
	if err != nil {
		return "", "", fmt.Errorf("无法获取机器名: %v", err)
	}

	// 获取用户名
	currentUser, err := user.Current()
	if err != nil {
		return "", "", fmt.Errorf("无法获取用户名: %v", err)
	}

	return hostname, currentUser.Username, nil
}

var mutexHandle windows.Handle

// buildSA 构造一个带 Everyone:GA DACL 的 SecurityAttributes
func buildSA() (*windows.SecurityAttributes, error) {
	const sddl = "D:(A;;GA;;;WD)"
	sddlPtr, _ := windows.UTF16PtrFromString(sddl)

	var sd *windows.SECURITY_DESCRIPTOR
	proc := windows.NewLazySystemDLL("advapi32.dll").
		NewProc("ConvertStringSecurityDescriptorToSecurityDescriptorW")
	r1, _, err := proc.Call(
		uintptr(unsafe.Pointer(sddlPtr)), // SDDL 字符串
		uintptr(1),                       // SDDL_REVISION_1
		uintptr(unsafe.Pointer(&sd)),     // 输出 SD*
		0,
	)
	if r1 == 0 {
		return nil, err
	}

	return &windows.SecurityAttributes{
		Length:             uint32(unsafe.Sizeof(windows.SecurityAttributes{})),
		SecurityDescriptor: sd,
		InheritHandle:      0,
	}, nil
}

// IsAlreadyRunning 确保 SYSTEM + 用户 Session 互斥，已有实例返回 true
func IsAlreadyRunning() bool {
	const name = "Global\\{166371-1234-1234-1234-143123459ABC}" // 换成你的 GUID

	sa, err := buildSA()
	if err != nil {
		fmt.Printf("构造 SecurityAttributes 失败: %v\n", err)
		return false // 构造失败就放行
	}

	// 不“先拥有”，只是创建或打开
	h, err := windows.CreateMutex(sa, false, windows.StringToUTF16Ptr(name))
	if h == 0 {
		fmt.Printf("CreateMutex 失败: %v\n", err)
		return false
	}

	// WaitForSingleObject 的第一个返回值在一些版本中是 Errno（uintptr），
	// 这里把它强制转为 uint32，以便跟常量比较
	raw, waitErr := windows.WaitForSingleObject(h, 0)
	if waitErr != nil {
		fmt.Printf("WaitForSingleObject 失败: %v\n", waitErr)
		mutexHandle = h
		return false
	}
	ret := uint32(raw)

	switch ret {
	case windows.WAIT_OBJECT_0, windows.WAIT_ABANDONED:
		// 第一实例：我们拿到锁
		mutexHandle = h
		return false
	case uint32(windows.WAIT_TIMEOUT):
		// 已被占用：直接退出
		windows.CloseHandle(h)
		return true
	default:
		// 其它情况都当第一实例
		mutexHandle = h
		return false
	}
}
