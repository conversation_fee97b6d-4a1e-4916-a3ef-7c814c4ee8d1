package help

import (
	"agent/global"
	"path/filepath"
	"strings"
)

// --- 新增: 健壮的路径解析辅助函数 ---
func ResolvePath(payloadPath string) string {
	cleanPath := strings.TrimSpace(payloadPath)
	cleanPath = strings.Trim(cleanPath, `"'`)
	if cleanPath == "" || cleanPath == "." {
		return global.CurrentDir
	}
	if filepath.IsAbs(cleanPath) {
		return filepath.Clean(cleanPath)
	}
	return filepath.Join(global.CurrentDir, cleanPath)
}
