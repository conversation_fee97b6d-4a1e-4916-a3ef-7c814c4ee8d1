//go:build windows
// +build windows

package commands

import (
	"os/exec"
	"runtime"
	"strings"
	"syscall"
)

// getDriveType 获取Windows盘符类型
func getDriveType(drivePath string) string {
	if runtime.GOOS != "windows" {
		return "unknown"
	}

	// 这里简化处理，实际可以调用GetDriveType API
	// 暂时根据盘符判断常见类型
	drive := strings.ToUpper(drivePath[:1])
	switch drive {
	case "A", "B":
		return "removable"
	case "C", "D", "E", "F", "G", "H":
		return "fixed"
	default:
		return "fixed"
	}
}

// setWindowsHiddenProcess 设置Windows进程隐藏属性
func setWindowsHiddenProcess(cmd *exec.Cmd) {
	if runtime.GOOS == "windows" {
		const CREATE_NO_WINDOW = 0x08000000
		cmd.SysProcAttr = &syscall.SysProcAttr{
			HideWindow:    true,
			CreationFlags: CREATE_NO_WINDOW,
		}
	}
}

// setWindowsExecuteHiddenProcess 设置Windows执行进程隐藏属性
func setWindowsExecuteHiddenProcess(cmd *exec.Cmd) {
	if runtime.GOOS == "windows" {
		cmd.SysProcAttr = &syscall.SysProcAttr{
			HideWindow:    true,
			CreationFlags: 0x08000000 | 0x00000008,
		}
	}
}
