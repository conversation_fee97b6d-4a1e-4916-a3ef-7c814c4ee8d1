//go:build !windows
// +build !windows

package remote_desktop

// CmdRemoteDesktopStart 在非Windows平台返回不支持信息
func CmdRemoteDesktopStart() (string, error) {
	return "实时远程桌面功能仅在Windows平台支持", nil
}

// CmdRemoteDesktopStop 在非Windows平台返回不支持信息
func CmdRemoteDesktopStop() (string, error) {
	return "实时远程桌面功能仅在Windows平台支持", nil
}

// CmdRemoteDesktopStatus 在非Windows平台返回不支持信息
func CmdRemoteDesktopStatus() (string, error) {
	return "实时远程桌面功能仅在Windows平台支持", nil
}

// CmdRemoteDesktopControl 在非Windows平台返回不支持信息
func CmdRemoteDesktopControl(payload string) (string, error) {
	return "实时远程桌面功能仅在Windows平台支持", nil
}
