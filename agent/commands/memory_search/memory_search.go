package memory_search

import (
	"fmt"
	"strconv"
	"strings"
	"unsafe"

	"golang.org/x/sys/windows"
)

// MEMORY_BASIC_INFORMATION 内存基本信息结构
type MEMORY_BASIC_INFORMATION struct {
	BaseAddress       uintptr
	AllocationBase    uintptr
	AllocationProtect uint32
	RegionSize        uintptr
	State             uint32
	Protect           uint32
	Type              uint32
}

// 内存状态常量
const (
	MEM_COMMIT  = 0x1000
	MEM_RESERVE = 0x2000
	MEM_FREE    = 0x10000
)

// 内存保护常量
const (
	PAGE_EXECUTE           = 0x10
	PAGE_EXECUTE_READ      = 0x20
	PAGE_EXECUTE_READWRITE = 0x40
	PAGE_EXECUTE_WRITECOPY = 0x80
	PAGE_NOACCESS          = 0x01
	PAGE_READONLY          = 0x02
	PAGE_READWRITE         = 0x04
	PAGE_WRITECOPY         = 0x08
	PAGE_GUARD             = 0x100
	PAGE_NOCACHE           = 0x200
	PAGE_WRITECOMBINE      = 0x400
)

// 进程访问权限常量
const (
	PROCESS_QUERY_INFORMATION = 0x0400
	PROCESS_VM_READ           = 0x0010
	PROCESS_ALL_ACCESS        = 0x1F0FFF
)

var (
	kernel32                = windows.NewLazySystemDLL("kernel32.dll")
	procVirtualQueryEx      = kernel32.NewProc("VirtualQueryEx")
	procReadProcessMemory   = kernel32.NewProc("ReadProcessMemory")
	procOpenProcess         = kernel32.NewProc("OpenProcess")
	procCloseHandle         = kernel32.NewProc("CloseHandle")
	procCreateToolhelp32Snapshot = kernel32.NewProc("CreateToolhelp32Snapshot")
	procProcess32First      = kernel32.NewProc("Process32FirstW")
	procProcess32Next       = kernel32.NewProc("Process32NextW")
)

// PROCESSENTRY32 进程信息结构
type PROCESSENTRY32 struct {
	Size              uint32
	Usage             uint32
	ProcessID         uint32
	DefaultHeapID     uintptr
	ModuleID          uint32
	Threads           uint32
	ParentProcessID   uint32
	PriorityClassBase int32
	Flags             uint32
	ExeFile           [260]uint16
}

// GetProcessIDs 根据进程名获取所有匹配的进程ID
func GetProcessIDs(processName string) ([]uint32, error) {
	const TH32CS_SNAPPROCESS = 0x00000002
	
	snapshot, _, _ := procCreateToolhelp32Snapshot.Call(TH32CS_SNAPPROCESS, 0)
	if snapshot == uintptr(windows.InvalidHandle) {
		return nil, fmt.Errorf("创建进程快照失败")
	}
	defer procCloseHandle.Call(snapshot)

	var pe32 PROCESSENTRY32
	pe32.Size = uint32(unsafe.Sizeof(pe32))

	var processIDs []uint32
	
	ret, _, _ := procProcess32First.Call(snapshot, uintptr(unsafe.Pointer(&pe32)))
	if ret == 0 {
		return nil, fmt.Errorf("获取第一个进程失败")
	}

	for {
		exeName := windows.UTF16ToString(pe32.ExeFile[:])
		if strings.EqualFold(exeName, processName) {
			processIDs = append(processIDs, pe32.ProcessID)
		}

		ret, _, _ := procProcess32Next.Call(snapshot, uintptr(unsafe.Pointer(&pe32)))
		if ret == 0 {
			break
		}
	}

	return processIDs, nil
}

// OpenProcessByID 根据进程ID打开进程句柄
func OpenProcessByID(processID uint32) (windows.Handle, error) {
	handle, _, err := procOpenProcess.Call(
		PROCESS_QUERY_INFORMATION|PROCESS_VM_READ,
		0,
		uintptr(processID),
	)
	
	if handle == 0 {
		return windows.InvalidHandle, fmt.Errorf("打开进程失败: %v", err)
	}
	
	return windows.Handle(handle), nil
}

// VirtualQueryExWrapper 查询虚拟内存信息
func VirtualQueryExWrapper(hProcess windows.Handle, address uintptr) (*MEMORY_BASIC_INFORMATION, error) {
	var mbi MEMORY_BASIC_INFORMATION
	ret, _, _ := procVirtualQueryEx.Call(
		uintptr(hProcess),
		address,
		uintptr(unsafe.Pointer(&mbi)),
		unsafe.Sizeof(mbi),
	)
	
	if ret == 0 {
		return nil, fmt.Errorf("VirtualQueryEx失败")
	}
	
	return &mbi, nil
}

// ReadBytesFromMemory 从进程内存中读取数据
func ReadBytesFromMemory(hProcess windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	buffer := make([]byte, size)
	var bytesRead uintptr
	
	ret, _, _ := procReadProcessMemory.Call(
		uintptr(hProcess),
		address,
		uintptr(unsafe.Pointer(&buffer[0])),
		size,
		uintptr(unsafe.Pointer(&bytesRead)),
	)
	
	if ret == 0 {
		return nil, fmt.Errorf("ReadProcessMemory失败")
	}
	
	return buffer[:bytesRead], nil
}

// parsePattern 解析十六进制模式字符串
func parsePattern(pattern string) ([]byte, []byte, error) {
	parts := strings.Fields(pattern)
	patternBytes := make([]byte, 0, len(parts))
	maskBytes := make([]byte, 0, len(parts))

	for _, part := range parts {
		if len(part) != 2 {
			return nil, nil, fmt.Errorf("无效的模式字节长度: %s", part)
		}
		var b byte = 0
		var m byte = 0
		
		for _, ch := range part {
			b <<= 4
			m <<= 4
			if ch == '?' {
				b |= 0x0
				m |= 0x0
			} else {
				digit, err := strconv.ParseUint(string(ch), 16, 4)
				if err != nil {
					return nil, nil, fmt.Errorf("无效的十六进制字符 '%c' in %s", ch, part)
				}
				b |= byte(digit)
				m |= 0xF
			}
		}
		patternBytes = append(patternBytes, b)
		maskBytes = append(maskBytes, m)
	}

	return patternBytes, maskBytes, nil
}

// SearchMemoryBlock 在内存块中搜索特征码
func SearchMemoryBlock(hProcess windows.Handle, pattern []byte, mask []byte, startAddress uintptr, size uintptr, resultArray *[]uintptr) {
	data, err := ReadBytesFromMemory(hProcess, startAddress, size)
	if err != nil {
		return
	}

	patternLength := len(pattern)
	i := 0
	for i <= len(data)-patternLength {
		j := 0
		for j < patternLength {
			if (data[i+j] & mask[j]) != (pattern[j] & mask[j]) {
				break
			}
			j++
		}

		if j == patternLength {
			matchAddr := startAddress + uintptr(i)
			*resultArray = append(*resultArray, matchAddr)
		}
		i++
	}
}

// SearchFeatures 在进程中搜索特征码
func SearchFeatures(hProcess windows.Handle, patternStr string) ([]uintptr, int) {
	pattern, mask, err := parsePattern(patternStr)
	if err != nil {
		return nil, 0
	}

	var results []uintptr
	var address uintptr = 0

	for {
		mbi, err := VirtualQueryExWrapper(hProcess, address)
		if err != nil {
			break
		}

		if mbi.State == MEM_COMMIT && 
		   (mbi.Protect&PAGE_GUARD) == 0 && 
		   (mbi.Protect&PAGE_NOACCESS) == 0 {
			SearchMemoryBlock(hProcess, pattern, mask, mbi.BaseAddress, mbi.RegionSize, &results)
		}

		address = mbi.BaseAddress + mbi.RegionSize
		if address == 0 {
			break
		}
	}

	return results, len(results)
}
