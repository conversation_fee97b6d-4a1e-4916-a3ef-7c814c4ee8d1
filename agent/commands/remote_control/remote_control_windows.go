//go:build windows
// +build windows

package remote_control

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

// Windows API 常量
const (
	// 鼠标事件
	MOUSEEVENTF_MOVE       = 0x0001
	MOUSEEVENTF_LEFTDOWN   = 0x0002
	MOUSEEVENTF_LEFTUP     = 0x0004
	MOUSEEVENTF_RIGHTDOWN  = 0x0008
	MOUSEEVENTF_RIGHTUP    = 0x0010
	MOUSEEVENTF_MIDDLEDOWN = 0x0020
	MOUSEEVENTF_MIDDLEUP   = 0x0040
	MOUSEEVENTF_WHEEL      = 0x0800
	MOUSEEVENTF_ABSOLUTE   = 0x8000

	// 键盘事件
	KEYEVENTF_KEYDOWN = 0x0000
	KEYEVENTF_KEYUP   = 0x0002

	// 虚拟键码
	VK_LWIN    = 0x5B
	VK_RETURN  = 0x0D
	VK_SPACE   = 0x20
	VK_ESCAPE  = 0x1B
	VK_TAB     = 0x09
	VK_CONTROL = 0x11
	VK_ALT     = 0x12
	VK_SHIFT   = 0x10

	// 屏幕解锁相关
	VK_L = 0x4C
	VK_U = 0x55
)

// Windows API 函数
var (
	user32                = windows.NewLazySystemDLL("user32.dll")
	procSetCursorPos      = user32.NewProc("SetCursorPos")
	procGetCursorPos      = user32.NewProc("GetCursorPos")
	procmouse_event       = user32.NewProc("mouse_event")
	prockeybd_event       = user32.NewProc("keybd_event")
	procGetSystemMetrics  = user32.NewProc("GetSystemMetrics")
	procSendInput         = user32.NewProc("SendInput")
	procGetForegroundWindow = user32.NewProc("GetForegroundWindow")
	procSetForegroundWindow = user32.NewProc("SetForegroundWindow")
	procFindWindow        = user32.NewProc("FindWindowW")
)

// 数据结构
type POINT struct {
	X, Y int32
}

type INPUT struct {
	Type uint32
	Data [24]byte
}

type MOUSEINPUT struct {
	Dx          int32
	Dy          int32
	MouseData   uint32
	DwFlags     uint32
	Time        uint32
	DwExtraInfo uintptr
}

type KEYBDINPUT struct {
	Wvk         uint16
	Wscan       uint16
	DwFlags     uint32
	Time        uint32
	DwExtraInfo uintptr
}

// 远程控制命令结构
type RemoteCommand struct {
	Action string                 `json:"action"`
	Params map[string]interface{} `json:"params"`
}

// CmdRemoteControl 远程控制主函数
func CmdRemoteControl(payload string) (string, error) {
	var cmd RemoteCommand
	if err := json.Unmarshal([]byte(payload), &cmd); err != nil {
		return "", fmt.Errorf("解析远程控制命令失败: %v", err)
	}

	switch cmd.Action {
	case "mouse_move":
		return handleMouseMove(cmd.Params)
	case "mouse_click":
		return handleMouseClick(cmd.Params)
	case "mouse_drag":
		return handleMouseDrag(cmd.Params)
	case "key_press":
		return handleKeyPress(cmd.Params)
	case "key_combination":
		return handleKeyCombination(cmd.Params)
	case "type_text":
		return handleTypeText(cmd.Params)
	case "unlock_screen":
		return handleUnlockScreen(cmd.Params)
	case "get_cursor_pos":
		return handleGetCursorPos()
	case "get_screen_size":
		return handleGetScreenSize()
	default:
		return "", fmt.Errorf("未知的远程控制命令: %s", cmd.Action)
	}
}

// 鼠标移动
func handleMouseMove(params map[string]interface{}) (string, error) {
	x, ok1 := params["x"].(float64)
	y, ok2 := params["y"].(float64)
	if !ok1 || !ok2 {
		return "", fmt.Errorf("缺少坐标参数")
	}

	ret, _, _ := procSetCursorPos.Call(uintptr(int32(x)), uintptr(int32(y)))
	if ret == 0 {
		return "", fmt.Errorf("移动鼠标失败")
	}

	return fmt.Sprintf("鼠标已移动到 (%d, %d)", int32(x), int32(y)), nil
}

// 鼠标点击
func handleMouseClick(params map[string]interface{}) (string, error) {
	x, ok1 := params["x"].(float64)
	y, ok2 := params["y"].(float64)
	button, ok3 := params["button"].(string)
	if !ok1 || !ok2 || !ok3 {
		return "", fmt.Errorf("缺少点击参数")
	}

	// 移动到目标位置
	procSetCursorPos.Call(uintptr(int32(x)), uintptr(int32(y)))
	time.Sleep(50 * time.Millisecond)

	// 执行点击
	var downFlag, upFlag uint32
	switch button {
	case "left":
		downFlag, upFlag = MOUSEEVENTF_LEFTDOWN, MOUSEEVENTF_LEFTUP
	case "right":
		downFlag, upFlag = MOUSEEVENTF_RIGHTDOWN, MOUSEEVENTF_RIGHTUP
	case "middle":
		downFlag, upFlag = MOUSEEVENTF_MIDDLEDOWN, MOUSEEVENTF_MIDDLEUP
	default:
		return "", fmt.Errorf("未知的鼠标按钮: %s", button)
	}

	// 按下
	procmouse_event.Call(uintptr(downFlag), uintptr(int32(x)), uintptr(int32(y)), 0, 0)
	time.Sleep(50 * time.Millisecond)
	// 释放
	procmouse_event.Call(uintptr(upFlag), uintptr(int32(x)), uintptr(int32(y)), 0, 0)

	return fmt.Sprintf("在 (%d, %d) 执行了%s键点击", int32(x), int32(y), button), nil
}

// 鼠标拖拽
func handleMouseDrag(params map[string]interface{}) (string, error) {
	x1, ok1 := params["x1"].(float64)
	y1, ok2 := params["y1"].(float64)
	x2, ok3 := params["x2"].(float64)
	y2, ok4 := params["y2"].(float64)
	if !ok1 || !ok2 || !ok3 || !ok4 {
		return "", fmt.Errorf("缺少拖拽坐标参数")
	}

	// 移动到起始位置
	procSetCursorPos.Call(uintptr(int32(x1)), uintptr(int32(y1)))
	time.Sleep(50 * time.Millisecond)

	// 按下左键
	procmouse_event.Call(uintptr(MOUSEEVENTF_LEFTDOWN), uintptr(int32(x1)), uintptr(int32(y1)), 0, 0)
	time.Sleep(50 * time.Millisecond)

	// 拖拽到目标位置
	steps := 10
	for i := 1; i <= steps; i++ {
		currentX := int32(x1 + float64(i)*(x2-x1)/float64(steps))
		currentY := int32(y1 + float64(i)*(y2-y1)/float64(steps))
		procSetCursorPos.Call(uintptr(currentX), uintptr(currentY))
		time.Sleep(20 * time.Millisecond)
	}

	// 释放左键
	procmouse_event.Call(uintptr(MOUSEEVENTF_LEFTUP), uintptr(int32(x2)), uintptr(int32(y2)), 0, 0)

	return fmt.Sprintf("从 (%d, %d) 拖拽到 (%d, %d)", int32(x1), int32(y1), int32(x2), int32(y2)), nil
}

// 按键
func handleKeyPress(params map[string]interface{}) (string, error) {
	keyStr, ok := params["key"].(string)
	if !ok {
		return "", fmt.Errorf("缺少按键参数")
	}

	vkCode := getVirtualKeyCode(keyStr)
	if vkCode == 0 {
		return "", fmt.Errorf("未知的按键: %s", keyStr)
	}

	// 按下
	prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
	time.Sleep(50 * time.Millisecond)
	// 释放
	prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYUP), 0)

	return fmt.Sprintf("按下了按键: %s", keyStr), nil
}

// 组合键
func handleKeyCombination(params map[string]interface{}) (string, error) {
	keysInterface, ok := params["keys"].([]interface{})
	if !ok {
		return "", fmt.Errorf("缺少组合键参数")
	}

	var keys []string
	for _, k := range keysInterface {
		if keyStr, ok := k.(string); ok {
			keys = append(keys, keyStr)
		}
	}

	if len(keys) == 0 {
		return "", fmt.Errorf("组合键列表为空")
	}

	// 按下所有键
	var vkCodes []uint16
	for _, key := range keys {
		vkCode := getVirtualKeyCode(key)
		if vkCode == 0 {
			return "", fmt.Errorf("未知的按键: %s", key)
		}
		vkCodes = append(vkCodes, vkCode)
		prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
		time.Sleep(10 * time.Millisecond)
	}

	time.Sleep(100 * time.Millisecond)

	// 释放所有键（逆序）
	for i := len(vkCodes) - 1; i >= 0; i-- {
		prockeybd_event.Call(uintptr(vkCodes[i]), 0, uintptr(KEYEVENTF_KEYUP), 0)
		time.Sleep(10 * time.Millisecond)
	}

	return fmt.Sprintf("执行了组合键: %s", strings.Join(keys, "+")), nil
}

// 输入文本
func handleTypeText(params map[string]interface{}) (string, error) {
	text, ok := params["text"].(string)
	if !ok {
		return "", fmt.Errorf("缺少文本参数")
	}

	for _, char := range text {
		// 简化处理：只处理ASCII字符
		if char < 128 {
			vkCode := uint16(char)
			if char >= 'a' && char <= 'z' {
				vkCode = uint16(char - 'a' + 'A') // 转换为大写
			}

			prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
			time.Sleep(20 * time.Millisecond)
			prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYUP), 0)
			time.Sleep(20 * time.Millisecond)
		}
	}

	return fmt.Sprintf("输入了文本: %s", text), nil
}

// 屏幕解锁
func handleUnlockScreen(params map[string]interface{}) (string, error) {
	password, ok := params["password"].(string)
	if !ok {
		return "", fmt.Errorf("缺少密码参数")
	}

	// 方法1: 尝试唤醒屏幕
	prockeybd_event.Call(uintptr(VK_SPACE), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
	prockeybd_event.Call(uintptr(VK_SPACE), 0, uintptr(KEYEVENTF_KEYUP), 0)
	time.Sleep(1 * time.Second)

	// 方法2: 输入密码
	for _, char := range password {
		if char < 128 {
			vkCode := uint16(char)
			if char >= 'a' && char <= 'z' {
				vkCode = uint16(char - 'a' + 'A')
			}

			prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
			time.Sleep(50 * time.Millisecond)
			prockeybd_event.Call(uintptr(vkCode), 0, uintptr(KEYEVENTF_KEYUP), 0)
			time.Sleep(50 * time.Millisecond)
		}
	}

	// 按回车
	prockeybd_event.Call(uintptr(VK_RETURN), 0, uintptr(KEYEVENTF_KEYDOWN), 0)
	time.Sleep(50 * time.Millisecond)
	prockeybd_event.Call(uintptr(VK_RETURN), 0, uintptr(KEYEVENTF_KEYUP), 0)

	return "尝试解锁屏幕", nil
}

// 获取鼠标位置
func handleGetCursorPos() (string, error) {
	var point POINT
	ret, _, _ := procGetCursorPos.Call(uintptr(unsafe.Pointer(&point)))
	if ret == 0 {
		return "", fmt.Errorf("获取鼠标位置失败")
	}

	result := map[string]int32{
		"x": point.X,
		"y": point.Y,
	}

	jsonData, _ := json.Marshal(result)
	return string(jsonData), nil
}

// 获取屏幕尺寸
func handleGetScreenSize() (string, error) {
	width, _, _ := procGetSystemMetrics.Call(0)  // SM_CXSCREEN
	height, _, _ := procGetSystemMetrics.Call(1) // SM_CYSCREEN

	result := map[string]int{
		"width":  int(width),
		"height": int(height),
	}

	jsonData, _ := json.Marshal(result)
	return string(jsonData), nil
}

// 虚拟键码映射
func getVirtualKeyCode(key string) uint16 {
	keyMap := map[string]uint16{
		"enter":   VK_RETURN,
		"space":   VK_SPACE,
		"escape":  VK_ESCAPE,
		"tab":     VK_TAB,
		"ctrl":    VK_CONTROL,
		"alt":     VK_ALT,
		"shift":   VK_SHIFT,
		"win":     VK_LWIN,
		"f1":      0x70,
		"f2":      0x71,
		"f3":      0x72,
		"f4":      0x73,
		"f5":      0x74,
		"f6":      0x75,
		"f7":      0x76,
		"f8":      0x77,
		"f9":      0x78,
		"f10":     0x79,
		"f11":     0x7A,
		"f12":     0x7B,
	}

	if vk, exists := keyMap[strings.ToLower(key)]; exists {
		return vk
	}

	// 处理单个字符
	if len(key) == 1 {
		char := key[0]
		if char >= 'A' && char <= 'Z' {
			return uint16(char)
		}
		if char >= 'a' && char <= 'z' {
			return uint16(char - 'a' + 'A')
		}
		if char >= '0' && char <= '9' {
			return uint16(char)
		}
	}

	return 0
}
