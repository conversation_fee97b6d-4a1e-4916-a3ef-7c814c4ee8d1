//go:build windows
// +build windows

// commands/autoruns/autoruns_windows.go
package autoruns

import (
	"bytes"
	"fmt"
	"io"
	"os/exec"
	"strings"

	"golang.org/x/sys/windows" // 导入核心 windows 包
	"golang.org/x/sys/windows/svc"
	"golang.org/x/sys/windows/svc/mgr"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// QueryServices 查询 Windows 服务
func QueryServices() (string, error) {
	m, err := mgr.Connect()
	if err != nil {
		return "", fmt.Errorf("无法连接到服务管理器: %w", err)
	}
	defer m.Disconnect()

	serviceNames, err := m.ListServices()
	if err != nil {
		return "", fmt.Errorf("无法列出服务: %w", err)
	}

	var b strings.Builder
	fmt.Fprintf(&b, "--- Windows 服务 ---\n")
	fmt.Fprintf(&b, "%-35s %-40s %-10s %s\n", "服务名 (Name)", "显示名 (Display)", "状态 (State)", "启动类型 (StartType)")
	fmt.Fprintln(&b, strings.Repeat("-", 120))

	for _, name := range serviceNames {
		s, err := m.OpenService(name)
		if err != nil {
			continue
		}

		conf, err := s.Config()
		if err != nil {
			s.Close()
			continue
		}

		status, err := s.Query()
		if err != nil {
			s.Close()
			continue
		}

		fmt.Fprintf(&b, "%-35s %-40s %-10s %s\n",
			truncateString(name, 35),
			truncateString(conf.DisplayName, 40),
			getServiceState(status.State),
			getServiceStartType(conf.StartType),
		)
		s.Close()
	}

	return b.String(), nil
}

// QueryScheduledTasks (保持不变)
func QueryScheduledTasks() (string, error) {
	cmd := exec.Command("schtasks", "/query", "/fo", "CSV", "/nh")
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr
	if err := cmd.Run(); err != nil {
		return "", fmt.Errorf("执行 schtasks 失败: %w, %s", err, stderr.String())
	}
	utf8, err := io.ReadAll(transform.NewReader(&out, simplifiedchinese.GBK.NewDecoder()))
	if err != nil {
		return out.String(), nil
	}
	var b strings.Builder
	fmt.Fprintf(&b, "--- 计划任务 ---\n")
	fmt.Fprintf(&b, "%-40s %-25s %s\n", "任务名 (TaskName)", "下次运行时间 (Next Run)", "状态 (Status)")
	fmt.Fprintln(&b, strings.Repeat("-", 80))
	lines := strings.Split(string(utf8), "\n")
	for _, line := range lines {
		parts := strings.Split(strings.Trim(line, "\r "), `","`)
		if len(parts) >= 3 {
			fmt.Fprintf(&b, "%-40s %-25s %s\n", truncateString(strings.Trim(parts[0], `"`), 40), strings.Trim(parts[1], `"`), strings.Trim(parts[2], `"`))
		}
	}
	return b.String(), nil
}

// --- 辅助函数 ---
func getServiceState(state svc.State) string {
	switch state {
	case svc.Stopped:
		return "Stopped"
	case svc.StartPending:
		return "Starting"
	case svc.StopPending:
		return "Stopping"
	case svc.Running:
		return "Running"
	case svc.ContinuePending:
		return "Continuing"
	case svc.PausePending:
		return "Pausing"
	case svc.Paused:
		return "Paused"
	default:
		return "Unknown"
	}
}

// [最终修复版] getServiceStartType
func getServiceStartType(startType uint32) string {
	switch startType {
	case windows.SERVICE_AUTO_START:
		return "Auto"
	case windows.SERVICE_BOOT_START:
		return "Boot"
	case windows.SERVICE_DEMAND_START:
		return "Manual"
	case windows.SERVICE_DISABLED:
		return "Disabled"
	case windows.SERVICE_SYSTEM_START:
		return "System"
	default:
		return "Unknown"
	}
}

func truncateString(s string, maxLen int) string {
	if len(s) > maxLen {
		return s[:maxLen-3] + "..."
	}
	return s
}
