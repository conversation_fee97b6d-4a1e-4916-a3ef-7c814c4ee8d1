//go:build !windows
// +build !windows

// commands/autoruns/autoruns_other.go
package autoruns

// CmdAutoruns 在非Windows平台返回不支持信息
func CmdAutoruns() (string, error) {
	return "autoruns命令仅在Windows平台支持", nil
}

// QueryServices 在非Windows平台返回不支持信息
func QueryServices() (string, error) {
	return "服务查询仅在Windows平台支持", nil
}

// QueryScheduledTasks 在非Windows平台返回不支持信息
func QueryScheduledTasks() (string, error) {
	return "计划任务查询仅在Windows平台支持", nil
}
