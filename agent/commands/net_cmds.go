//go:build windows
// +build windows

// xxx/commands/net_cmds.go
package commands

import (
	"fmt"
	"net"
	"strings"
	"syscall"
	"unsafe"

	gops_net "github.com/shirou/gopsutil/v3/net"
	gops_proc "github.com/shirou/gopsutil/v3/process"
	"golang.org/x/sys/windows"
)

// --- 为兼容或未导出的API，手动定义结构体 ---

// ipNetTable 结构体布局必须与Windows API文档中的 MIB_IPNETTABLE 完全一致
type ipNetTable struct {
	NumEntries uint32
	Table      [1]ipNetRow // 使用[1]作为占位符，因为数组的实际大小是动态的
}

// ipNetRow 结构体布局必须与 MIB_IPNETROW 完全一致
type ipNetRow struct {
	Index       uint32
	PhysAddrLen uint32
	PhysAddr    [8]byte // 在Windows中，MAX_PHYS_ADDR_LEN 是 8
	Addr        [4]byte // IPv4地址是4字节 (uint32)
	Type        uint32
}

// --- 核心命令实现 ---

// CmdNetstat 使用 gopsutil 获取网络连接信息
func CmdNetstat() (string, error) {
	// "all" 表示获取所有协议 (tcp, udp) 的连接
	conns, err := gops_net.Connections("all")
	if err != nil {
		return "", fmt.Errorf("无法获取网络连接列表: %w", err)
	}

	var b strings.Builder
	fmt.Fprintf(&b, "--- 网络连接 (Netstat) ---\n")
	fmt.Fprintf(&b, "%-8s %-25s %-25s %-15s %s\n", "协议", "本地地址", "远程地址", "状态", "PID/进程名")
	fmt.Fprintln(&b, strings.Repeat("-", 100))

	// 创建一个PID到进程名的缓存，避免在循环中重复查询，提高效率
	procCache := make(map[int32]string)

	for _, c := range conns {
		// 过滤掉无用的LISTEN状态，让输出更聚焦于活动连接
		if c.Status == "LISTEN" {
			continue
		}

		localAddr := fmt.Sprintf("%s:%d", c.Laddr.IP, c.Laddr.Port)
		remoteAddr := "0.0.0.0:0"
		if len(c.Raddr.IP) > 0 {
			remoteAddr = fmt.Sprintf("%s:%d", c.Raddr.IP, c.Raddr.Port)
		}

		pid := c.Pid
		procName := ""
		if pid > 0 {
			if name, ok := procCache[pid]; ok {
				procName = name
			} else {
				if p, err := gops_proc.NewProcess(pid); err == nil {
					if name, err := p.Name(); err == nil {
						procName = name
						procCache[pid] = name
					}
				}
			}
		}

		fmt.Fprintf(&b, "%-8s %-25s %-25s %-15s %d/%s\n",
			getProtoName(c.Type),
			localAddr,
			remoteAddr,
			c.Status,
			pid,
			procName,
		)
	}
	return b.String(), nil
}

// CmdIpconfig 使用 gopsutil 获取所有网卡信息 (修复版)
func CmdIpconfig() (string, error) {
	interfaces, err := gops_net.Interfaces()
	if err != nil {
		return "", fmt.Errorf("无法获取网络接口列表: %w", err)
	}

	var b strings.Builder
	fmt.Fprintf(&b, "--- 网卡信息 (ipconfig) ---\n")

	for _, i := range interfaces {
		// ★★★ 核心修复点：检查 i.Flags 是否包含 "up" 字符串 ★★★
		var isUp bool
		for _, flag := range i.Flags {
			if flag == "up" {
				isUp = true
				break
			}
		}

		// 过滤掉没有IP地址或不活跃的网卡
		if len(i.Addrs) == 0 || !isUp {
			continue
		}

		fmt.Fprintf(&b, "\n[+] %s (Flags: %s)\n", i.Name, strings.Join(i.Flags, ","))
		if i.HardwareAddr != "" {
			fmt.Fprintf(&b, "    MAC地址: %s\n", i.HardwareAddr)
		}

		for _, addr := range i.Addrs {
			fmt.Fprintf(&b, "    地址: %s\n", addr.Addr)
		}
	}
	return b.String(), nil
}

// CmdArp 使用原生Windows API查询ARP表，实现无Shell操作 (修复版)
func CmdArp() (string, error) {
	// 加载 iphlpapi.dll 并获取 GetIpNetTable 函数地址
	iphlpapi := windows.NewLazySystemDLL("iphlpapi.dll")
	procGetIpNetTable := iphlpapi.NewProc("GetIpNetTable")

	var bufSize uint32
	// 第一次调用，获取需要的缓冲区大小
	ret, _, _ := procGetIpNetTable.Call(0, uintptr(unsafe.Pointer(&bufSize)), 1)
	if ret != uintptr(windows.ERROR_INSUFFICIENT_BUFFER) {
		return "ARP缓存为空或无法获取。", nil
	}
	if bufSize == 0 {
		return "ARP缓存为空。", nil
	}

	// 分配缓冲区
	buf := make([]byte, bufSize)

	// 第二次调用，获取ARP表数据
	ret, _, err := procGetIpNetTable.Call(uintptr(unsafe.Pointer(&buf[0])), uintptr(unsafe.Pointer(&bufSize)), 1)
	if ret != 0 {
		return "", fmt.Errorf("获取ARP表数据失败: %v", err)
	}

	// ★★★ 核心修复点：使用我们自己定义的结构体 ★★★
	table := (*ipNetTable)(unsafe.Pointer(&buf[0]))

	var b strings.Builder
	fmt.Fprintf(&b, "--- ARP 缓存表 ---\n")
	fmt.Fprintf(&b, "%-20s %-20s %s\n", "Internet 地址", "物理地址 (MAC)", "类型")
	fmt.Fprintln(&b, strings.Repeat("-", 60))

	rowPtr := uintptr(unsafe.Pointer(&table.Table[0]))
	rowSize := unsafe.Sizeof(table.Table[0])

	for i := 0; i < int(table.NumEntries); i++ {
		row := (*ipNetRow)(unsafe.Pointer(rowPtr + uintptr(i)*rowSize))

		ip := net.IP(row.Addr[:]).String()
		mac := net.HardwareAddr(row.PhysAddr[:row.PhysAddrLen]).String()
		arpType := getArpType(row.Type)

		fmt.Fprintf(&b, "%-20s %-20s %s\n", ip, mac, arpType)
	}
	return b.String(), nil
}

// --- 辅助函数 ---

func getArpType(arpType uint32) string {
	switch arpType {
	case 2:
		return "Invalid (无效)"
	case 3:
		return "Dynamic (动态)"
	case 4:
		return "Static (静态)"
	default:
		return "Other (其他)"
	}
}

func getProtoName(proto uint32) string {
	switch proto {
	case syscall.SOCK_STREAM:
		return "tcp"
	case syscall.SOCK_DGRAM:
		return "udp"
	case windows.AF_INET6:
		return "tcp6/udp6"
	default:
		return "unknown"
	}
}
