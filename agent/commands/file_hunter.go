//go:build !windows
// +build !windows

package commands

import (
	"bufio"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// 敏感文件搜索模块

// FileMatch 文件匹配结果
type FileMatch struct {
	FilePath    string    `json:"file_path"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	ModTime     time.Time `json:"mod_time"`
	MatchType   string    `json:"match_type"`
	MatchReason string    `json:"match_reason"`
	Content     string    `json:"content,omitempty"`
}

// SearchConfig 搜索配置
type SearchConfig struct {
	SearchPaths     []string
	FilePatterns    []string
	ContentPatterns []string
	MaxFileSize     int64
	MaxResults      int
	IncludeContent  bool
	SearchHidden    bool
}

// 预定义的敏感文件模式
var (
	// 凭据和密钥文件
	credentialPatterns = []string{
		`.*\.pem$`, `.*\.key$`, `.*\.p12$`, `.*\.pfx$`, `.*\.crt$`, `.*\.cer$`,
		`.*id_rsa.*`, `.*id_dsa.*`, `.*id_ecdsa.*`, `.*id_ed25519.*`,
		`.*\.ssh.*`, `.*known_hosts.*`, `.*authorized_keys.*`,
		`.*\.aws.*`, `.*credentials.*`, `.*\.boto.*`,
		`.*\.netrc.*`, `.*\.pgpass.*`, `.*\.my\.cnf.*`,
	}
	
	// 配置文件
	configPatterns = []string{
		`.*config\..*`, `.*\.conf$`, `.*\.cfg$`, `.*\.ini$`,
		`.*\.properties$`, `.*\.yaml$`, `.*\.yml$`, `.*\.json$`,
		`.*\.xml$`, `.*\.toml$`, `.*\.env$`, `.*\.local$`,
	}
	
	// 数据库文件
	databasePatterns = []string{
		`.*\.db$`, `.*\.sqlite.*`, `.*\.mdb$`, `.*\.accdb$`,
		`.*\.dbf$`, `.*\.sql$`, `.*\.dump$`, `.*\.backup$`,
	}
	
	// 文档文件
	documentPatterns = []string{
		`.*password.*\.txt$`, `.*secret.*\.txt$`, `.*private.*\.txt$`,
		`.*\.doc$`, `.*\.docx$`, `.*\.pdf$`, `.*\.xls$`, `.*\.xlsx$`,
		`.*readme.*`, `.*todo.*`, `.*note.*`, `.*diary.*`,
	}
	
	// 备份和日志文件
	backupPatterns = []string{
		`.*\.bak$`, `.*\.backup$`, `.*\.old$`, `.*\.orig$`,
		`.*\.log$`, `.*\.logs$`, `.*\.out$`, `.*\.err$`,
		`.*\.tmp$`, `.*\.temp$`, `.*\.swp$`, `.*\.swo$`,
	}
)

// 敏感内容模式
var sensitiveContentPatterns = []string{
	`password\s*[=:]\s*[^\s\n]+`,
	`passwd\s*[=:]\s*[^\s\n]+`,
	`secret\s*[=:]\s*[^\s\n]+`,
	`api[_-]?key\s*[=:]\s*[^\s\n]+`,
	`access[_-]?token\s*[=:]\s*[^\s\n]+`,
	`private[_-]?key\s*[=:]\s*[^\s\n]+`,
	`-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----`,
	`-----BEGIN\s+CERTIFICATE-----`,
	`mysql://.*:.*@`,
	`postgresql://.*:.*@`,
	`mongodb://.*:.*@`,
	`redis://.*:.*@`,
	`ftp://.*:.*@`,
	`ssh://.*:.*@`,
	`[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`,
	`\b(?:\d{1,3}\.){3}\d{1,3}\b`,
	`[0-9a-fA-F]{32,}`,
}

// CmdFileHunt 敏感文件搜索命令
func CmdFileHunt(searchType string) (string, error) {
	var config SearchConfig
	
	switch strings.ToLower(searchType) {
	case "credentials", "creds":
		config = SearchConfig{
			SearchPaths:     getDefaultSearchPaths(),
			FilePatterns:    credentialPatterns,
			ContentPatterns: []string{`password`, `secret`, `key`, `token`},
			MaxFileSize:     1024 * 1024, // 1MB
			MaxResults:      100,
			IncludeContent:  false,
			SearchHidden:    true,
		}
	case "config":
		config = SearchConfig{
			SearchPaths:     getDefaultSearchPaths(),
			FilePatterns:    configPatterns,
			ContentPatterns: sensitiveContentPatterns,
			MaxFileSize:     1024 * 1024, // 1MB
			MaxResults:      50,
			IncludeContent:  true,
			SearchHidden:    false,
		}
	case "database", "db":
		config = SearchConfig{
			SearchPaths:     getDefaultSearchPaths(),
			FilePatterns:    databasePatterns,
			ContentPatterns: []string{},
			MaxFileSize:     10 * 1024 * 1024, // 10MB
			MaxResults:      30,
			IncludeContent:  false,
			SearchHidden:    false,
		}
	case "documents", "docs":
		config = SearchConfig{
			SearchPaths:     getDocumentSearchPaths(),
			FilePatterns:    documentPatterns,
			ContentPatterns: sensitiveContentPatterns,
			MaxFileSize:     5 * 1024 * 1024, // 5MB
			MaxResults:      50,
			IncludeContent:  true,
			SearchHidden:    false,
		}
	case "backup":
		config = SearchConfig{
			SearchPaths:     getDefaultSearchPaths(),
			FilePatterns:    backupPatterns,
			ContentPatterns: []string{},
			MaxFileSize:     50 * 1024 * 1024, // 50MB
			MaxResults:      30,
			IncludeContent:  false,
			SearchHidden:    true,
		}
	case "all":
		allPatterns := append(credentialPatterns, configPatterns...)
		allPatterns = append(allPatterns, databasePatterns...)
		allPatterns = append(allPatterns, documentPatterns...)
		allPatterns = append(allPatterns, backupPatterns...)
		
		config = SearchConfig{
			SearchPaths:     getDefaultSearchPaths(),
			FilePatterns:    allPatterns,
			ContentPatterns: sensitiveContentPatterns,
			MaxFileSize:     5 * 1024 * 1024, // 5MB
			MaxResults:      200,
			IncludeContent:  false,
			SearchHidden:    true,
		}
	default:
		return "", fmt.Errorf("未知搜索类型: %s。支持的类型: credentials, config, database, documents, backup, all", searchType)
	}
	
	matches, err := searchSensitiveFiles(config)
	if err != nil {
		return "", fmt.Errorf("文件搜索失败: %v", err)
	}
	
	return formatSearchResults(matches, searchType), nil
}

// searchSensitiveFiles 搜索敏感文件
func searchSensitiveFiles(config SearchConfig) ([]FileMatch, error) {
	var matches []FileMatch
	matchCount := 0
	
	// 编译正则表达式
	fileRegexes := make([]*regexp.Regexp, len(config.FilePatterns))
	for i, pattern := range config.FilePatterns {
		regex, err := regexp.Compile(`(?i)` + pattern)
		if err != nil {
			continue
		}
		fileRegexes[i] = regex
	}
	
	contentRegexes := make([]*regexp.Regexp, len(config.ContentPatterns))
	for i, pattern := range config.ContentPatterns {
		regex, err := regexp.Compile(`(?i)` + pattern)
		if err != nil {
			continue
		}
		contentRegexes[i] = regex
	}
	
	// 搜索每个路径
	for _, searchPath := range config.SearchPaths {
		if matchCount >= config.MaxResults {
			break
		}
		
		err := filepath.WalkDir(searchPath, func(path string, d fs.DirEntry, err error) error {
			if err != nil {
				return nil // 忽略错误，继续搜索
			}
			
			if matchCount >= config.MaxResults {
				return filepath.SkipDir
			}
			
			// 跳过目录
			if d.IsDir() {
				// 跳过隐藏目录（除非配置允许）
				if !config.SearchHidden && strings.HasPrefix(d.Name(), ".") {
					return filepath.SkipDir
				}
				return nil
			}
			
			// 跳过隐藏文件（除非配置允许）
			if !config.SearchHidden && strings.HasPrefix(d.Name(), ".") {
				return nil
			}
			
			// 获取文件信息
			info, err := d.Info()
			if err != nil {
				return nil
			}
			
			// 检查文件大小
			if info.Size() > config.MaxFileSize {
				return nil
			}
			
			// 检查文件名模式
			fileName := strings.ToLower(d.Name())
			var matchReason string
			var matchType string
			
			for i, regex := range fileRegexes {
				if regex != nil && regex.MatchString(fileName) {
					matchType = "文件名匹配"
					matchReason = config.FilePatterns[i]
					break
				}
			}
			
			// 如果文件名匹配或需要检查内容
			if matchReason != "" || len(contentRegexes) > 0 {
				match := FileMatch{
					FilePath:    path,
					FileName:    d.Name(),
					FileSize:    info.Size(),
					ModTime:     info.ModTime(),
					MatchType:   matchType,
					MatchReason: matchReason,
				}
				
				// 检查文件内容
				if len(contentRegexes) > 0 && info.Size() < 1024*1024 { // 只检查小于1MB的文件
					contentMatch, content := checkFileContent(path, contentRegexes)
					if contentMatch != "" {
						if matchReason == "" {
							match.MatchType = "内容匹配"
							match.MatchReason = contentMatch
						} else {
							match.MatchReason += " + 内容匹配"
						}
						
						if config.IncludeContent {
							match.Content = content
						}
					}
				}
				
				// 如果有匹配，添加到结果
				if match.MatchReason != "" {
					matches = append(matches, match)
					matchCount++
				}
			}
			
			return nil
		})
		
		if err != nil {
			// 记录错误但继续搜索其他路径
			continue
		}
	}
	
	return matches, nil
}

// checkFileContent 检查文件内容
func checkFileContent(filePath string, regexes []*regexp.Regexp) (string, string) {
	file, err := os.Open(filePath)
	if err != nil {
		return "", ""
	}
	defer file.Close()
	
	scanner := bufio.NewScanner(file)
	var contentBuilder strings.Builder
	lineCount := 0
	
	for scanner.Scan() && lineCount < 100 { // 只读取前100行
		line := scanner.Text()
		lineCount++
		
		for i, regex := range regexes {
			if regex != nil && regex.MatchString(line) {
				contentBuilder.WriteString(fmt.Sprintf("Line %d: %s\n", lineCount, line))
				return fmt.Sprintf("Pattern %d", i), contentBuilder.String()
			}
		}
	}
	
	return "", ""
}

// getDefaultSearchPaths 获取默认搜索路径
func getDefaultSearchPaths() []string {
	userProfile := os.Getenv("USERPROFILE")
	programFiles := os.Getenv("PROGRAMFILES")
	programFilesX86 := os.Getenv("PROGRAMFILES(X86)")
	
	paths := []string{
		userProfile,
		filepath.Join(userProfile, "Desktop"),
		filepath.Join(userProfile, "Documents"),
		filepath.Join(userProfile, "Downloads"),
		filepath.Join(userProfile, ".ssh"),
		filepath.Join(userProfile, ".aws"),
		filepath.Join(userProfile, "AppData", "Local"),
		filepath.Join(userProfile, "AppData", "Roaming"),
		"C:\\Windows\\System32\\config",
		"C:\\ProgramData",
	}
	
	if programFiles != "" {
		paths = append(paths, programFiles)
	}
	if programFilesX86 != "" {
		paths = append(paths, programFilesX86)
	}
	
	return paths
}

// getDocumentSearchPaths 获取文档搜索路径
func getDocumentSearchPaths() []string {
	userProfile := os.Getenv("USERPROFILE")
	
	return []string{
		filepath.Join(userProfile, "Desktop"),
		filepath.Join(userProfile, "Documents"),
		filepath.Join(userProfile, "Downloads"),
		filepath.Join(userProfile, "OneDrive"),
		filepath.Join(userProfile, "Dropbox"),
		filepath.Join(userProfile, "Google Drive"),
	}
}

// formatSearchResults 格式化搜索结果
func formatSearchResults(matches []FileMatch, searchType string) string {
	var result strings.Builder
	result.WriteString(fmt.Sprintf("=== 敏感文件搜索报告 (%s) ===\n\n", searchType))
	
	if len(matches) == 0 {
		result.WriteString("未找到匹配的敏感文件\n")
		return result.String()
	}
	
	result.WriteString(fmt.Sprintf("找到 %d 个敏感文件:\n\n", len(matches)))
	
	// 按匹配类型分组
	byType := make(map[string][]FileMatch)
	for _, match := range matches {
		byType[match.MatchType] = append(byType[match.MatchType], match)
	}
	
	for matchType, typeMatches := range byType {
		result.WriteString(fmt.Sprintf("=== %s (%d个) ===\n", matchType, len(typeMatches)))
		
		for i, match := range typeMatches {
			result.WriteString(fmt.Sprintf("\n--- 文件 %d ---\n", i+1))
			result.WriteString(fmt.Sprintf("路径: %s\n", match.FilePath))
			result.WriteString(fmt.Sprintf("大小: %d bytes\n", match.FileSize))
			result.WriteString(fmt.Sprintf("修改时间: %s\n", match.ModTime.Format("2006-01-02 15:04:05")))
			result.WriteString(fmt.Sprintf("匹配原因: %s\n", match.MatchReason))
			
			if match.Content != "" {
				result.WriteString("匹配内容:\n")
				result.WriteString(match.Content)
				result.WriteString("\n")
			}
		}
		result.WriteString("\n")
	}
	
	return result.String()
}

// CmdFileHuntCustom 自定义文件搜索
func CmdFileHuntCustom(pattern, searchPath string) (string, error) {
	if pattern == "" {
		return "", fmt.Errorf("搜索模式不能为空")
	}
	
	if searchPath == "" {
		searchPath = os.Getenv("USERPROFILE")
	}
	
	config := SearchConfig{
		SearchPaths:     []string{searchPath},
		FilePatterns:    []string{pattern},
		ContentPatterns: []string{},
		MaxFileSize:     10 * 1024 * 1024, // 10MB
		MaxResults:      100,
		IncludeContent:  false,
		SearchHidden:    true,
	}
	
	matches, err := searchSensitiveFiles(config)
	if err != nil {
		return "", err
	}
	
	return formatSearchResults(matches, fmt.Sprintf("自定义搜索: %s", pattern)), nil
}
