package path

import (
	"os"
	"path/filepath"
)

// GetHiddenStoragePath 返回一个用于隐蔽存储的目录路径。
// 它会尝试在 %LOCALAPPDATA% 下创建一个看起来无害的目录。
func GetHiddenStoragePath() (string, error) {
	// os.UserCacheDir() 在 Windows 上通常返回 %LOCALAPPDATA%
	// 在其他系统上也有对应的缓存目录，兼容性好。
	cacheDir, err := os.UserCacheDir()
	if err != nil {
		// 如果获取失败，回退到用户主目录下的一个隐藏文件夹
		home, err_home := os.UserHomeDir()
		if err_home != nil {
			return "", err_home // 如果连主目录都拿不到，就真的出错了
		}
		cacheDir = home
	}

	// 创建一个看起来像系统或驱动更新的目录名
	storageDir := filepath.Join(cacheDir, "SystemUpdater", "Logs")

	// 确保这个目录存在，如果不存在就创建它
	// os.MkdirAll 会创建所有必需的父目录，且如果目录已存在也不会报错。
	if err := os.MkdirAll(storageDir, 0700); err != nil {
		return "", err
	}

	return storageDir, nil
}
