//go:build windows
// +build windows

package RDp

import (
	"fmt"
	"strings"

	"golang.org/x/sys/windows/registry"
)

// 更隐蔽的RDP信息获取方案，避免使用wevtutil

// 暂时注释掉Windows Event Log API相关代码
// var (
// 	// Windows Event Log API
// 	wevtapi                = windows.NewLazySystemDLL("wevtapi.dll")
// 	procEvtOpenLog         = wevtapi.NewProc("EvtOpenLog")
// 	procEvtQuery           = wevtapi.NewProc("EvtQuery")
// 	procEvtNext            = wevtapi.NewProc("EvtNext")
// 	procEvtRender          = wevtapi.NewProc("EvtRender")
// 	procEvtClose           = wevtapi.NewProc("EvtClose")
// 	procEvtCreateRenderContext = wevtapi.NewProc("EvtCreateRenderContext")
// )

// Event Log 常量
const (
	EvtQueryChannelPath     = 0x1
	EvtQueryFilePath        = 0x2
	EvtQueryForwardDirection = 0x100
	EvtQueryReverseDirection = 0x200
	
	EvtRenderEventXml    = 1
	EvtRenderEventValues = 2
	
	ERROR_NO_MORE_ITEMS = 259
)

// StealthRdpInfo 隐蔽获取RDP信息
type StealthRdpInfo struct {
	SuccessEvents map[string]Info
	FailedEvents  map[string]Info
}

// GetRdpInfoStealth 使用更隐蔽的方式获取RDP信息
func GetRdpInfoStealth() (*StealthRdpInfo, error) {
	info := &StealthRdpInfo{
		SuccessEvents: make(map[string]Info),
		FailedEvents:  make(map[string]Info),
	}
	
	// 方案1: 直接读取注册表中的RDP连接历史
	if err := info.getRegistryRdpHistory(); err == nil {
		// 注册表方式成功，优先使用
		return info, nil
	}
	
	// 方案2: 使用Windows Event Log API (更隐蔽)
	if err := info.getEventLogRdpHistory(); err == nil {
		return info, nil
	}
	
	// 方案3: 读取系统文件中的连接记录
	if err := info.getFileBasedRdpHistory(); err == nil {
		return info, nil
	}
	
	return info, fmt.Errorf("所有隐蔽方式都失败")
}

// getRegistryRdpHistory 从注册表获取RDP历史（最隐蔽）
func (sri *StealthRdpInfo) getRegistryRdpHistory() error {
	// 1. 获取RDP客户端连接历史
	if err := sri.getClientConnectionHistory(); err != nil {
		return err
	}
	
	// 2. 获取RDP服务器连接历史
	if err := sri.getServerConnectionHistory(); err != nil {
		return err
	}
	
	return nil
}

// getClientConnectionHistory 获取作为客户端的连接历史
func (sri *StealthRdpInfo) getClientConnectionHistory() error {
	// 遍历所有用户的RDP客户端历史
	usersKey, err := registry.OpenKey(registry.USERS, "", registry.READ)
	if err != nil {
		return err
	}
	defer usersKey.Close()
	
	subkeys, err := usersKey.ReadSubKeyNames(0)
	if err != nil {
		return err
	}
	
	for _, sid := range subkeys {
		if !strings.HasPrefix(sid, "S-1-5-21-") {
			continue // 跳过非用户SID
		}
		
		// 读取Terminal Server Client历史
		clientPath := sid + `\Software\Microsoft\Terminal Server Client\Default`
		clientKey, err := registry.OpenKey(registry.USERS, clientPath, registry.READ)
		if err != nil {
			continue
		}
		
		names, err := clientKey.ReadValueNames(0)
		if err != nil {
			clientKey.Close()
			continue
		}
		
		for _, name := range names {
			if value, _, err := clientKey.GetStringValue(name); err == nil {
				// 解析连接信息
				addr := value
				if strings.Contains(value, ":") {
					parts := strings.SplitN(value, ":", 2)
					addr = parts[0]
				}
				
				key := addr + "\t" + "outbound_" + sid[:10]
				sri.SuccessEvents[key] = Info{
					Num:      1,
					LastTime: "Registry_Based",
				}
			}
		}
		clientKey.Close()
		
		// 读取服务器历史
		serverPath := sid + `\Software\Microsoft\Terminal Server Client\Servers`
		sri.readServerEntries(serverPath)
	}
	
	return nil
}

// readServerEntries 读取服务器条目
func (sri *StealthRdpInfo) readServerEntries(basePath string) {
	serverKey, err := registry.OpenKey(registry.USERS, basePath, registry.READ)
	if err != nil {
		return
	}
	defer serverKey.Close()
	
	serverNames, err := serverKey.ReadSubKeyNames(0)
	if err != nil {
		return
	}
	
	for _, serverName := range serverNames {
		serverPath := basePath + `\` + serverName
		subKey, err := registry.OpenKey(registry.USERS, serverPath, registry.READ)
		if err != nil {
			continue
		}
		
		// 读取用户名
		username := "unknown"
		if val, _, err := subKey.GetStringValue("UsernameHint"); err == nil {
			username = val
		}
		
		key := serverName + "\t" + username
		sri.SuccessEvents[key] = Info{
			Num:      1,
			LastTime: "Registry_Server",
		}
		
		subKey.Close()
	}
}

// getServerConnectionHistory 获取作为服务器的连接历史
func (sri *StealthRdpInfo) getServerConnectionHistory() error {
	// 读取RDP服务器日志相关注册表项
	// 这些信息通常存储在系统注册表中
	
	// 1. 检查RDP服务配置
	rdpPath := `SYSTEM\CurrentControlSet\Control\Terminal Server`
	rdpKey, err := registry.OpenKey(registry.LOCAL_MACHINE, rdpPath, registry.READ)
	if err != nil {
		return err
	}
	defer rdpKey.Close()
	
	// 检查RDP是否启用
	if enabled, _, err := rdpKey.GetIntegerValue("fDenyTSConnections"); err == nil {
		if enabled == 0 {
			// RDP已启用，记录这个信息
			sri.SuccessEvents["localhost\trdp_enabled"] = Info{
				Num:      1,
				LastTime: "Registry_Config",
			}
		}
	}
	
	return nil
}

// getEventLogRdpHistory 使用Windows Event Log API获取历史
func (sri *StealthRdpInfo) getEventLogRdpHistory() error {
	// 这里实现直接调用Windows Event Log API
	// 比wevtutil更隐蔽，但实现复杂
	
	// 暂时返回错误，让系统使用其他方法
	return fmt.Errorf("Event Log API方式暂未实现")
}

// getFileBasedRdpHistory 从文件系统获取RDP历史
func (sri *StealthRdpInfo) getFileBasedRdpHistory() error {
	// 1. 检查RDP相关的临时文件
	// 2. 检查用户配置文件中的连接缓存
	// 3. 检查系统日志文件
	
	// 这是最后的备选方案
	return fmt.Errorf("文件系统方式暂未实现")
}

// GetRdpHistoryStealthMode 隐蔽模式获取RDP历史
func GetRdpHistoryStealthMode() string {
	var result strings.Builder
	result.WriteString("=== 隐蔽模式RDP信息收集 ===\n\n")
	
	info, err := GetRdpInfoStealth()
	if err != nil {
		result.WriteString(fmt.Sprintf("隐蔽获取失败: %v\n", err))
		return result.String()
	}
	
	result.WriteString("--- RDP连接历史 (注册表方式) ---\n")
	result.WriteString("目标地址\t\t用户\t\t来源\n")
	result.WriteString("----------------------------------------------\n")
	
	for key, info := range info.SuccessEvents {
		parts := strings.Split(key, "\t")
		if len(parts) >= 2 {
			addr, user := parts[0], parts[1]
			result.WriteString(fmt.Sprintf("%-20s\t%-15s\t%s\n", addr, user, info.LastTime))
		}
	}
	
	if len(info.FailedEvents) > 0 {
		result.WriteString("\n--- 失败连接记录 ---\n")
		for key, info := range info.FailedEvents {
			parts := strings.Split(key, "\t")
			if len(parts) >= 2 {
				addr, user := parts[0], parts[1]
				result.WriteString(fmt.Sprintf("%-20s\t%-15s\t%s\n", addr, user, info.LastTime))
			}
		}
	}
	
	result.WriteString("\n[+] 隐蔽模式收集完成，未使用wevtutil\n")
	return result.String()
}

// 替换原有的parseEventLogs函数调用
func parseEventLogsStealth(logName, query string, count int) (map[string]Info, error) {
	// 首先尝试隐蔽方式
	info, err := GetRdpInfoStealth()
	if err == nil {
		if strings.Contains(logName, "LocalSessionManager") {
			return info.SuccessEvents, nil
		} else {
			return info.FailedEvents, nil
		}
	}
	
	// 如果隐蔽方式失败，回退到原有方式（但已经修复了黑窗口问题）
	return parseEventLogs(logName, query, count)
}
