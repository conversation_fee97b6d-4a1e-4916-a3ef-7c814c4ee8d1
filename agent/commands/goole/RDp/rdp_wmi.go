//go:build windows
// +build windows

package RDp

import (
	"fmt"
	"strings"
	"unsafe"

	"golang.org/x/sys/windows"
)

// WMI查询方式获取RDP信息，避免使用wevtutil

var (
	// COM/WMI相关API
	ole32               = windows.NewLazySystemDLL("ole32.dll")
	oleaut32            = windows.NewLazySystemDLL("oleaut32.dll")
	procCoInitialize    = ole32.NewProc("CoInitialize")
	procCoUninitialize  = ole32.NewProc("CoUninitialize")
	procCoCreateInstance = ole32.NewProc("CoCreateInstance")
	procSysAllocString  = oleaut32.NewProc("SysAllocString")
	procSysFreeString   = oleaut32.NewProc("SysFreeString")
)

// COM接口GUID
var (
	CLSID_WbemLocator = windows.GUID{
		Data1: 0x4590f811,
		Data2: 0x1d3a,
		Data3: 0x11d0,
		Data4: [8]byte{0x89, 0x1f, 0x00, 0xaa, 0x00, 0x4b, 0x2e, 0x24},
	}
	IID_IWbemLocator = windows.GUID{
		Data1: 0xdc12a687,
		Data2: 0x737f,
		Data3: 0x11cf,
		Data4: [8]byte{0x88, 0x4d, 0x00, 0xaa, 0x00, 0x4b, 0x2e, 0x24},
	}
)

// WMI查询结构
type WMIRdpInfo struct {
	Sessions []RdpSession
	Events   []RdpEvent
}

type RdpSession struct {
	SessionID   uint32
	UserName    string
	ClientName  string
	ClientIP    string
	ConnectTime string
	State       string
}

type RdpEvent struct {
	EventID     uint32
	TimeCreated string
	UserName    string
	ClientIP    string
	EventType   string
}

// GetRdpInfoViaWMI 通过WMI查询RDP信息
func GetRdpInfoViaWMI() (*WMIRdpInfo, error) {
	info := &WMIRdpInfo{
		Sessions: make([]RdpSession, 0),
		Events:   make([]RdpEvent, 0),
	}
	
	// 初始化COM
	ret, _, _ := procCoInitialize.Call(0)
	if ret != 0 && ret != 1 { // S_OK or S_FALSE
		return nil, fmt.Errorf("COM初始化失败: %x", ret)
	}
	defer procCoUninitialize.Call()
	
	// 查询当前RDP会话
	if err := info.queryCurrentSessions(); err != nil {
		// 如果WMI查询失败，尝试其他方式
		return info, err
	}
	
	// 查询RDP事件（通过WMI事件日志）
	if err := info.queryRdpEvents(); err != nil {
		// 事件查询失败不影响会话查询结果
	}
	
	return info, nil
}

// queryCurrentSessions 查询当前RDP会话
func (wmi *WMIRdpInfo) queryCurrentSessions() error {
	// 使用WMI查询Win32_LogonSession和Win32_LoggedOnUser
	// 这比直接查询事件日志更隐蔽
	
	// 简化实现：直接调用Windows Terminal Services API
	return wmi.querySessionsViaAPI()
}

// querySessionsViaAPI 通过Terminal Services API查询会话
func (wmi *WMIRdpInfo) querySessionsViaAPI() error {
	// 加载wtsapi32.dll
	wtsapi32 := windows.NewLazySystemDLL("wtsapi32.dll")
	procWTSEnumerateSessions := wtsapi32.NewProc("WTSEnumerateSessionsW")
	procWTSQuerySessionInformation := wtsapi32.NewProc("WTSQuerySessionInformationW")
	procWTSFreeMemory := wtsapi32.NewProc("WTSFreeMemory")
	
	var sessionInfo uintptr
	var sessionCount uint32
	
	// 枚举会话
	ret, _, _ := procWTSEnumerateSessions.Call(
		0, // WTS_CURRENT_SERVER_HANDLE
		0, // Reserved
		1, // Version
		uintptr(unsafe.Pointer(&sessionInfo)),
		uintptr(unsafe.Pointer(&sessionCount)),
	)
	
	if ret == 0 {
		return fmt.Errorf("WTSEnumerateSessions失败")
	}
	defer procWTSFreeMemory.Call(sessionInfo)
	
	// 解析会话信息
	sessionSize := unsafe.Sizeof(WTS_SESSION_INFO{})
	for i := uint32(0); i < sessionCount; i++ {
		sessionPtr := sessionInfo + uintptr(i)*sessionSize
		session := (*WTS_SESSION_INFO)(unsafe.Pointer(sessionPtr))
		
		// 只处理RDP会话
		if session.State == WTSActive || session.State == WTSDisconnected {
			rdpSession := RdpSession{
				SessionID: session.SessionId,
				State:     wmi.getSessionStateName(session.State),
			}
			
			// 查询会话详细信息
			wmi.querySessionDetails(session.SessionId, &rdpSession, procWTSQuerySessionInformation, procWTSFreeMemory)
			wmi.Sessions = append(wmi.Sessions, rdpSession)
		}
	}
	
	return nil
}

// WTS_SESSION_INFO 结构
type WTS_SESSION_INFO struct {
	SessionId      uint32
	pWinStationName *uint16
	State          uint32
}

// WTS会话状态常量
const (
	WTSActive       = 0
	WTSConnected    = 1
	WTSConnectQuery = 2
	WTSShadow       = 3
	WTSDisconnected = 4
	WTSIdle         = 5
	WTSListen       = 6
	WTSReset        = 7
	WTSDown         = 8
	WTSInit         = 9
)

// WTS信息类型
const (
	WTSUserName     = 5
	WTSClientName   = 10
	WTSClientAddress = 14
	WTSConnectTime  = 16
)

// querySessionDetails 查询会话详细信息
func (wmi *WMIRdpInfo) querySessionDetails(sessionID uint32, session *RdpSession, 
	procQuery, procFree *windows.LazyProc) {
	
	// 查询用户名
	if userName := wmi.querySessionInfo(sessionID, WTSUserName, procQuery, procFree); userName != "" {
		session.UserName = userName
	}
	
	// 查询客户端名称
	if clientName := wmi.querySessionInfo(sessionID, WTSClientName, procQuery, procFree); clientName != "" {
		session.ClientName = clientName
	}
	
	// 查询客户端IP（需要特殊处理）
	if clientIP := wmi.queryClientIP(sessionID, procQuery, procFree); clientIP != "" {
		session.ClientIP = clientIP
	}
}

// querySessionInfo 查询会话信息
func (wmi *WMIRdpInfo) querySessionInfo(sessionID uint32, infoType uint32, 
	procQuery, procFree *windows.LazyProc) string {
	
	var buffer uintptr
	var bytesReturned uint32
	
	ret, _, _ := procQuery.Call(
		0, // WTS_CURRENT_SERVER_HANDLE
		uintptr(sessionID),
		uintptr(infoType),
		uintptr(unsafe.Pointer(&buffer)),
		uintptr(unsafe.Pointer(&bytesReturned)),
	)
	
	if ret == 0 {
		return ""
	}
	defer procFree.Call(buffer)
	
	if buffer == 0 {
		return ""
	}
	
	// 转换为字符串
	return windows.UTF16PtrToString((*uint16)(unsafe.Pointer(buffer)))
}

// queryClientIP 查询客户端IP地址
func (wmi *WMIRdpInfo) queryClientIP(sessionID uint32, procQuery, procFree *windows.LazyProc) string {
	var buffer uintptr
	var bytesReturned uint32
	
	ret, _, _ := procQuery.Call(
		0, // WTS_CURRENT_SERVER_HANDLE
		uintptr(sessionID),
		uintptr(WTSClientAddress),
		uintptr(unsafe.Pointer(&buffer)),
		uintptr(unsafe.Pointer(&bytesReturned)),
	)
	
	if ret == 0 {
		return ""
	}
	defer procFree.Call(buffer)
	
	if buffer == 0 || bytesReturned < 4 {
		return ""
	}
	
	// 解析IP地址结构
	addr := (*[4]byte)(unsafe.Pointer(buffer))
	return fmt.Sprintf("%d.%d.%d.%d", addr[0], addr[1], addr[2], addr[3])
}

// getSessionStateName 获取会话状态名称
func (wmi *WMIRdpInfo) getSessionStateName(state uint32) string {
	switch state {
	case WTSActive:
		return "Active"
	case WTSConnected:
		return "Connected"
	case WTSDisconnected:
		return "Disconnected"
	case WTSIdle:
		return "Idle"
	default:
		return fmt.Sprintf("Unknown(%d)", state)
	}
}

// queryRdpEvents 查询RDP事件（备选方案）
func (wmi *WMIRdpInfo) queryRdpEvents() error {
	// 这里可以实现通过WMI查询事件日志
	// 比直接调用wevtutil更隐蔽
	return nil
}

// GetRdpInfoViaAPI 通过API方式获取RDP信息（最隐蔽）
func GetRdpInfoViaAPI() string {
	var result strings.Builder
	result.WriteString("=== API方式RDP信息收集 ===\n\n")
	
	info, err := GetRdpInfoViaWMI()
	if err != nil {
		result.WriteString(fmt.Sprintf("API查询失败: %v\n", err))
		return result.String()
	}
	
	result.WriteString("--- 当前RDP会话 ---\n")
	result.WriteString("会话ID\t用户名\t\t客户端名\t\t客户端IP\t状态\n")
	result.WriteString("----------------------------------------------------------------\n")
	
	for _, session := range info.Sessions {
		result.WriteString(fmt.Sprintf("%d\t%-15s\t%-15s\t%-15s\t%s\n",
			session.SessionID, session.UserName, session.ClientName, 
			session.ClientIP, session.State))
	}
	
	if len(info.Events) > 0 {
		result.WriteString("\n--- RDP事件记录 ---\n")
		for _, event := range info.Events {
			result.WriteString(fmt.Sprintf("事件ID: %d, 时间: %s, 用户: %s, IP: %s\n",
				event.EventID, event.TimeCreated, event.UserName, event.ClientIP))
		}
	}
	
	result.WriteString("\n[+] API方式收集完成，完全避免了外部命令调用\n")
	return result.String()
}
