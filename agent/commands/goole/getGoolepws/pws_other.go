//go:build !windows
// +build !windows

package getGoolepws

// Gt_pws 在非Windows平台的存根实现
func Gt_pws() {
	// 非Windows平台不支持浏览器密码获取
}

// GetChromePasswords 在非Windows平台返回不支持信息
func GetChromePasswords() (string, error) {
	return "Chrome密码获取仅在Windows平台支持", nil
}

// GetChromeBookmarks 在非Windows平台返回不支持信息
func GetChromeBookmarks() (string, error) {
	return "Chrome书签获取仅在Windows平台支持", nil
}

// GetEdgePasswords 在非Windows平台返回不支持信息
func GetEdgePasswords() (string, error) {
	return "Edge密码获取仅在Windows平台支持", nil
}

// GetEdgeBookmarks 在非Windows平台返回不支持信息
func GetEdgeBookmarks() (string, error) {
	return "Edge书签获取仅在Windows平台支持", nil
}
