package commands

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"os/exec"
)

// executeAssemblyViaPowershell 通过在后台调用PowerShell来无文件加载和执行.NET程序集
func executeAssemblyViaPowershell(assemblyBytes []byte, args string) (string, error) {
	// 1. 将.NET程序集的二进制内容进行Base64编码，以便能安全地嵌入到PowerShell脚本字符串中。
	encodedAssembly := base64.StdEncoding.EncodeToString(assemblyBytes)

	// 2. 构建核心的PowerShell脚本。
	// 这个脚本做了几件关键事情：
	// a. 从Base64字符串解码出程序集的字节。
	// b. 使用 [System.Reflection.Assembly]::Load() 从内存字节中加载程序集。
	// c. 找到程序的入口点 (通常是 Main 方法)。
	// d. 准备要传递给 Main 方法的参数。
	// e. **关键**：重定向[Console]::Out和[Console]::Error到一个StringWriter对象，
	//    这样就能捕获所有由.NET程序集打印到控制台的输出。
	// f. 调用入口点执行程序集。
	// g. 恢复原始的输出流，并返回捕获到的所有输出。
	psScript := fmt.Sprintf(`
		$encodedAssembly = "%s"
		$assemblyBytes = [System.Convert]::FromBase64String($encodedAssembly)
		$assembly = [System.Reflection.Assembly]::Load($assemblyBytes)
		$entryPoint = $assembly.EntryPoint
		
		# 将传入的参数字符串分割成一个字符串数组
		$parameters = if ([string]::IsNullOrEmpty('%s')) { $null } else { @('%s'.Split(' ', [System.StringSplitOptions]::RemoveEmptyEntries)) }
		
		# 创建一个用于捕获输出的StringWriter
		$stringWriter = New-Object System.IO.StringWriter
		
		# 保存原始的输出和错误流
		$stdout_orig = [System.Console]::Out
		$stderr_orig = [System.Console]::Error
		
		# 将输出和错误流都重定向到我们的StringWriter
		[System.Console]::SetOut($stringWriter)
		[System.Console]::SetError($stringWriter)
		
		# 调用程序集的入口点方法 (Main)
		# $null 表示静态方法，@($parameters) 是传递的参数数组
		$entryPoint.Invoke($null, $parameters)
		
		# 恢复原始的输出和错误流
		[System.Console]::SetOut($stdout_orig)
		[System.Console]::SetError($stderr_orig)
		
		# 获取并返回所有捕获到的输出
		$result = $string_writer.ToString()
		return $result
	`, encodedAssembly, args, args) // 注意：args被用了两次，一次用于判断是否为空，一次用于分割

	// 3. 为了能通过命令行安全地执行这个可能包含各种特殊字符的脚本，
	//    我们对整个脚本再次进行Base64编码。
	//    PowerShell的 -EncodedCommand 参数专门用来接收这种编码后的脚本。
	encodedScript := base64.StdEncoding.EncodeToString([]byte(psScript))

	// 4. 构造最终的执行命令
	// -NoProfile: 不加载配置文件，启动更快、环境更干净。
	// -NonInteractive: 非交互模式。
	// -ExecutionPolicy Bypass: 绕过执行策略限制。
	// -EncodedCommand: 执行Base64编码的命令。
	cmd := exec.Command("powershell.exe", "-NoProfile", "-NonInteractive", "-ExecutionPolicy", "Bypass", "-EncodedCommand", encodedScript)

	// 5. 捕获命令的输出
	var out bytes.Buffer
	var stderr bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		// 如果PowerShell执行本身出错，我们将错误流的内容也一并返回，便于排查问题。
		return "", fmt.Errorf("powershell 执行失败: %v, stderr: %s", err, stderr.String())
	}

	// 6. 成功执行后，返回捕获到的标准输出。
	// 因为我们已经在PowerShell脚本内部处理了GBK编码问题(StringWriter默认使用UTF)，
	// 所以这里通常不需要再进行转码。
	return out.String(), nil
}
