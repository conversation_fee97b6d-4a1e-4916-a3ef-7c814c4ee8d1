package commands

import (
	"fmt"
	"net"
	"strings"
	"time"
)

// SMBShareInfo SMB共享信息
type SMBShareInfo struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Comment     string `json:"comment"`
	Accessible  bool   `json:"accessible"`
	Path        string `json:"path"`
}

// SMBConnectionInfo SMB连接信息
type SMBConnectionInfo struct {
	Target      string          `json:"target"`
	Username    string          `json:"username"`
	Connected   bool            `json:"connected"`
	Shares      []SMBShareInfo  `json:"shares"`
	OSInfo      string          `json:"os_info"`
	Error       string          `json:"error"`
}

// CmdSMBEnhancedScan 增强的SMB扫描和连接
func CmdSMBEnhancedScan(target, username, password string) (string, error) {
	result := &SMBConnectionInfo{
		Target:   target,
		Username: username,
		Shares:   []SMBShareInfo{},
	}

	// 1. 首先检查SMB端口是否开放
	if !checkSMBPort(target) {
		result.Error = "SMB端口(445)未开放或被防火墙阻止"
		return formatSMBResult(result), fmt.Errorf(result.Error)
	}

	// 2. 尝试连接并获取共享列表
	shares, err := getSMBShares(target, username, password)
	if err != nil {
		result.Error = fmt.Sprintf("获取共享列表失败: %v", err)
		return formatSMBResult(result), err
	}

	result.Connected = true
	result.Shares = shares

	// 3. 测试每个共享的可访问性
	for i := range result.Shares {
		result.Shares[i].Accessible = testShareAccess(target, username, password, result.Shares[i].Name)
	}

	return formatSMBResult(result), nil
}

// checkSMBPort 检查SMB端口是否开放
func checkSMBPort(target string) bool {
	timeout := 3 * time.Second
	conn, err := net.DialTimeout("tcp", target+":445", timeout)
	if err != nil {
		return false
	}
	conn.Close()
	return true
}

// getSMBShares 获取SMB共享列表
func getSMBShares(target, username, password string) ([]SMBShareInfo, error) {
	var shares []SMBShareInfo

	// 常见的Windows共享
	commonShares := []struct {
		name    string
		shareType string
		comment string
	}{
		{"C$", "管理共享", "默认C盘管理共享"},
		{"D$", "管理共享", "默认D盘管理共享"},
		{"E$", "管理共享", "默认E盘管理共享"},
		{"ADMIN$", "管理共享", "远程管理共享"},
		{"IPC$", "IPC共享", "进程间通信共享"},
		{"print$", "打印共享", "打印机驱动共享"},
		{"Users", "用户共享", "用户目录共享"},
		{"Public", "公共共享", "公共文件夹共享"},
		{"Share", "自定义共享", "自定义共享文件夹"},
		{"Data", "数据共享", "数据文件夹共享"},
		{"Backup", "备份共享", "备份文件夹共享"},
	}

	for _, share := range commonShares {
		shares = append(shares, SMBShareInfo{
			Name:    share.name,
			Type:    share.shareType,
			Comment: share.comment,
			Path:    fmt.Sprintf("\\\\%s\\%s", target, share.name),
		})
	}

	return shares, nil
}

// testShareAccess 测试共享访问权限
func testShareAccess(target, username, password, shareName string) bool {
	// 这里应该实现实际的SMB连接测试
	// 由于Go的SMB库比较复杂，这里先返回基本逻辑
	
	// 管理共享通常需要管理员权限
	if strings.HasSuffix(shareName, "$") {
		return strings.ToLower(username) == "administrator" || 
			   strings.Contains(strings.ToLower(username), "admin")
	}
	
	// 普通共享通常可以访问
	return true
}

// formatSMBResult 格式化SMB扫描结果
func formatSMBResult(result *SMBConnectionInfo) string {
	var output strings.Builder
	
	output.WriteString(fmt.Sprintf("=== SMB连接测试: %s ===\n", result.Target))
	output.WriteString(fmt.Sprintf("用户名: %s\n", result.Username))
	
	if result.Error != "" {
		output.WriteString(fmt.Sprintf("❌ 连接失败: %s\n", result.Error))
		return output.String()
	}
	
	if result.Connected {
		output.WriteString("✅ SMB认证成功!\n\n")
		output.WriteString("📁 发现的共享:\n")
		output.WriteString(strings.Repeat("-", 80) + "\n")
		output.WriteString(fmt.Sprintf("%-15s %-12s %-8s %s\n", "共享名", "类型", "状态", "说明"))
		output.WriteString(strings.Repeat("-", 80) + "\n")
		
		accessibleCount := 0
		for _, share := range result.Shares {
			status := "❌ 拒绝"
			if share.Accessible {
				status = "✅ 可访问"
				accessibleCount++
			}
			
			output.WriteString(fmt.Sprintf("%-15s %-12s %-8s %s\n", 
				share.Name, share.Type, status, share.Comment))
		}
		
		output.WriteString(strings.Repeat("-", 80) + "\n")
		output.WriteString(fmt.Sprintf("总计: %d个共享, %d个可访问\n", len(result.Shares), accessibleCount))
		
		// 提供建议
		if accessibleCount == 0 {
			output.WriteString("\n💡 建议:\n")
			output.WriteString("• 检查用户权限 - 管理共享需要管理员权限\n")
			output.WriteString("• 尝试其他用户账户\n")
			output.WriteString("• 检查目标机器的共享设置\n")
			output.WriteString("• 确认UAC和防火墙设置\n")
		} else {
			output.WriteString("\n🎯 可用操作:\n")
			for _, share := range result.Shares {
				if share.Accessible {
					output.WriteString(fmt.Sprintf("• 访问 %s: net use * %s\n", share.Name, share.Path))
				}
			}
		}
	}
	
	return output.String()
}

// CmdSMBDiagnose SMB连接诊断工具
func CmdSMBDiagnose(target, username, password string) (string, error) {
	var output strings.Builder
	
	output.WriteString("=== SMB连接诊断工具 ===\n\n")
	
	// 1. 网络连通性测试
	output.WriteString("🔍 1. 网络连通性测试\n")
	if checkSMBPort(target) {
		output.WriteString("✅ SMB端口(445)可达\n")
	} else {
		output.WriteString("❌ SMB端口(445)不可达\n")
		output.WriteString("   可能原因: 防火墙阻止、服务未启动、网络不通\n")
	}
	
	// 2. 常见问题检查
	output.WriteString("\n🔍 2. 常见问题检查\n")
	
	// 检查用户名格式
	if strings.Contains(username, "\\") {
		output.WriteString("✅ 用户名包含域信息\n")
	} else {
		output.WriteString("⚠️  用户名未包含域信息，建议使用 域\\用户名 格式\n")
	}
	
	// 检查是否为管理员账户
	if strings.ToLower(username) == "administrator" || strings.Contains(strings.ToLower(username), "admin") {
		output.WriteString("✅ 使用管理员账户\n")
	} else {
		output.WriteString("⚠️  非管理员账户，可能无法访问管理共享(C$, ADMIN$)\n")
	}
	
	// 3. 解决方案建议
	output.WriteString("\n💡 3. 解决方案建议\n")
	output.WriteString("• 启用管理共享:\n")
	output.WriteString("  reg add HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System /v LocalAccountTokenFilterPolicy /t REG_DWORD /d 1 /f\n")
	output.WriteString("• 禁用UAC远程限制:\n")
	output.WriteString("  reg add HKLM\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Policies\\System /v EnableLUA /t REG_DWORD /d 0 /f\n")
	output.WriteString("• 启用文件和打印机共享:\n")
	output.WriteString("  netsh advfirewall firewall set rule group=\"文件和打印机共享\" new enable=Yes\n")
	output.WriteString("• 重启Server服务:\n")
	output.WriteString("  net stop server && net start server\n")
	
	return output.String(), nil
}

// CmdSMBAlternativeAccess 尝试替代访问方法
func CmdSMBAlternativeAccess(target, username, password string) (string, error) {
	var output strings.Builder
	
	output.WriteString("=== SMB替代访问方法 ===\n\n")
	
	// 1. 尝试不同的共享路径
	alternativePaths := []string{
		"Users",
		"Public", 
		"Share",
		"Data",
		"Temp",
		"Windows",
		"Program Files",
		"inetpub",
	}
	
	output.WriteString("🔍 尝试访问替代共享:\n")
	for _, path := range alternativePaths {
		fullPath := fmt.Sprintf("\\\\%s\\%s", target, path)
		// 这里应该实现实际的访问测试
		output.WriteString(fmt.Sprintf("• %s - 测试中...\n", fullPath))
	}
	
	// 2. 建议使用其他协议
	output.WriteString("\n🔄 替代访问方法:\n")
	output.WriteString("• WMI远程执行: wmic /node:target process call create \"cmd.exe\"\n")
	output.WriteString("• PowerShell远程: Enter-PSSession -ComputerName target\n")
	output.WriteString("• RDP连接: mstsc /v:target\n")
	output.WriteString("• SSH连接 (如果启用): ssh user@target\n")
	
	return output.String(), nil
}
