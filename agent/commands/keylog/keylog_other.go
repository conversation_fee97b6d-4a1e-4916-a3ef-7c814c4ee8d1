//go:build !windows
// +build !windows

package keylog

import (
	"agent/global"
	"fmt"
)

// KeyLoggerConfig 键盘记录器配置
type KeyLoggerConfig struct {
	BufferSize     int
	UploadInterval int
	DebugMode      bool
}

// KeyLogger 键盘记录器接口
type KeyLogger struct {
	config  KeyLoggerConfig
	running bool
}

// NewKeyLogger 创建新的键盘记录器
func NewKeyLogger(config KeyLoggerConfig) *KeyLogger {
	return &KeyLogger{
		config:  config,
		running: false,
	}
}

// Start 在非Windows平台返回不支持
func (k *KeyLogger) Start() error {
	return fmt.Errorf("键盘记录功能仅在Windows平台支持")
}

// Stop 停止键盘记录器
func (k *KeyLogger) Stop() error {
	k.running = false
	return nil
}

// IsRunning 检查是否正在运行
func (k *KeyLogger) IsRunning() bool {
	return k.running
}

// GetInstance 获取全局实例
func GetInstance() global.IKeyLogger {
	return nil
}

// SetInstance 设置全局实例
func SetInstance(logger global.IKeyLogger) {
	// 非Windows平台不支持
}

// New 创建新的键盘记录器实例
func New(config KeyLoggerConfig) *KeyLogger {
	return NewKeyLogger(config)
}
