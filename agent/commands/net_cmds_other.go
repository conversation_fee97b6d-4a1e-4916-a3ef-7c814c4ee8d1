//go:build !windows
// +build !windows

// xxx/commands/net_cmds_other.go
package commands

import (
	"fmt"
	"strings"

	gops_net "github.com/shirou/gopsutil/v3/net"
	gops_proc "github.com/shirou/gopsutil/v3/process"
)

// CmdNetstat 在非Windows平台使用gopsutil实现
func CmdNetstat() (string, error) {
	connections, err := gops_net.Connections("all")
	if err != nil {
		return "", fmt.Errorf("获取网络连接失败: %w", err)
	}

	var result strings.Builder
	result.WriteString("协议  本地地址                远程地址                状态           PID\n")
	result.WriteString("====  ==================    ==================    ==========    =====\n")

	for _, conn := range connections {
		protocol := strings.ToUpper(fmt.Sprintf("%d", conn.Type))
		localAddr := fmt.Sprintf("%s:%d", conn.Laddr.IP, conn.Laddr.Port)
		remoteAddr := fmt.Sprintf("%s:%d", conn.Raddr.IP, conn.Raddr.Port)
		status := conn.Status
		pid := conn.Pid

		result.WriteString(fmt.Sprintf("%-6s %-20s %-20s %-12s %d\n",
			protocol, localAddr, remoteAddr, status, pid))
	}

	return result.String(), nil
}

// CmdArp 在非Windows平台的简化实现
func CmdArp() (string, error) {
	return "ARP表查询在此平台不支持，请使用系统命令: arp -a", nil
}

// CmdNetConnections 获取网络连接详情 (跨平台版本)
func CmdNetConnections() (string, error) {
	connections, err := gops_net.Connections("all")
	if err != nil {
		return "", fmt.Errorf("获取网络连接失败: %w", err)
	}

	var result strings.Builder
	result.WriteString("详细网络连接信息:\n")
	result.WriteString("================\n\n")

	for _, conn := range connections {
		result.WriteString(fmt.Sprintf("协议: %s\n", strings.ToUpper(fmt.Sprintf("%d", conn.Type))))
		result.WriteString(fmt.Sprintf("本地地址: %s:%d\n", conn.Laddr.IP, conn.Laddr.Port))
		result.WriteString(fmt.Sprintf("远程地址: %s:%d\n", conn.Raddr.IP, conn.Raddr.Port))
		result.WriteString(fmt.Sprintf("状态: %s\n", conn.Status))
		result.WriteString(fmt.Sprintf("PID: %d\n", conn.Pid))

		// 尝试获取进程信息
		if conn.Pid > 0 {
			if proc, err := gops_proc.NewProcess(conn.Pid); err == nil {
				if name, err := proc.Name(); err == nil {
					result.WriteString(fmt.Sprintf("进程名: %s\n", name))
				}
			}
		}
		result.WriteString("\n")
	}

	return result.String(), nil
}
