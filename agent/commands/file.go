package commands

import (
	"agent/global"
	"agent/help"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"
)

// 1. [新增] CmdCopyFile 实现文件的复制
func CmdCopyFile(source, destination string) (string, error) {
	// 使用 help 包来解析和标准化路径
	srcPath := help.ResolvePath(source)
	destPath := help.ResolvePath(destination)

	// 打开源文件
	srcFile, err := os.Open(srcPath)
	if err != nil {
		return "", fmt.Errorf("无法打开源文件 '%s': %w", srcPath, err)
	}
	defer srcFile.Close()

	// 检查源是否是目录
	srcInfo, err := srcFile.Stat()
	if err != nil {
		return "", fmt.Errorf("无法获取源文件信息: %w", err)
	}
	if srcInfo.IsDir() {
		return "", fmt.Errorf("错误: 'cp' 命令不支持复制整个目录，请一次复制一个文件")
	}

	// 创建目标文件
	destFile, err := os.Create(destPath)
	if err != nil {
		return "", fmt.Errorf("无法创建目标文件 '%s': %w", destPath, err)
	}
	defer destFile.Close()

	// 使用 io.Copy 进行高效的数据复制
	bytesCopied, err := io.Copy(destFile, srcFile)
	if err != nil {
		// 如果复制出错，尝试删除已创建的不完整目标文件
		os.Remove(destPath)
		return "", fmt.Errorf("文件复制过程中出错: %w", err)
	}

	return fmt.Sprintf("成功: 已将 '%s' 复制到 '%s' (%d 字节)。", srcPath, destPath, bytesCopied), nil
}

// 2. [新增/修改] CmdMoveOrRenameFile 实现文件/目录的移动或重命名
// 这个函数将同时服务于 'mv' 和 'ren' 命令
func CmdMoveOrRenameFile(source, destination string) (string, error) {
	srcPath := help.ResolvePath(source)
	destPath := help.ResolvePath(destination)

	// os.Rename 在 Go 中同时负责移动和重命名
	err := os.Rename(srcPath, destPath)
	if err != nil {
		return "", fmt.Errorf("移动或重命名失败: %w", err)
	}

	return fmt.Sprintf("成功: 已将 '%s' 移动/重命名为 '%s'", srcPath, destPath), nil
}

// ★★★ 最终版 CmdRemove 函数，与你的项目风格完全一致 ★★★
func CmdRemove(payload string) (string, error) {
	payloadTrimmed := strings.TrimSpace(payload)

	// 1. 检查 'all' 参数
	if strings.EqualFold(payloadTrimmed, "all") {
		// 直接从 global 包获取当前目录
		currentDir := global.CurrentDir

		entries, err := ioutil.ReadDir(currentDir)
		if err != nil {
			return "", fmt.Errorf("无法读取当前目录 '%s': %w", currentDir, err)
		}

		if len(entries) == 0 {
			return fmt.Sprintf("当前目录 '%s' 为空，无需删除。", currentDir), nil
		}

		var deletedItems []string
		var errorItems []string

		for _, entry := range entries {
			fullPath := filepath.Join(currentDir, entry.Name())
			if err := os.RemoveAll(fullPath); err != nil {
				errorItems = append(errorItems, fmt.Sprintf("%s (%v)", entry.Name(), err))
			} else {
				deletedItems = append(deletedItems, entry.Name())
			}
		}

		var b strings.Builder
		fmt.Fprintf(&b, "在目录 '%s' 中执行批量删除:\n", currentDir)
		if len(deletedItems) > 0 {
			fmt.Fprintf(&b, "  - 成功删除 %d 个项目: %s\n", len(deletedItems), strings.Join(deletedItems, ", "))
		}
		if len(errorItems) > 0 {
			fmt.Fprintf(&b, "  - 删除失败 %d 个项目: %s\n", len(errorItems), strings.Join(errorItems, ", "))
		}
		return b.String(), nil

	} else {
		// 2. 删除单个文件/目录的逻辑
		// help.ResolvePath 内部会正确使用 global.CurrentDir
		targetPath := help.ResolvePath(payloadTrimmed)

		err := os.RemoveAll(targetPath)
		if err != nil {
			return "", err
		}
		return "已删除: " + payloadTrimmed, nil
	}
}
