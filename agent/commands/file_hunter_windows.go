//go:build windows
// +build windows

package commands

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"
)

// 敏感文件搜索模块 (Windows版本)

// FileMatch 文件匹配结果
type FileMatch struct {
	FilePath    string    `json:"file_path"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	ModTime     time.Time `json:"mod_time"`
	MatchType   string    `json:"match_type"`
	MatchReason string    `json:"match_reason"`
	Content     string    `json:"content,omitempty"`
}

// 预定义的敏感文件模式
var (
	credentialPatterns = []string{
		`.*\.pem$`, `.*\.key$`, `.*\.p12$`, `.*\.pfx$`, `.*\.crt$`,
		`.*id_rsa.*`, `.*\.ssh.*`, `.*credentials.*`, `.*\.aws.*`,
	}
	
	configPatterns = []string{
		`.*config\..*`, `.*\.conf$`, `.*\.ini$`, `.*\.json$`, `.*\.xml$`,
	}
	
	databasePatterns = []string{
		`.*\.db$`, `.*\.sqlite.*`, `.*\.mdb$`, `.*\.sql$`,
	}
)

// CmdFileHunt 敏感文件搜索命令
func CmdFileHunt(searchType string) (string, error) {
	var patterns []string
	var searchPaths []string
	
	userProfile := os.Getenv("USERPROFILE")
	searchPaths = []string{
		userProfile,
		filepath.Join(userProfile, "Desktop"),
		filepath.Join(userProfile, "Documents"),
		filepath.Join(userProfile, "Downloads"),
	}
	
	switch strings.ToLower(searchType) {
	case "credentials", "creds":
		patterns = credentialPatterns
	case "config":
		patterns = configPatterns
	case "database", "db":
		patterns = databasePatterns
	case "all":
		patterns = append(credentialPatterns, configPatterns...)
		patterns = append(patterns, databasePatterns...)
	default:
		return "", fmt.Errorf("未知搜索类型: %s", searchType)
	}
	
	matches, err := searchFiles(searchPaths, patterns, 100)
	if err != nil {
		return "", err
	}
	
	return formatResults(matches, searchType), nil
}

// searchFiles 搜索文件
func searchFiles(searchPaths, patterns []string, maxResults int) ([]FileMatch, error) {
	var matches []FileMatch
	matchCount := 0
	
	// 编译正则表达式
	regexes := make([]*regexp.Regexp, len(patterns))
	for i, pattern := range patterns {
		regex, err := regexp.Compile(`(?i)` + pattern)
		if err != nil {
			continue
		}
		regexes[i] = regex
	}
	
	for _, searchPath := range searchPaths {
		if matchCount >= maxResults {
			break
		}
		
		err := filepath.WalkDir(searchPath, func(path string, d fs.DirEntry, err error) error {
			if err != nil || matchCount >= maxResults {
				return nil
			}
			
			if d.IsDir() {
				return nil
			}
			
			fileName := strings.ToLower(d.Name())
			for i, regex := range regexes {
				if regex != nil && regex.MatchString(fileName) {
					info, err := d.Info()
					if err != nil {
						continue
					}
					
					match := FileMatch{
						FilePath:    path,
						FileName:    d.Name(),
						FileSize:    info.Size(),
						ModTime:     info.ModTime(),
						MatchType:   "文件名匹配",
						MatchReason: patterns[i],
					}
					
					matches = append(matches, match)
					matchCount++
					break
				}
			}
			
			return nil
		})
		
		if err != nil {
			continue
		}
	}
	
	return matches, nil
}

// formatResults 格式化结果
func formatResults(matches []FileMatch, searchType string) string {
	var result strings.Builder
	result.WriteString(fmt.Sprintf("=== 敏感文件搜索报告 (%s) ===\n\n", searchType))
	
	if len(matches) == 0 {
		result.WriteString("未找到匹配的文件\n")
		return result.String()
	}
	
	result.WriteString(fmt.Sprintf("找到 %d 个文件:\n\n", len(matches)))
	
	for i, match := range matches {
		result.WriteString(fmt.Sprintf("--- 文件 %d ---\n", i+1))
		result.WriteString(fmt.Sprintf("路径: %s\n", match.FilePath))
		result.WriteString(fmt.Sprintf("大小: %d bytes\n", match.FileSize))
		result.WriteString(fmt.Sprintf("修改时间: %s\n", match.ModTime.Format("2006-01-02 15:04:05")))
		result.WriteString("\n")
	}
	
	return result.String()
}
