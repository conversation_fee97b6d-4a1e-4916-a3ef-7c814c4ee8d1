//go:build !windows
// +build !windows

package ppid

import "fmt"

// SpoofParent 在非Windows平台返回不支持信息
func SpoofParent(targetPID int) error {
	return fmt.Errorf("父进程欺骗仅在Windows平台支持")
}

// GetProcessList 在非Windows平台返回不支持信息
func GetProcessList() (string, error) {
	return "进程列表获取仅在Windows平台支持", nil
}

// Execute 在非Windows平台返回不支持信息
func Execute(fullCommand string, parentProcessName string, workingDir string) (string, error) {
	return "父进程欺骗执行仅在Windows平台支持", nil
}
