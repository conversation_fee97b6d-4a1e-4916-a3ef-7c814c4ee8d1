//go:build !windows
// +build !windows

package commands

import (
	"os/exec"
)

// getDriveType 在非Windows平台返回unknown
func getDriveType(drivePath string) string {
	return "unknown"
}

// setWindowsHiddenProcess 在非Windows平台为空实现
func setWindowsHiddenProcess(cmd *exec.Cmd) {
	// 非Windows平台无需设置
}

// setWindowsExecuteHiddenProcess 在非Windows平台为空实现
func setWindowsExecuteHiddenProcess(cmd *exec.Cmd) {
	// 非Windows平台无需设置
}

// CmdIpconfig 在非Windows平台返回不支持信息
func CmdIpconfig() (string, error) {
	return "ipconfig命令仅在Windows平台支持，请使用 ifconfig 或 ip addr", nil
}
