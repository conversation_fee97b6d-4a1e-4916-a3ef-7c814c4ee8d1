//go:build windows
// +build windows

package commands

import (
	"fmt"
	"strings"

	"github.com/go-ole/go-ole"
	"github.com/yusufpapurcu/wmi"
)

// Win32_Share 是对应 WMI 中 Win32_Share 类的结构体
type Win32_Share struct {
	Name string
	Path string
	Type uint32
}

// CmdNetShare 使用 WMI 查询指定主机的网络共享 (最终兼容版)
func CmdNetShare(targetIP string) (string, error) {
	// 初始化 COM
	err := ole.CoInitializeEx(0, ole.COINIT_MULTITHREADED)

	// ★★★ 核心修复点：通过判断具体的错误码数值来兼容旧版库 ★★★
	if err != nil {
		// ole.S_FALSE 的 HRESULT 值为 1
		const S_FALSE = 0x00000001
		// ole.RPC_E_CHANGED_MODE 的 HRESULT 值为 0x80010106
		const RPC_E_CHANGED_MODE = 0x80010106

		// 将返回的error转换为 *ole.OleError 类型
		if o, ok := err.(*ole.OleError); ok {
			// 如果错误码不是这两个我们预期可以忽略的，才认为是真正的失败
			if o.Code() != S_FALSE && o.Code() != RPC_E_CHANGED_MODE {
				return "", fmt.Errorf("COM 初始化失败: %w", err)
			}
			// 如果是这两个错误码之一，则忽略错误，继续执行
		} else {
			// 如果返回的错误甚至不是 OleError 类型，那也是一个真正的失败
			return "", fmt.Errorf("COM 初始化时发生未知错误: %w", err)
		}
	}
	// ★★★ 修复结束 ★★★

	defer ole.CoUninitialize()

	var dst []Win32_Share

	wmiNamespace := ""
	if targetIP != "." && targetIP != "127.0.0.1" && targetIP != "localhost" {
		wmiNamespace = fmt.Sprintf(`\\%s\ROOT\CIMV2`, targetIP)
	}

	if err := wmi.Query(wmi.CreateQuery(&dst, ""), &dst, wmiNamespace); err != nil {
		return "", fmt.Errorf("WMI 查询 '%s' 失败: %w", targetIP, err)
	}

	if len(dst) == 0 {
		return fmt.Sprintf("在 %s 上未找到任何网络共享，或无权限查询。", targetIP), nil
	}

	var b strings.Builder
	fmt.Fprintf(&b, "--- %s 的网络共享 ---\n", targetIP)
	fmt.Fprintf(&b, "%-25s %-10s %s\n", "共享名 (Name)", "类型 (Type)", "路径 (Path)")
	fmt.Fprintln(&b, strings.Repeat("-", 80))
	for _, share := range dst {
		if strings.HasSuffix(share.Name, "$") {
			continue
		}
		fmt.Fprintf(&b, "%-25s %-10s %s\n", share.Name, getShareType(share.Type), share.Path)
	}

	return b.String(), nil
}

// getShareType 辅助函数 (保持不变)
func getShareType(t uint32) string {
	switch t {
	case 0:
		return "磁盘驱动器"
	case 1:
		return "打印队列"
	case 2:
		return "设备"
	case 3:
		return "IPC"
	default:
		return "未知"
	}
}
