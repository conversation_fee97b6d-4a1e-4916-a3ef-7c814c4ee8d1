//go:build !windows
// +build !windows

package commands

// CmdQueryRegistry 在非Windows平台返回不支持信息
func CmdQueryRegistry(payload string) (string, error) {
	return "注册表查询仅在Windows平台支持", nil
}

// CmdSetRegistry 在非Windows平台返回不支持信息
func CmdSetRegistry(payload string) (string, error) {
	return "注册表设置仅在Windows平台支持", nil
}

// CmdDeleteRegistry 在非Windows平台返回不支持信息
func CmdDeleteRegistry(payload string) (string, error) {
	return "注册表删除仅在Windows平台支持", nil
}
