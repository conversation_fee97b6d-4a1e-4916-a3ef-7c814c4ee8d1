//go:build windows
// +build windows

package commands

import (
	"fmt"
	"strings"

	"golang.org/x/sys/windows/registry"
)

// CmdQueryRegistry 查询注册表
func CmdQueryRegistry(payload string) (string, error) {
	parts := strings.Fields(payload)
	if len(parts) < 2 {
		return "用法: reg_query <根键> \"<子键路径>\" [值名称]", nil
	}

	rootKeyStr := strings.ToUpper(parts[0])
	subKeyPath := strings.Trim(parts[1], `"`)
	valueName := ""
	if len(parts) > 2 {
		valueName = strings.Trim(parts[2], `"`)
	}

	var rootKey registry.Key
	switch rootKeyStr {
	case "HKLM":
		rootKey = registry.LOCAL_MACHINE
	case "HKCU":
		rootKey = registry.CURRENT_USER
	case "HKCR":
		rootKey = registry.CLASSES_ROOT
	case "HKU":
		rootKey = registry.USERS
	case "HKCC":
		rootKey = registry.CURRENT_CONFIG
	default:
		return "错误: 无效的根键。支持: HKLM, HKCU, HKCR, HKU, HKCC", nil
	}

	key, err := registry.OpenKey(rootKey, subKeyPath, registry.QUERY_VALUE|registry.ENUMERATE_SUB_KEYS)
	if err != nil {
		return "", fmt.Errorf("无法打开注册表键 '%s\\%s': %w", rootKeyStr, subKeyPath, err)
	}
	defer key.Close()

	// ★★★ 核心修复点 2：使用 strings.Builder 而不是 []byte ★★★
	var b strings.Builder

	if valueName != "" {
		s, _, err := key.GetStringValue(valueName)
		if err == nil {
			fmt.Fprintf(&b, "%s: %s\n", valueName, s)
			return b.String(), nil
		}

		i, _, err := key.GetIntegerValue(valueName)
		if err == nil {
			fmt.Fprintf(&b, "%s: %d (DWORD)\n", valueName, i)
			return b.String(), nil
		}

		bin, _, err := key.GetBinaryValue(valueName)
		if err == nil {
			// 将二进制数据格式化为十六进制字符串
			fmt.Fprintf(&b, "%s: %x (Binary)\n", valueName, bin)
			return b.String(), nil
		}

		return "", fmt.Errorf("无法读取值 '%s' 或其类型不受支持", valueName)

	} else {
		valueNames, err := key.ReadValueNames(-1)
		if err != nil {
			return "", fmt.Errorf("无法读取键 '%s\\%s' 下的所有值: %w", rootKeyStr, subKeyPath, err)
		}

		fmt.Fprintf(&b, "--- 注册表 '%s\\%s'下的值 ---\n", rootKeyStr, subKeyPath)
		for _, name := range valueNames {
			// 为了简洁，我们只获取字符串类型的值进行预览
			s, valType, _ := key.GetStringValue(name)
			valStr := s
			if valType == registry.NONE { // 如果不是字符串类型，显示类型提示
				valStr = "(非字符串类型)"
			}
			if name == "" {
				name = "(默认)"
			}
			fmt.Fprintf(&b, "  %-30s    %s\n", name, valStr)
		}
	}

	return b.String(), nil
}
