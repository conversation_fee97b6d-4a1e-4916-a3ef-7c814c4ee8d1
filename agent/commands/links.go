package commands

import (
	"agent/help"
	"fmt"
	"os"
	"path/filepath"
	"strings"

	golnk "github.com/parsiya/golnk"
	"golang.org/x/text/encoding/simplifiedchinese" // 新增
	"golang.org/x/text/transform"                  // 新增
	"io"
)

// 尝试把 GBK/CP936 字符串转成 UTF-8；失败就原样返回
func decodeGBK(s string) string {
	if s == "" {
		return s
	}
	// 这里直接全部尝试解码，成功率高，失败也只是保持原样
	reader := transform.NewReader(strings.NewReader(s), simplifiedchinese.GBK.NewDecoder())
	utf8Bytes, err := io.ReadAll(reader)
	if err != nil {
		return s
	}
	return string(utf8Bytes)
}

// CmdResolveLnk 解析单个 .lnk 文件或目录下所有 .lnk 文件
func CmdResolveLnk(path string) (string, error) {
	target := help.ResolvePath(path)

	info, err := os.Stat(target)
	if err != nil {
		return "", fmt.Errorf("路径不存在或无法访问: %w", err)
	}

	var b strings.Builder

	// ---------- 目录 ----------
	if info.IsDir() {
		b.WriteString(fmt.Sprintf("--- 目录 '%s' 下的快捷方式 ---\n", target))

		entries, err := os.ReadDir(target)
		if err != nil {
			return "", fmt.Errorf("无法读取目录: %w", err)
		}

		found := 0
		for _, e := range entries {
			if e.IsDir() || !strings.HasSuffix(strings.ToLower(e.Name()), ".lnk") {
				continue
			}
			lnkPath := filepath.Join(target, e.Name())
			lnk, err := golnk.File(lnkPath)
			if err != nil {
				continue // 跳过损坏/无法解析的 .lnk
			}
			found++

			base := lnk.LinkInfo.LocalBasePathUnicode
			if base == "" { // 退回 ANSI + 转码
				base = decodeGBK(lnk.LinkInfo.LocalBasePath)
			}

			b.WriteString(fmt.Sprintf("  %s  ->  %s\n", e.Name(), base))
		}

		if found == 0 {
			b.WriteString("  (未找到任何 .lnk 文件)\n")
		}

		// ---------- 单个文件 ----------
	} else {
		if !strings.HasSuffix(strings.ToLower(target), ".lnk") {
			return "错误: 目标必须是 .lnk 文件或目录。", nil
		}

		lnk, err := golnk.File(target)
		if err != nil {
			return "", fmt.Errorf("无法解析快捷方式 '%s': %w", target, err)
		}

		b.WriteString(fmt.Sprintf("--- 快捷方式 '%s' 详情 ---\n", filepath.Base(target)))

		base := lnk.LinkInfo.LocalBasePathUnicode
		if base == "" {
			base = decodeGBK(lnk.LinkInfo.LocalBasePath)
		}
		b.WriteString(fmt.Sprintf("  目标路径 (Target):  %s\n", base))

		if wd := decodeGBK(lnk.StringData.WorkingDir); wd != "" {
			b.WriteString(fmt.Sprintf("  起始位置 (Working Dir): %s\n", wd))
		}
		if args := decodeGBK(lnk.StringData.CommandLineArguments); args != "" {
			b.WriteString(fmt.Sprintf("  命令行参数 (Arguments): %s\n", args))
		}
		if icon := decodeGBK(lnk.StringData.IconLocation); icon != "" {
			b.WriteString(fmt.Sprintf("  图标位置 (Icon): %s\n", icon))
		}
	}

	return b.String(), nil
}
