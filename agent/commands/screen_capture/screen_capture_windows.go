//go:build windows
// +build windows

package screen_capture

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"unsafe"

	"golang.org/x/sys/windows"
)

// Windows API 常量
const (
	SRCCOPY     = 0x00CC0020
	CAPTUREBLT  = 0x40000000
	DIB_RGB_COLORS = 0
)

// Windows API 函数
var (
	user32   = windows.NewLazySystemDLL("user32.dll")
	gdi32    = windows.NewLazySystemDLL("gdi32.dll")
	kernel32 = windows.NewLazySystemDLL("kernel32.dll")

	procGetDC           = user32.NewProc("GetDC")
	procReleaseDC       = user32.NewProc("ReleaseDC")
	procGetSystemMetrics = user32.NewProc("GetSystemMetrics")
	procCreateCompatibleDC = gdi32.NewProc("CreateCompatibleDC")
	procCreateCompatibleBitmap = gdi32.NewProc("CreateCompatibleBitmap")
	procSelectObject    = gdi32.NewProc("SelectObject")
	procBitBlt          = gdi32.NewProc("BitBlt")
	procDeleteObject    = gdi32.NewProc("DeleteObject")
	procDeleteDC        = gdi32.NewProc("DeleteDC")
	procGetDIBits       = gdi32.NewProc("GetDIBits")
	procGlobalAlloc     = kernel32.NewProc("GlobalAlloc")
	procGlobalLock      = kernel32.NewProc("GlobalLock")
	procGlobalUnlock    = kernel32.NewProc("GlobalUnlock")
	procGlobalFree      = kernel32.NewProc("GlobalFree")
)

// BITMAPINFOHEADER 结构
type BITMAPINFOHEADER struct {
	BiSize          uint32
	BiWidth         int32
	BiHeight        int32
	BiPlanes        uint16
	BiBitCount      uint16
	BiCompression   uint32
	BiSizeImage     uint32
	BiXPelsPerMeter int32
	BiYPelsPerMeter int32
	BiClrUsed       uint32
	BiClrImportant  uint32
}

// BITMAPINFO 结构
type BITMAPINFO struct {
	BmiHeader BITMAPINFOHEADER
	BmiColors [1]uint32
}

// 屏幕捕获命令结构
type ScreenCommand struct {
	Action  string                 `json:"action"`
	Params  map[string]interface{} `json:"params"`
}

// CmdScreenCapture 屏幕捕获主函数
func CmdScreenCapture(payload string) (string, error) {
	var cmd ScreenCommand
	if err := json.Unmarshal([]byte(payload), &cmd); err != nil {
		return "", fmt.Errorf("解析屏幕捕获命令失败: %v", err)
	}

	switch cmd.Action {
	case "screenshot":
		return handleScreenshot(cmd.Params)
	case "get_screen_info":
		return handleGetScreenInfo()
	case "capture_window":
		return handleCaptureWindow(cmd.Params)
	case "capture_region":
		return handleCaptureRegion(cmd.Params)
	default:
		return "", fmt.Errorf("未知的屏幕捕获命令: %s", cmd.Action)
	}
}

// 全屏截图
func handleScreenshot(params map[string]interface{}) (string, error) {
	// 获取屏幕尺寸
	width, _, _ := procGetSystemMetrics.Call(0)  // SM_CXSCREEN
	height, _, _ := procGetSystemMetrics.Call(1) // SM_CYSCREEN

	// 获取屏幕DC
	hdc, _, _ := procGetDC.Call(0)
	if hdc == 0 {
		return "", fmt.Errorf("获取屏幕DC失败")
	}
	defer procReleaseDC.Call(0, hdc)

	// 创建兼容DC
	memDC, _, _ := procCreateCompatibleDC.Call(hdc)
	if memDC == 0 {
		return "", fmt.Errorf("创建兼容DC失败")
	}
	defer procDeleteDC.Call(memDC)

	// 创建兼容位图
	hBitmap, _, _ := procCreateCompatibleBitmap.Call(hdc, width, height)
	if hBitmap == 0 {
		return "", fmt.Errorf("创建兼容位图失败")
	}
	defer procDeleteObject.Call(hBitmap)

	// 选择位图到DC
	procSelectObject.Call(memDC, hBitmap)

	// 复制屏幕内容
	ret, _, _ := procBitBlt.Call(memDC, 0, 0, width, height, hdc, 0, 0, SRCCOPY|CAPTUREBLT)
	if ret == 0 {
		return "", fmt.Errorf("复制屏幕内容失败")
	}

	// 转换为图像数据
	imageData, err := bitmapToImage(hBitmap, int(width), int(height))
	if err != nil {
		return "", fmt.Errorf("转换位图失败: %v", err)
	}

	// 获取压缩格式
	format := "jpeg"
	if f, ok := params["format"].(string); ok {
		format = f
	}

	// 压缩图像
	var buf bytes.Buffer
	switch format {
	case "png":
		err = png.Encode(&buf, imageData)
	case "jpeg":
		quality := 80
		if q, ok := params["quality"].(float64); ok {
			quality = int(q)
		}
		err = jpeg.Encode(&buf, imageData, &jpeg.Options{Quality: quality})
	default:
		err = jpeg.Encode(&buf, imageData, &jpeg.Options{Quality: 80})
	}

	if err != nil {
		return "", fmt.Errorf("压缩图像失败: %v", err)
	}

	// 编码为base64
	base64Data := base64.StdEncoding.EncodeToString(buf.Bytes())

	result := map[string]interface{}{
		"width":  int(width),
		"height": int(height),
		"format": format,
		"data":   base64Data,
		"size":   buf.Len(),
	}

	jsonData, _ := json.Marshal(result)
	return string(jsonData), nil
}

// 获取屏幕信息
func handleGetScreenInfo() (string, error) {
	width, _, _ := procGetSystemMetrics.Call(0)  // SM_CXSCREEN
	height, _, _ := procGetSystemMetrics.Call(1) // SM_CYSCREEN

	result := map[string]interface{}{
		"width":  int(width),
		"height": int(height),
		"dpi":    96, // 默认DPI
	}

	jsonData, _ := json.Marshal(result)
	return string(jsonData), nil
}

// 捕获窗口
func handleCaptureWindow(params map[string]interface{}) (string, error) {
	// 这里可以实现特定窗口的捕获
	// 暂时返回全屏截图
	return handleScreenshot(params)
}

// 捕获区域
func handleCaptureRegion(params map[string]interface{}) (string, error) {
	x, ok1 := params["x"].(float64)
	y, ok2 := params["y"].(float64)
	w, ok3 := params["width"].(float64)
	h, ok4 := params["height"].(float64)

	if !ok1 || !ok2 || !ok3 || !ok4 {
		return "", fmt.Errorf("缺少区域参数")
	}

	// 获取屏幕DC
	hdc, _, _ := procGetDC.Call(0)
	if hdc == 0 {
		return "", fmt.Errorf("获取屏幕DC失败")
	}
	defer procReleaseDC.Call(0, hdc)

	// 创建兼容DC
	memDC, _, _ := procCreateCompatibleDC.Call(hdc)
	if memDC == 0 {
		return "", fmt.Errorf("创建兼容DC失败")
	}
	defer procDeleteDC.Call(memDC)

	// 创建兼容位图
	hBitmap, _, _ := procCreateCompatibleBitmap.Call(hdc, uintptr(w), uintptr(h))
	if hBitmap == 0 {
		return "", fmt.Errorf("创建兼容位图失败")
	}
	defer procDeleteObject.Call(hBitmap)

	// 选择位图到DC
	procSelectObject.Call(memDC, hBitmap)

	// 复制指定区域
	ret, _, _ := procBitBlt.Call(memDC, 0, 0, uintptr(w), uintptr(h), hdc, uintptr(x), uintptr(y), SRCCOPY|CAPTUREBLT)
	if ret == 0 {
		return "", fmt.Errorf("复制屏幕区域失败")
	}

	// 转换为图像数据
	imageData, err := bitmapToImage(hBitmap, int(w), int(h))
	if err != nil {
		return "", fmt.Errorf("转换位图失败: %v", err)
	}

	// 压缩图像
	var buf bytes.Buffer
	err = jpeg.Encode(&buf, imageData, &jpeg.Options{Quality: 80})
	if err != nil {
		return "", fmt.Errorf("压缩图像失败: %v", err)
	}

	// 编码为base64
	base64Data := base64.StdEncoding.EncodeToString(buf.Bytes())

	result := map[string]interface{}{
		"x":      int(x),
		"y":      int(y),
		"width":  int(w),
		"height": int(h),
		"format": "jpeg",
		"data":   base64Data,
		"size":   buf.Len(),
	}

	jsonData, _ := json.Marshal(result)
	return string(jsonData), nil
}

// 将位图转换为图像
func bitmapToImage(hBitmap uintptr, width, height int) (image.Image, error) {
	// 获取屏幕DC
	hdc, _, _ := procGetDC.Call(0)
	if hdc == 0 {
		return nil, fmt.Errorf("获取屏幕DC失败")
	}
	defer procReleaseDC.Call(0, hdc)

	// 准备BITMAPINFO结构
	var bi BITMAPINFO
	bi.BmiHeader.BiSize = uint32(unsafe.Sizeof(bi.BmiHeader))
	bi.BmiHeader.BiWidth = int32(width)
	bi.BmiHeader.BiHeight = -int32(height) // 负值表示从上到下
	bi.BmiHeader.BiPlanes = 1
	bi.BmiHeader.BiBitCount = 32
	bi.BmiHeader.BiCompression = 0 // BI_RGB

	// 分配内存
	dataSize := width * height * 4
	hMem, _, _ := procGlobalAlloc.Call(0x0040, uintptr(dataSize)) // GMEM_ZEROINIT
	if hMem == 0 {
		return nil, fmt.Errorf("分配内存失败")
	}
	defer procGlobalFree.Call(hMem)

	pData, _, _ := procGlobalLock.Call(hMem)
	if pData == 0 {
		return nil, fmt.Errorf("锁定内存失败")
	}
	defer procGlobalUnlock.Call(hMem)

	// 获取位图数据
	ret, _, _ := procGetDIBits.Call(hdc, hBitmap, 0, uintptr(height), pData, uintptr(unsafe.Pointer(&bi)), DIB_RGB_COLORS)
	if ret == 0 {
		return nil, fmt.Errorf("获取位图数据失败")
	}

	// 转换为Go图像
	data := (*[1 << 30]byte)(unsafe.Pointer(pData))[:dataSize:dataSize]
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	for y := 0; y < height; y++ {
		for x := 0; x < width; x++ {
			offset := (y*width + x) * 4
			// Windows位图格式是BGRA，需要转换为RGBA
			img.Pix[(y*width+x)*4+0] = data[offset+2] // R
			img.Pix[(y*width+x)*4+1] = data[offset+1] // G
			img.Pix[(y*width+x)*4+2] = data[offset+0] // B
			img.Pix[(y*width+x)*4+3] = data[offset+3] // A
		}
	}

	return img, nil
}
