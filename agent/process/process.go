// process/process.go
package process

import (
	"fmt"
	"strconv"
	"strings"

	// 我们统一使用 gopsutil，它功能强大且跨平台
	"github.com/shirou/gopsutil/v3/process"
)

// --- 核心查找函数 ---

// FindProcessByName 遍历系统进程，根据名称查找第一个匹配的进程PID。
// targetName: 要查找的进程名，例如 "explorer.exe"。
// 返回值: 找到的进程PID(uint32) 和错误信息。
func FindProcessByName(targetName string) (uint32, error) {
	// 1. 获取系统上所有进程的PID列表
	pids, err := process.Pids()
	if err != nil {
		return 0, fmt.Errorf("无法获取PID列表: %w", err)
	}

	targetLower := strings.ToLower(targetName)

	// 2. 遍历所有PID
	for _, pid := range pids {
		// 3. 根据PID创建新的进程对象
		p, err := process.NewProcess(pid)
		if err != nil {
			// 获取进程信息失败，可能是进程已退出或权限不足，跳过
			continue
		}

		// 4. 获取进程名
		name, err := p.Name()
		if err != nil {
			continue // 获取名称失败，跳过
		}

		// 5. 不区分大小写地比较进程名
		if strings.ToLower(name) == targetLower {
			// 找到了！立即返回这个进程的PID
			return uint32(pid), nil
		}
	}

	// 6. 如果循环结束还没找到，就返回错误
	return 0, fmt.Errorf("进程 '%s' 未找到", targetName)
}

// FindProcess (终极版) 检查指定进程是否存在，支持PID、精确名称、关键字模糊搜索和多目标搜索。
func FindProcess(target string) (int, string) {
	// 1. 尝试将整个输入解析为单个PID
	pid, err := strconv.Atoi(target)
	if err == nil {
		// --- 按单个 PID 查找 (最高优先级) ---
		p, err := process.NewProcess(int32(pid))
		if err != nil {
			return 0, fmt.Sprintf("未找到 PID 为 %d 的进程。", pid)
		}

		name, _ := p.Name()
		exePath, _ := p.Exe()
		return 1, fmt.Sprintf("进程存在 -> PID: %-8d 名称: %s, 路径: %s", pid, name, exePath)
	}

	// 2. 如果不是单个PID，则将其视为一个或多个名称/关键字列表
	//    用逗号和空格作为分隔符
	targets := strings.Fields(strings.ReplaceAll(target, ",", " "))
	if len(targets) == 0 {
		return 0, "错误: 请提供至少一个 PID、进程名或关键字。"
	}

	// 3. 获取所有进程列表，只获取一次，提高效率
	procs, err := process.Processes()
	if err != nil {
		return 0, fmt.Sprintf("无法获取进程列表: %v", err)
	}

	// 4. 遍历所有进程，与目标列表进行匹配
	foundCount := 0
	var foundDetails strings.Builder

	for _, p := range procs {
		name, err := p.Name()
		if err != nil {
			continue
		}
		nameLower := strings.ToLower(name)

		// 检查当前进程名是否与任何一个搜索目标匹配
		for _, t := range targets {
			targetLower := strings.ToLower(t)

			// ★★★ 核心逻辑：使用 strings.Contains 进行关键字模糊匹配 ★★★
			if strings.Contains(nameLower, targetLower) {
				foundCount++
				exePath, _ := p.Exe()
				// 在输出中明确是哪个关键字匹配到的
				foundDetails.WriteString(fmt.Sprintf("  - [匹配: %s] PID: %-8d 名称: %s, 路径: %s\n", t, p.Pid, name, exePath))
				// 一个进程可能匹配多个关键字，但我们只记录一次，所以找到后就跳出内层循环
				break
			}
		}
	}

	if foundCount > 0 {
		return foundCount, fmt.Sprintf("找到 %d 个匹配的进程:\n%s", foundCount, foundDetails.String())
	}

	return 0, fmt.Sprintf("未找到任何与 '%s' 相关的活动进程。", target)
}

// KillProcess 根据 PID 或进程名杀死进程。
func KillProcess(target string) (string, error) {
	// 尝试将输入解析为PID
	pid, err := strconv.Atoi(target)
	if err == nil {
		p, err := process.NewProcess(int32(pid))
		if err != nil {
			return "", fmt.Errorf("未找到PID为 %d 的进程: %w", pid, err)
		}
		name, _ := p.Name()
		if err := p.Kill(); err != nil {
			return "", fmt.Errorf("杀死进程 %d (%s) 失败: %w", pid, name, err)
		}
		return fmt.Sprintf("成功: 已终止进程 PID: %d, 名称: %s。", pid, name), nil
	}

	// 按进程名处理
	procs, err := process.Processes()
	if err != nil {
		return "", fmt.Errorf("无法获取进程列表: %w", err)
	}

	killedCount := 0
	var lastError error
	targetLower := strings.ToLower(target)

	for _, p := range procs {
		name, err := p.Name()
		if err != nil {
			continue
		}
		if strings.ToLower(name) == targetLower {
			pidToKill := p.Pid
			if err := p.Kill(); err != nil {
				lastError = fmt.Errorf("终止 PID %d 时失败: %w", pidToKill, err)
			} else {
				killedCount++
			}
		}
	}

	if killedCount > 0 {
		return fmt.Sprintf("成功: 已终止 %d 个名为 '%s' 的进程。", killedCount, target), nil
	}

	if lastError != nil {
		return "", fmt.Errorf("尝试杀死进程 '%s' 时发生错误: %w", target, lastError)
	}

	return "", fmt.Errorf("未找到名为 '%s' 的活动进程。", target)
}
