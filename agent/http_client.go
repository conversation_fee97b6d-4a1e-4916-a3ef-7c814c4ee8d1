package main

import (
	"agent/global"
	"context"
	"crypto/tls"
	utls "github.com/refraction-networking/utls"
	"golang.org/x/net/http2"
	"net"
	"net/http"
	"strings"
	"time"
)

// ★ 智能HTTP客户端：HTTP用HTTP/1.1，HTTPS用HTTP/2
var (
	httpClient  *http.Client // HTTP客户端 (HTTP/1.1)
	httpsClient *http.Client // HTTPS客户端 (HTTP/2 + uTLS)
)

// 初始化HTTP客户端
func init() {
	// HTTP客户端（HTTP/1.1 only）
	httpClient = &http.Client{
		Transport: &http.Transport{
			DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
				return (&net.Dialer{Timeout: 10 * time.Second}).DialContext(ctx, network, addr)
			},
		},
		Timeout: 30 * time.Second,
	}

	// HTTPS客户端（HTTP/2 + uTLS）
	httpsClient = &http.Client{
		Transport: createHTTP2Transport(),
		Timeout:   30 * time.Second,
	}
}

// createHTTP2Transport 创建HTTP/2传输层（使用uTLS）
func createHTTP2Transport() http.RoundTripper {
	// 创建HTTP/2传输层
	transport := &http2.Transport{
		// 使用自定义的TLS拨号函数
		DialTLS: func(network, addr string, cfg *tls.Config) (net.Conn, error) {
			tcp, err := net.DialTimeout(network, addr, 10*time.Second)
			if err != nil {
				return nil, err
			}

			// 使用uTLS进行TLS握手
			serverName := strings.Split(addr, ":")[0]
			utlsConfig := &utls.Config{
				ServerName:         serverName,
				InsecureSkipVerify: true, // 跳过证书验证
			}

			u := utls.UClient(tcp, utlsConfig, utls.HelloChrome_Auto)
			if err = u.Handshake(); err != nil {
				tcp.Close()
				return nil, err
			}
			return u, nil
		},
		// 允许HTTP/2
		AllowHTTP: false, // 只允许HTTPS
	}

	return transport
}

// getHTTPClient 根据URL选择合适的HTTP客户端
func getHTTPClient(url string) *http.Client {
	if strings.HasPrefix(url, "https://") {
		return httpsClient
	}
	return httpClient
}

// Send 保留原 Request（Method/URL/Header 都不变），但走 uTLS+Chrome UA
func Send(req *http.Request) (*http.Response, error) {
	// 如业务代码自己已经加了 UA 就不覆盖；否则补一个
	if req.Header.Get("User-Agent") == "" {
		req.Header.Set("User-Agent",
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36")
	}
	// 使用智能HTTP请求（先尝试严格验证，失败后跳过验证）
	return global.SmartHTTPRequest(req)
}
