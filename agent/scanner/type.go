// scanner/types.go
package scanner

// ScanResult 定义了扫描结果的结构
type ScanResult struct {
	Address  string    `json:"address"`
	Protocol string    `json:"protocol"`
	Banner   string    `json:"banner"`
	Risk     RiskLevel `json:"risk"`
	Error    error     `json:"-"`
}

type RiskLevel string

const (
	RiskHigh   RiskLevel = "High"
	RiskMedium RiskLevel = "Medium"
	RiskInfo   RiskLevel = "Info"
)

type Scanner interface {
	Name() string
	Ports() []int
	Scan(target Target, resultsChan chan<- ScanResult)
}

type Target struct {
	IP   string
	Port int
}
