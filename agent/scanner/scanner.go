// scanner/scanner.go
package scanner

import (
	"context"
	"fmt"
	"log"
	"net"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/malfunkt/iprange"
	"golang.org/x/time/rate"
)

// allScanners 是我们所有扫描器插件的注册表
var allScanners = []Scanner{
	&SSHScanner{},
	&SMBScanner{},
	&RDPScanner{},
	&MySQLScanner{},
	&PostgreSQLScanner{},
	&RedisScanner{},
	&HTTPScanner{},
	&FTPScanner{},
	&MSSQLScanner{},
}

func RunScan(targetRange string) string {
	log.Printf("[SCAN] 开始扫描目标: %s", targetRange)

	ips, err := iprange.ParseList(targetRange)
	if err != nil {
		return fmt.Sprintf("错误: 无效的目标格式: %v", err)
	}

	ipList := ips.Expand()
	if len(ipList) > 1024 {
		return fmt.Sprintf("错误: 目标IP数量过大 (%d > 1024)，请缩小范围。", len(ipList))
	}
	log.Printf("[SCAN] 共计 %d 个IP地址，%d 个扫描模块待执行。", len(ipList), len(allScanners))

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	limiter := rate.NewLimiter(rate.Limit(200), 100)
	var wg sync.WaitGroup
	resultsChan := make(chan ScanResult, 200)

	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	for _, ip := range ipList {
		wg.Add(1)
		go func(targetIP net.IP) {
			defer wg.Done()
			scanHost(ctx, limiter, targetIP.String(), resultsChan)
		}(ip)
	}

	var results []ScanResult
	for res := range resultsChan {
		if res.Error == nil {
			results = append(results, res)
		} else {
			log.Printf("[SCAN-ERROR] %s: %v", res.Address, res.Error)
		}
	}

	sort.Slice(results, func(i, j int) bool {
		if results[i].Risk != results[j].Risk {
			return results[i].Risk == RiskHigh || (results[i].Risk == RiskMedium && results[j].Risk == RiskInfo)
		}
		return results[i].Address < results[j].Address
	})

	if len(results) == 0 {
		return "扫描完成，未发现任何有价值的目标。"
	}

	var output strings.Builder
	output.WriteString(fmt.Sprintf("对 %s 的扫描结果:\n", targetRange))
	for _, res := range results {
		var riskTag string
		switch res.Risk {
		case RiskHigh:
			riskTag = "[高危]"
		case RiskMedium:
			riskTag = "[中危]"
		case RiskInfo:
			riskTag = "[信息]"
		}
		output.WriteString(fmt.Sprintf("[+] %-21s | %-11s | %-7s %s\n", res.Address, res.Protocol, riskTag, res.Banner))
	}

	return output.String()
}

func scanHost(ctx context.Context, limiter *rate.Limiter, ip string, resultsChan chan<- ScanResult) {
	var wg sync.WaitGroup
	portsToScan := make(map[int]struct{})
	for _, scanner := range allScanners {
		for _, port := range scanner.Ports() {
			portsToScan[port] = struct{}{}
		}
	}

	for port := range portsToScan {
		select {
		case <-ctx.Done():
			return
		default:
		}

		wg.Add(1)
		go func(p int) {
			defer wg.Done()
			if err := limiter.Wait(ctx); err != nil {
				return
			}

			target := Target{IP: ip, Port: p}
			address := fmt.Sprintf("%s:%d", ip, p)
			conn, err := net.DialTimeout("tcp", address, 2*time.Second)
			if err != nil {
				return
			}
			conn.Close()

			for _, scanner := range allScanners {
				for _, sp := range scanner.Ports() {
					if sp == p {
						scanner.Scan(target, resultsChan)
					}
				}
			}
		}(port)
	}
	wg.Wait()
}

func checkService(ip string, port int, resultsChan chan<- ScanResult) {
	// 这个函数在新的结构中不再需要，因为逻辑分散到各个scanner的Scan方法中了
	// 为了保持兼容性，可以留空或删除
}
