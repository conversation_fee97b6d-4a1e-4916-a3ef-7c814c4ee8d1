// scanner/protocols.go
package scanner

import (
	"crypto/tls"
	"database/sql"
	"fmt"
	"io"
	"net"
	"net/http"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
	_ "github.com/lib/pq"
	"golang.org/x/crypto/ssh"
	"golang.org/x/net/html"
)

var (
	sshUsers = []string{"root", "admin", "test"}
	sshPass  = []string{"123456", "password", "root", "admin"}

	mysqlUsers = []string{"root"}
	mysqlPass  = []string{"", "123456", "root"}

	pqUsers = []string{"postgres"}
	pqPass  = []string{"123456", "postgres"}

	mssqlUsers = []string{"sa"}
	mssqlPass  = []string{"123456", "sa", "password"}
)

// --- FTP ---
type FTPScanner struct{}

func (s *FTPScanner) Name() string { return "FTP Anonymous Login" }
func (s *FTPScanner) Ports() []int { return []int{21} }
func (s *FTPScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	resultsChan <- ScanResult{Address: address, Protocol: "FTP", Risk: RiskInfo, Banner: "FTP服务开放，请尝试匿名登录"}
}

// --- SSH ---
type SSHScanner struct{}

func (s *SSHScanner) Name() string { return "SSH Weak Password" }
func (s *SSHScanner) Ports() []int { return []int{22} }
func (s *SSHScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	for _, user := range sshUsers {
		for _, pass := range sshPass {
			config := &ssh.ClientConfig{
				User: user, Auth: []ssh.AuthMethod{ssh.Password(pass)},
				HostKeyCallback: ssh.InsecureIgnoreHostKey(), Timeout: 3 * time.Second,
			}
			client, err := ssh.Dial("tcp", address, config)
			if err == nil {
				client.Close()
				resultsChan <- ScanResult{Address: address, Protocol: "SSH", Risk: RiskHigh, Banner: fmt.Sprintf("弱口令爆破成功: %s / %s", user, pass)}
				return
			}
			if err != nil && strings.Contains(strings.ToLower(err.Error()), "authenticate") {
				continue
			}
			break
		}
	}
}

// --- SMB ---
type SMBScanner struct{}

func (s *SMBScanner) Name() string { return "SMB MS17-010" }
func (s *SMBScanner) Ports() []int { return []int{445} }
func (s *SMBScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	resultsChan <- ScanResult{Address: address, Protocol: "SMB", Risk: RiskMedium, Banner: "SMB服务开放，请检查MS17-010等漏洞"}
}

// --- RDP ---
type RDPScanner struct{}

func (s *RDPScanner) Name() string { return "RDP Service" }
func (s *RDPScanner) Ports() []int { return []int{3389} }
func (s *RDPScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	resultsChan <- ScanResult{Address: fmt.Sprintf("%s:%d", target.IP, target.Port), Protocol: "RDP", Risk: RiskInfo, Banner: "RDP服务开放"}
}

// --- MSSQL ---
type MSSQLScanner struct{}

func (s *MSSQLScanner) Name() string { return "MSSQL Weak Password" }
func (s *MSSQLScanner) Ports() []int { return []int{1433} }
func (s *MSSQLScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	resultsChan <- ScanResult{Address: fmt.Sprintf("%s:%d", target.IP, target.Port), Protocol: "MSSQL", Risk: RiskMedium, Banner: "MSSQL服务开放，请手动尝试sa弱口令"}
}

// --- MySQL ---
type MySQLScanner struct{}

func (s *MySQLScanner) Name() string { return "MySQL Weak Password" }
func (s *MySQLScanner) Ports() []int { return []int{3306} }
func (s *MySQLScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	for _, user := range mysqlUsers {
		for _, pass := range mysqlPass {
			dsn := fmt.Sprintf("%s:%s@tcp(%s)/?timeout=3s", user, pass, address)
			db, err := sql.Open("mysql", dsn)
			if err != nil {
				continue
			}
			if err = db.Ping(); err == nil {
				db.Close()
				resultsChan <- ScanResult{Address: address, Protocol: "MySQL", Risk: RiskHigh, Banner: fmt.Sprintf("弱口令爆破成功: %s / %s", user, pass)}
				return
			}
			db.Close()
		}
	}
}

// --- PostgreSQL ---
type PostgreSQLScanner struct{}

func (s *PostgreSQLScanner) Name() string { return "PostgreSQL Weak Password" }
func (s *PostgreSQLScanner) Ports() []int { return []int{5432} }
func (s *PostgreSQLScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	for _, user := range pqUsers {
		for _, pass := range pqPass {
			dsn := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=postgres sslmode=disable connect_timeout=3", target.IP, target.Port, user, pass)
			db, err := sql.Open("postgres", dsn)
			if err != nil {
				continue
			}
			if err = db.Ping(); err == nil {
				db.Close()
				resultsChan <- ScanResult{Address: address, Protocol: "PostgreSQL", Risk: RiskHigh, Banner: fmt.Sprintf("弱口令爆破成功: %s / %s", user, pass)}
				return
			}
			db.Close()
		}
	}
}

// --- Redis ---
type RedisScanner struct{}

func (s *RedisScanner) Name() string { return "Redis Unauthorized" }
func (s *RedisScanner) Ports() []int { return []int{6379} }
func (s *RedisScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	address := fmt.Sprintf("%s:%d", target.IP, target.Port)
	conn, err := net.DialTimeout("tcp", address, 2*time.Second)
	if err != nil {
		return
	}
	defer conn.Close()
	conn.SetWriteDeadline(time.Now().Add(2 * time.Second))
	if _, err = conn.Write([]byte("*1\r\n$4\r\nINFO\r\n")); err != nil {
		return
	}
	conn.SetReadDeadline(time.Now().Add(2 * time.Second))
	resp := make([]byte, 1024)
	n, err := conn.Read(resp)
	if err != nil || n == 0 {
		return
	}
	if strings.Contains(string(resp[:n]), "redis_version") {
		resultsChan <- ScanResult{Address: address, Protocol: "Redis", Risk: RiskHigh, Banner: "存在Redis未授权访问漏洞!"}
	}
}

// --- HTTP ---
type HTTPScanner struct{}

func (s *HTTPScanner) Name() string { return "HTTP Title Fetcher" }
func (s *HTTPScanner) Ports() []int { return []int{80, 443, 8080, 8081, 8443, 9200, 7001} }
func (s *HTTPScanner) Scan(target Target, resultsChan chan<- ScanResult) {
	schema := "http"
	if target.Port == 443 || target.Port == 8443 {
		schema = "https"
	}
	url := fmt.Sprintf("%s://%s:%d", schema, target.IP, target.Port)

	tr := &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}}
	client := &http.Client{Transport: tr, Timeout: 5 * time.Second, CheckRedirect: func(req *http.Request, via []*http.Request) error { return http.ErrUseLastResponse }}

	resp, err := client.Get(url)
	if err != nil {
		return
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(io.LimitReader(resp.Body, 256*1024))
	if err != nil {
		return
	}

	title := getTitle(string(body))
	serverHeader := resp.Header.Get("Server")
	banner := fmt.Sprintf("Title: %s (Status: %s, Server: %s)", title, resp.Status, serverHeader)
	resultsChan <- ScanResult{Address: url, Protocol: "HTTP/S", Risk: RiskInfo, Banner: banner}
}

func getTitle(body string) string {
	doc, err := html.Parse(strings.NewReader(body))
	if err != nil {
		return "N/A"
	}
	var title string
	var f func(*html.Node)
	f = func(n *html.Node) {
		if n.Type == html.ElementNode && n.Data == "title" && n.FirstChild != nil {
			title = n.FirstChild.Data
			return
		}
		for c := n.FirstChild; c != nil && title == ""; c = c.NextSibling {
			f(c)
		}
	}
	f(doc)
	return strings.TrimSpace(title)
}
