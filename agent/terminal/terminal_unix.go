//go:build linux || darwin

package terminal

import (
	"io"
	"os"
	"os/exec"
	"runtime"

	"github.com/creack/pty"
)

// startShell 在Unix-like系统上启动一个交互式shell
func startShell() (io.ReadWriteCloser, error) {
	shell := "/bin/sh"
	if runtime.GOOS == "darwin" {
		shell = "/bin/zsh" // macOS 默认使用 zsh
	}
	cmd := exec.Command(shell, "-i")
	return pty.Start(cmd)
}

// resizeShell 在Unix-like系统上调整PTY大小
func resizeShell(ptmx io.ReadWriteCloser, cols, rows uint16) error {
	if p, ok := ptmx.(*os.File); ok {
		return pty.Setsize(p, &pty.Winsize{
			Rows: rows,
			Cols: cols,
		})
	}
	return nil // 如果不是*os.File类型，忽略调整大小请求
}