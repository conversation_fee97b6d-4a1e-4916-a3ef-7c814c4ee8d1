package need

import (
	"bytes"
	"os/exec"
)

const STRING_OBFUSCATION_KEY = 0xAB

// runExe 运行指定路径的可执行文件
func RunExe(path string) error {
	cmd := exec.Command(path)
	cmd.Start()
	return nil
}

// deobfuscateString 从片段中重构并解混淆字符串
func DeobfuscateString(fragments [][]byte) string {
	var buf bytes.Buffer
	for _, frag := range fragments {
		for _, b := range frag {
			buf.WriteByte(b ^ STRING_OBFUSCATION_KEY)
		}
	}
	return buf.String()
}
