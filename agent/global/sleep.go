package global

import (
	"math/rand"
	"time"
)

// RandSleep returns a randomized duration around SleepDuration
// using a clipped normal distribution defined by Ji<PERSON>.
//
//	actual = SleepDuration  ± ~0.5·Jitter (95%% coverage)
//
// If r is nil a new source is created.
func RandSleep(r *rand.Rand) time.Duration {
	if r == nil {
		r = rand.New(rand.NewSource(time.Now().UnixNano()))
	}
	if Jitter == 0 {
		return SleepDuration
	}
	sigma := float64(Jitter) * 0.5 // 95% of values within ±Jitter
	delta := time.Duration(r.NormFloat64() * sigma)
	if delta < -Jitter {
		delta = -Jitter
	} else if delta > Jitter {
		delta = Jitter
	}
	return SleepDuration + delta
}
