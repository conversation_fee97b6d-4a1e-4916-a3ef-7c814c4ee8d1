/* WinNexus C2 - Supershell风格主题 */

:root {
    --primary-color: #ff6b35;
    --primary-color-dark: #e55a2b;
    --accent-hover: #ff7f57;
    --success-color: #10b981;
    --error-color: #ef4444;
    --warning-color: #f59e0b;
    --interactive-color: #6366f1;

    --bg-main: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-card: #f8f9fa;
    --bg-card-hover: #f1f3f4;
    --bg-panel: #f8f9fa;
    --bg-input: #ffffff;

    --border-color: #e9ecef;
    --border-light: #f1f3f4;

    --text-title: #212529;
    --text-primary: #495057;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;

    --terminal-bg: #0d1117;
    --terminal-fg: #c9d1d9;
    --terminal-cursor: #58a6ff;
    --terminal-selection: #264f78;

    --status-online: #10b981;
    --status-offline: #ef4444;

    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 10px 15px rgba(0, 0, 0, 0.1);

    --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", "Microsoft YaHei", Roboto, "Helvetica Neue", Arial, sans-serif;
    --font-mono: "SF Mono", "Monaco", "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;

    --radius-sm: 4px;
    --radius-md: 6px;
    --radius-lg: 8px;

    --transition-fast: 0.15s ease;
    --transition-normal: 0.2s ease;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: var(--font-sans);
    background: var(--bg-main);
    color: var(--text-primary);
    font-size: 14px;
    line-height: 1.5;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}
::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}
::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* === 登录页面样式 === */
.login-body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: var(--bg-secondary);
}

.login-container {
    background: var(--bg-input);
    padding: 40px;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    width: 380px;
    text-align: center;
    border: 1px solid var(--border-color);
}

.login-container h2 {
    font-size: 2.5em;
    font-weight: 300;
    color: var(--text-title);
    margin: 0 0 40px;
    letter-spacing: -0.5px;
}

.login-container input {
    width: 100%;
    margin-bottom: 20px;
    padding: 12px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition-fast);
}

.login-container input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.login-container button {
    width: 100%;
    padding: 14px;
    font-size: 14px;
    font-weight: 500;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
}

.login-container button:hover {
    background: var(--primary-color-dark);
}

.error-text {
    color: var(--error-color);
    min-height: 1.2em;
    margin-top: 20px;
    font-size: 14px;
}

/* === 顶部导航栏 === */
.navbar {
    background: var(--bg-input);
    border-bottom: 1px solid var(--border-color);
    padding: 0 24px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.navbar-brand h1 {
    font-size: 1.4em;
    font-weight: 500;
    color: var(--text-title);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.navbar-brand span {
    color: var(--text-secondary);
    font-size: 0.9em;
    margin-left: 15px;
}

.navbar-controls {
    display: flex;
    align-items: center;
    gap: 20px;
}

.search-container {
    display: flex;
    gap: 15px;
    align-items: center;
}

.search-container input {
    width: 280px;
    padding: 10px 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    transition: var(--transition-normal);
}

.search-container input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.1);
}

.search-container select {
    padding: 10px 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    min-width: 140px;
}

.navbar-buttons {
    display: flex;
    gap: 10px;
}

.nav-btn {
    padding: 10px 20px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 14px;
    font-weight: 500;
}

.nav-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.nav-btn.logout:hover {
    background: var(--error-color);
    border-color: var(--error-color);
}

/* === 主要内容区域 === */
.main-content {
    padding: 30px;
    min-height: calc(100vh - 70px);
}

.view {
    display: none;
}

.view.active {
    display: block;
}

/* === 客户端网格布局 === */
.clients-container {
    width: 100%;
    margin: 0 auto;
}

#clients-grid {
    padding: 20px;
    overflow-x: auto;
    width: 100%;
}

/* === 客户端表格样式 === */
.agents-table {
    width: 100%;
    min-width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    font-size: 13px;
    table-layout: fixed;
}

.agents-table thead {
    background: var(--bg-secondary);
}

.agents-table th {
    padding: 15px 12px;
    text-align: left;
    font-weight: 600;
    color: var(--text-title);
    border-bottom: 2px solid var(--border-color);
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 设置各列的宽度 */
.agents-table th:nth-child(1) { width: 60px; }   /* 状态 */
.agents-table th:nth-child(2) { width: 120px; }  /* 主机名 */
.agents-table th:nth-child(3) { width: 100px; }  /* 用户 */
.agents-table th:nth-child(4) { width: 80px; }   /* 操作系统 */
.agents-table th:nth-child(5) { width: 60px; }   /* 架构 */
.agents-table th:nth-child(6) { width: 100px; }  /* 主IP */
.agents-table th:nth-child(7) { width: 120px; }  /* 所有IP */
.agents-table th:nth-child(8) { width: 100px; }  /* CPU */
.agents-table th:nth-child(9) { width: 80px; }   /* 内存 */
.agents-table th:nth-child(10) { width: 120px; } /* 最后心跳 */
.agents-table th:nth-child(11) { width: 150px; } /* 进程信息 */
.agents-table th:nth-child(12) { width: 100px; } /* Agent ID */
.agents-table th:nth-child(13) { width: 120px; } /* 备注 */
.agents-table th:nth-child(14) { width: 100px; } /* 操作 */

.agents-table tbody tr {
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition-fast);
    cursor: pointer;
}

.agents-table tbody tr:hover {
    background: var(--bg-card-hover);
}

.agents-table tbody tr.online {
    border-left: 4px solid var(--status-online);
}

.agents-table tbody tr.offline {
    border-left: 4px solid var(--status-offline);
}

.agents-table td {
    padding: 12px;
    vertical-align: top;
    border-bottom: 1px solid var(--border-color);
    white-space: normal;
    word-break: break-all;
}

/* 允许某些列换行 */
.agents-table td.process-cell,
.agents-table td.notes-cell {
    white-space: normal;
}

/* 状态列 */
.status-cell .status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 11px;
    font-weight: 600;
}

.status-indicator.online {
    background: rgba(72, 187, 120, 0.1);
    color: var(--status-online);
}

.status-indicator.offline {
    background: rgba(229, 62, 62, 0.1);
    color: var(--status-offline);
}

/* 主机名列 */
.hostname-cell strong {
    color: var(--text-title);
    font-weight: 600;
}

/* 用户列 */
.user-cell {
    color: var(--text-primary);
}

.admin-badge {
    font-size: 12px;
    margin-left: 5px;
}

/* IP地址列 */
.ip-cell code,
.all-ips-cell code {
    background: var(--bg-input);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: var(--font-mono);
    font-size: 11px;
    color: var(--primary-color);
}

.ip-list {
    max-width: 100%;
    display: block;
    white-space: normal;
    word-break: break-all;
    max-height: 60px;
    overflow-y: auto;
    padding-right: 4px;
}

.all-ips-cell {
    white-space: normal;
    word-break: break-all;
    vertical-align: top;
}

/* CPU和内存列 */
.cpu-cell,
.ram-cell {
    font-family: var(--font-mono);
    font-size: 12px;
    color: var(--text-secondary);
}

/* 时间列 */
.lastseen-cell small {
    color: var(--text-muted);
    font-size: 11px;
}

/* 进程信息列 */
.process-cell {
    max-width: 180px;
}

.process-info div {
    margin-bottom: 2px;
}

.process-info strong {
    color: var(--text-title);
    font-size: 12px;
}

.process-path {
    color: var(--text-secondary);
    font-family: var(--font-mono);
    font-size: 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.process-pid {
    color: var(--text-muted);
    font-size: 10px;
}

/* Agent ID列 */
.id-cell .agent-id {
    background: var(--bg-input);
    padding: 2px 6px;
    border-radius: 3px;
    font-family: var(--font-mono);
    font-size: 10px;
    color: var(--warning-color);
}

/* 备注列 */
.notes-cell {
    max-width: 150px;
    color: var(--text-secondary);
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 操作列 */
.actions-cell {
    width: 120px;
}

.action-buttons {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.action-buttons .action-btn {
    padding: 4px 8px;
    font-size: 12px;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-buttons .action-btn.primary {
    background: var(--primary-color);
}

.action-buttons .action-btn.primary:hover {
    background: var(--primary-color-dark);
}

.client-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 24px;
    transition: var(--transition-fast);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.client-card:hover {
    background: var(--bg-card-hover);
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.client-card.online {
    border-left: 4px solid var(--status-online);
}

.client-card.offline {
    border-left: 4px solid var(--status-offline);
    opacity: 0.7;
}

.client-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.client-info h3 {
    font-size: 1.3em;
    font-weight: 600;
    color: var(--text-title);
    margin: 0 0 5px 0;
}

.client-info .client-id {
    font-size: 0.85em;
    color: var(--text-muted);
    font-family: var(--font-mono);
}

.status-indicator {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-indicator.online {
    background: rgba(72, 187, 120, 0.2);
    color: var(--status-online);
}

.status-indicator.offline {
    background: rgba(229, 62, 62, 0.2);
    color: var(--status-offline);
}

.client-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    font-size: 0.9em;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-label {
    color: var(--text-secondary);
    font-size: 0.8em;
    margin-bottom: 2px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 500;
}

.client-actions {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.action-btn {
    padding: 8px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    font-size: 0.85em;
    cursor: pointer;
    transition: var(--transition-fast);
}

.action-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.action-btn.danger:hover {
    background: var(--error-color);
    border-color: var(--error-color);
}

/* 空状态 */
.no-clients {
    text-align: center;
    padding: 80px 20px;
}

.empty-state h2 {
    font-size: 2em;
    color: var(--text-secondary);
    margin-bottom: 15px;
    font-weight: 300;
}

.empty-state p {
    color: var(--text-muted);
    font-size: 1.1em;
}

/* === 客户端详细视图 === */
.detail-header {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 25px;
    margin-bottom: 25px;
    display: flex;
    align-items: center;
    gap: 25px;
}

.back-btn {
    padding: 10px 20px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.back-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.agent-info {
    flex: 1;
    display: flex;
    align-items: center;
    gap: 20px;
}

.agent-info h2 {
    font-size: 1.8em;
    font-weight: 600;
    color: var(--text-title);
    margin: 0;
}

.agent-id {
    font-family: var(--font-mono);
    color: var(--text-muted);
    font-size: 0.9em;
}

.mode-control {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-left: auto;
}

.status-badge {
    padding: 8px 16px;
    background: rgba(72, 187, 120, 0.2);
    color: var(--status-online);
    border-radius: 20px;
    font-size: 0.85em;
    font-weight: 600;
}

.mode-btn {
    padding: 8px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.9em;
}

.mode-btn:hover {
    background: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

/* === S5代理控制样式 === */
.s5-control {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-top: 15px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-md);
}

.s5-status-section {
    display: flex;
    align-items: center;
    gap: 10px;
}

.s5-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9em;
}

.s5-info {
    font-size: 0.8em;
    color: var(--text-muted);
}

.s5-buttons {
    display: flex;
    gap: 10px;
    margin-left: auto;
}

.s5-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--bg-input);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.85em;
    font-weight: 500;
}

.s5-start-btn {
    background: rgba(72, 187, 120, 0.1);
    border-color: var(--success-color);
    color: var(--success-color);
}

.s5-start-btn:hover {
    background: var(--success-color);
    color: white;
}

.s5-stop-btn {
    background: rgba(229, 62, 62, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.s5-stop-btn:hover {
    background: var(--error-color);
    color: white;
}

.s5-status-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

.s5-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.s5-btn:disabled:hover {
    background: var(--bg-input);
    border-color: var(--border-color);
    color: var(--text-primary);
}

.status-badge.s5-online {
    background: rgba(72, 187, 120, 0.2);
    color: var(--success-color);
    border: 1px solid var(--success-color);
}

.status-badge.s5-offline {
    background: rgba(229, 62, 62, 0.2);
    color: var(--error-color);
    border: 1px solid var(--error-color);
}

.detail-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.notes-section {
    padding: 25px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.notes-section textarea {
    flex: 1;
    min-height: 80px;
    padding: 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    resize: vertical;
    font-family: var(--font-sans);
}

.notes-section button {
    padding: 15px 25px;
    background: var(--primary-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
}

.notes-section button:hover {
    background: var(--primary-color-dark);
}

/* === 操作标签页 === */
.operations-tabs {
    display: flex;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    padding: 8px;
    margin-bottom: 25px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-md);
}

.tab-link {
    flex: 1;
    padding: 15px 25px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    border-radius: var(--radius-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-weight: 600;
    text-align: center;
    font-size: 0.95em;
    position: relative;
    overflow: hidden;
}

.tab-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    opacity: 0;
    transition: opacity 0.3s;
    border-radius: var(--radius-md);
}

.tab-link:hover {
    color: var(--text-primary);
    background: var(--bg-card-hover);
    transform: translateY(-1px);
}

.tab-link.active {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 170, 255, 0.3);
}

.tab-link.active::before {
    opacity: 1;
}

/* 标签页内容显示控制 */
.tab-content {
    display: none;
    padding: 30px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    margin-bottom: 25px;
    box-shadow: var(--shadow-md);
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-content.active {
    display: block;
}

/* === SOCKS5代理管理样式 === */
.proxy-stats {
    display: flex;
    gap: 24px;
    margin-bottom: 20px;
    padding: 16px;
    background: var(--bg-input);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

/* 简化的SOCKS5代理样式 */
.s5-simple-controls {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 20px;
    margin-bottom: 20px;
}

.s5-config-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.s5-config-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.s5-config-item label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9em;
}

.s5-config-item input {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-input);
    color: var(--text-primary);
    font-size: 0.95em;
}

.s5-action-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.s5-simple-status {
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 20px;
}

.status-display {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.status-indicator {
    font-size: 1.5em;
    width: 30px;
    text-align: center;
}

.status-info {
    flex: 1;
}

.status-text {
    font-weight: 600;
    font-size: 1.1em;
    margin-bottom: 5px;
}

.connection-info {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.proxy-details-simple {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 15px;
    margin-top: 15px;
    display: none;
}

.proxy-details-simple.active {
    display: block;
}

.s5-btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9em;
}

.s5-start-btn {
    background: var(--success-color);
    color: white;
}

.s5-stop-btn {
    background: var(--error-color);
    color: white;
}

.s5-test-btn {
    background: var(--primary-color);
    color: white;
}

.s5-btn:hover:not(:disabled) {
    opacity: 0.8;
    transform: translateY(-1px);
}

.s5-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.stat-label {
    font-size: 0.85em;
    color: var(--text-secondary);
    font-weight: 500;
}

.stat-value {
    font-size: 1.5em;
    font-weight: 600;
    color: var(--primary-color);
}

.proxy-list-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-input);
}

.proxy-list {
    padding: 12px;
}

.proxy-list-empty {
    text-align: center;
    padding: 40px 20px;
    color: var(--text-secondary);
}

.proxy-list-empty p {
    margin: 0 0 8px 0;
    font-size: 1em;
}

.proxy-list-empty small {
    font-size: 0.85em;
    opacity: 0.8;
}

.proxy-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    margin-bottom: 8px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.proxy-item:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
}

.proxy-item:last-child {
    margin-bottom: 0;
}

.proxy-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.proxy-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.95em;
}

.proxy-details {
    font-size: 0.85em;
    color: var(--text-secondary);
}

.proxy-status {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-right: 12px;
}

.proxy-status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
}

.proxy-status-indicator.paused {
    background: var(--warning-color);
}

.proxy-status-indicator.stopped {
    background: var(--error-color);
}

.proxy-status-text {
    font-size: 0.85em;
    font-weight: 500;
}

.proxy-actions {
    display: flex;
    gap: 8px;
}

.proxy-action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.8em;
    font-weight: 500;
    transition: var(--transition-fast);
}

.proxy-action-btn.pause {
    background: var(--warning-color);
    color: white;
}

.proxy-action-btn.resume {
    background: var(--success-color);
    color: white;
}

.proxy-action-btn.stop {
    background: var(--error-color);
    color: white;
}

.proxy-action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.tab-content h3 {
    font-size: 1.4em;
    margin: 0 0 25px 0;
    color: var(--text-title);
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding-bottom: 15px;
    position: relative;
}

.tab-content h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    border-radius: 2px;
}

/* === 命令执行 === */
pre#output-area {
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    color: #00ff41;
    padding: 20px;
    height: 450px;
    overflow-y: auto;
    border-radius: var(--radius-lg);
    border: 1px solid rgba(0, 255, 65, 0.2);
    font-family: var(--font-mono);
    font-size: 12px;
    line-height: 1.4;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
}

pre#output-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
    opacity: 0.3;
}

pre#output-area::-webkit-scrollbar {
    width: 8px;
}

pre#output-area::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

pre#output-area::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
    opacity: 0.6;
}

pre#output-area::-webkit-scrollbar-thumb:hover {
    opacity: 1;
}

/* 时间戳样式 */
.timestamp {
    color: #7c3aed;
    font-weight: 500;
    opacity: 0.8;
}

.input-area {
    display: flex;
    gap: 15px;
}

#command-input {
    flex: 1;
    padding: 12px 18px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 14px;
}

#command-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.1);
}

#send-btn {
    padding: 12px 25px;
    background: var(--primary-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
}

#send-btn:hover {
    background: var(--primary-color-dark);
}

/* === 文件管理 === */
.file-browser-nav {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    align-items: center;
}

.file-browser-nav input {
    flex: 1;
    padding: 12px 18px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
}

.file-browser-nav button {
    padding: 12px 20px;
    background: var(--primary-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 500;
}

.file-browser-nav button:hover {
    background: var(--primary-color-dark);
}

.file-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    margin-bottom: 30px;
}

.screenshot-btn {
    padding: 15px 30px;
    background: var(--success-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
    font-size: 1em;
    margin-bottom: 20px;
}

.screenshot-btn:hover {
    background: #27ae60;
}

/* === 表格样式 === */
table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
}

th, td {
    padding: 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

th {
    background: var(--bg-input);
    color: var(--text-title);
    font-weight: 600;
    font-size: 0.9em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: sticky;
    top: 0;
}

tbody tr:hover {
    background: var(--bg-card-hover);
}

/* === 战利品管理 === */
.loot-controls {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
    align-items: flex-end;
}

.loot-controls textarea {
    flex: 1;
    padding: 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 14px;
    resize: vertical;
    min-height: 80px;
    max-height: 200px;
    line-height: 1.4;
}

.loot-controls textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.loot-controls button {
    padding: 15px 25px;
    background: var(--success-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
    white-space: nowrap;
}

.loot-controls button:hover {
    background: #27ae60;
    transform: translateY(-1px);
}

.loot-info {
    margin-bottom: 20px;
    padding: 15px;
    background: linear-gradient(135deg, rgba(0, 170, 255, 0.1), rgba(0, 170, 255, 0.05));
    border: 1px solid rgba(0, 170, 255, 0.2);
    border-radius: var(--radius-md);
    border-left: 4px solid var(--primary-color);
}

.loot-info p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.9em;
    line-height: 1.5;
}

.loot-info strong {
    color: var(--primary-color);
    font-weight: 600;
}

.loot-table-container {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.delete-btn {
    padding: 6px 12px !important;
    background: var(--error-color) !important;
    border: none !important;
    color: white !important;
    border-radius: var(--radius-sm) !important;
    cursor: pointer;
    transition: var(--transition-normal);
    font-size: 0.8em;
}

.delete-btn:hover {
    background: #c0392b !important;
}

/* === 设置页面 === */
.settings-header {
    display: flex;
    align-items: center;
    gap: 25px;
    margin-bottom: 30px;
}

.settings-header h2 {
    font-size: 2em;
    font-weight: 600;
    color: var(--text-title);
    margin: 0;
}

.settings-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.settings-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 30px;
}

.settings-section h3 {
    font-size: 1.5em;
    margin: 0 0 15px 0;
    color: var(--text-title);
    font-weight: 600;
}

.settings-section p {
    color: var(--text-secondary);
    margin-bottom: 30px;
    line-height: 1.6;
}

.settings-form {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: var(--text-title);
    font-weight: 600;
    font-size: 0.95em;
}

.form-group input,
.form-group textarea {
    padding: 15px 20px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition-normal);
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
    font-family: var(--font-mono);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.form-hint {
    font-size: 0.8em;
    color: var(--text-muted);
    font-style: italic;
    margin-top: 4px;
}

.form-group input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
}

.form-group label:has(input[type="checkbox"]) {
    flex-direction: row;
    align-items: center;
    cursor: pointer;
}

.watchlist-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

.secondary-btn {
    padding: 8px 16px;
    background: var(--bg-input);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.85em;
    transition: var(--transition-fast);
}

.secondary-btn:hover {
    background: var(--bg-secondary);
    border-color: var(--primary-color);
}

.save-btn {
    padding: 15px 30px;
    background: var(--success-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    font-weight: 600;
    font-size: 1em;
    align-self: flex-start;
}

.save-btn:hover {
    background: #27ae60;
    transform: translateY(-1px);
}

.test-btn {
    padding: 12px 24px;
    background: var(--info-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 600;
    font-size: 0.9em;
}

.test-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.export-btn {
    padding: 12px 24px;
    background: var(--warning-color);
    border: none;
    color: white;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 600;
    font-size: 0.9em;
}

.export-btn:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

.ip-whitelist-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    flex-wrap: wrap;
}

/* === 标签页布局 === */
.settings-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-color);
    margin-bottom: 0;
    background: var(--bg-secondary);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.tab-btn {
    padding: 16px 24px;
    background: transparent;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 1em;
    font-weight: 500;
    transition: var(--transition-fast);
    border-bottom: 3px solid transparent;
    position: relative;
}

.tab-btn:hover {
    background: var(--bg-card-hover);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--bg-card);
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* 设置页面的标签页内容样式 */
.panel-page .settings-content .tab-content {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--radius-lg) var(--radius-lg);
    height: calc(100vh - 300px);
    min-height: 450px;
    max-height: 600px;
    overflow: hidden;
    display: block !important; /* 确保显示 */
}

/* 移除复杂的客户端页面特殊样式，使用原始简单样式 */

.panel-page .settings-content .tab-panel {
    display: none !important;
    padding: 24px;
    height: 100%;
    overflow-y: auto;
}

.panel-page .settings-content .tab-panel.active {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.settings-panel {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    gap: 32px;
    height: 100%;
    visibility: visible !important;
}

.panel-left,
.panel-right {
    display: flex !important;
    flex-direction: column;
    visibility: visible !important;
}

.panel-left h3,
.panel-right h4 {
    margin: 0 0 8px 0;
    color: var(--text-primary);
    font-size: 1.3em;
    font-weight: 600;
}

.panel-left p {
    margin: 0 0 24px 0;
    color: var(--text-secondary);
    font-size: 0.95em;
    line-height: 1.5;
}

.panel-right h4 {
    font-size: 1.1em;
    margin-bottom: 16px;
}

.form-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-section h4 {
    margin: 16px 0 8px 0;
    color: var(--text-primary);
    font-size: 1em;
    font-weight: 600;
    padding-top: 12px;
    border-top: 1px solid var(--border-color);
}

.form-section:first-child h4 {
    margin-top: 0;
    padding-top: 0;
    border-top: none;
}

.settings-content .form-group {
    margin-bottom: 12px;
    display: block !important;
    visibility: visible !important;
}

.settings-content .form-group:last-child {
    margin-bottom: 0;
}

.settings-content .form-group label {
    display: block !important;
    margin-bottom: 6px;
    font-weight: 500;
    color: var(--text-primary);
}

.settings-content .form-group input,
.settings-content .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-input);
    color: var(--text-primary);
    font-size: 0.9em;
}

.form-hint {
    display: block;
    margin-top: 4px;
    font-size: 0.85em;
    color: var(--text-secondary);
    line-height: 1.3;
}



.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
    margin-bottom: 16px;
}

.form-row .form-group {
    margin-bottom: 0;
}

.button-group {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-top: 16px;
}

.stat-display {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
}

/* === 新的按钮样式 === */
.btn-primary {
    padding: 12px 24px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition-fast);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-secondary {
    padding: 10px 20px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition-fast);
}

.btn-secondary:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
}

.btn-warning {
    padding: 10px 20px;
    background: var(--warning-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition-fast);
}

.btn-warning:hover {
    background: #e67e22;
    transform: translateY(-1px);
}

.btn-small {
    padding: 6px 12px;
    background: var(--bg-input);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 0.85em;
    font-weight: 500;
    transition: var(--transition-fast);
}

.btn-small:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
}

.btn-icon {
    padding: 6px;
    background: transparent;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.btn-icon:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .settings-tabs {
        flex-direction: column;
    }

    .tab-btn {
        text-align: left;
        border-bottom: none;
        border-right: 3px solid transparent;
    }

    .tab-btn.active {
        border-bottom: none;
        border-right-color: var(--primary-color);
    }

    .settings-panel {
        grid-template-columns: 1fr;
        gap: 24px;
        max-height: none;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .button-group {
        flex-direction: column;
    }

    .panel-page .settings-content .tab-content {
        min-height: auto;
        height: auto !important;
    }

    .panel-page .settings-content .tab-panel {
        padding: 16px;
    }
}

/* === 终端样式 === */
.terminal-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-main) 100%);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
}

.terminal-controls button {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.terminal-controls button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.terminal-controls button:hover::before {
    left: 100%;
}

#start-terminal-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

#start-terminal-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

#stop-terminal-btn {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

#stop-terminal-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(239, 68, 68, 0.4);
}

#fullscreen-terminal-btn {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

#fullscreen-terminal-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

#clear-terminal-btn {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

#clear-terminal-btn:hover:not(:disabled) {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#terminal-container {
    background: var(--terminal-bg);
    border: 1px solid #333;
    border-radius: var(--radius-lg);
    overflow: hidden;
    min-height: 500px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    position: relative;
}

/* 终端标题栏 */
#terminal-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
    border-bottom: 1px solid #4a5568;
    z-index: 1;
}

/* 终端标题栏按钮 */
#terminal-container::after {
    content: '● ● ●';
    position: absolute;
    top: 8px;
    left: 12px;
    color: #ff5f56;
    font-size: 12px;
    letter-spacing: 4px;
    z-index: 2;
}

/* xterm.js 容器样式调整 */
#terminal-container .xterm {
    padding-top: 35px !important;
    background: transparent !important;
}

#terminal-container .xterm-viewport {
    background: var(--terminal-bg) !important;
}

#terminal-container .xterm-screen {
    background: var(--terminal-bg) !important;
}

#terminal-status {
    color: var(--text-secondary);
    font-size: 0.9em;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: var(--bg-main);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

#terminal-status::before {
    content: '●';
    font-size: 12px;
    animation: pulse 2s infinite;
}

#terminal-status.connected::before {
    color: var(--success-color);
}

#terminal-status.disconnected::before {
    color: var(--error-color);
}

#terminal-status.connecting::before {
    color: var(--warning-color);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 终端字体和文本样式 */
#terminal-container .xterm-rows {
    font-family: 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace !important;
    font-size: 14px !important;
    line-height: 1.4 !important;
    letter-spacing: 0.5px !important;
}

/* 终端滚动条美化 */
#terminal-container .xterm-viewport::-webkit-scrollbar {
    width: 8px;
}

#terminal-container .xterm-viewport::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

#terminal-container .xterm-viewport::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    transition: background 0.3s ease;
}

#terminal-container .xterm-viewport::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 终端光标样式 */
#terminal-container .xterm-cursor-layer .xterm-cursor {
    background-color: var(--terminal-cursor) !important;
    border: 1px solid var(--terminal-cursor) !important;
}

/* 终端选择文本样式 */
#terminal-container .xterm-selection {
    background-color: var(--terminal-selection) !important;
}

/* 终端链接样式 */
#terminal-container .xterm-rows a {
    color: #58a6ff !important;
    text-decoration: underline !important;
}

#terminal-container .xterm-rows a:hover {
    color: #79c0ff !important;
}

/* 终端响应式设计 */
@media (max-width: 768px) {
    .terminal-controls {
        flex-direction: column;
        gap: 10px;
        padding: 15px;
    }

    .terminal-controls button {
        width: 100%;
        padding: 15px;
        font-size: 16px;
    }

    #terminal-container {
        min-height: 300px;
    }

    #terminal-container .xterm-rows {
        font-size: 12px !important;
    }
}

/* 终端全屏模式 */
.terminal-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    border-radius: 0 !important;
    background: var(--bg-main) !important;
    padding: 20px !important;
    box-sizing: border-box !important;
}

.terminal-fullscreen #terminal-container {
    height: calc(100vh - 120px) !important;
    min-height: calc(100vh - 120px) !important;
    border-radius: var(--radius-lg) !important;
    width: 100% !important;
}

.terminal-fullscreen .terminal-controls {
    margin-bottom: 20px !important;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
    .navbar {
        flex-direction: column;
        height: auto;
        padding: 20px;
        gap: 20px;
    }
    
    .navbar-controls {
        flex-direction: column;
        width: 100%;
        gap: 15px;
    }
    
    .search-container {
        flex-direction: column;
        width: 100%;
    }
    
    .search-container input {
        width: 100%;
    }
    
    #clients-grid {
        padding: 10px;
    }
    
    .agents-table {
        font-size: 11px;
    }
    
    .agents-table th,
    .agents-table td {
        padding: 8px 6px;
    }
    
    /* 在小屏幕上隐藏一些不重要的列 */
    .arch-cell,
    .all-ips-cell,
    .cpu-cell,
    .process-cell,
    .id-cell {
        display: none;
    }
    
    .main-content {
        padding: 20px;
    }
    
    .detail-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 20px;
    }
    
    .mode-control {
        margin-left: 0;
        flex-wrap: wrap;
    }

    /* S5代理响应式 */
    .s5-control {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .s5-buttons {
        margin-left: 0;
        width: 100%;
        justify-content: flex-start;
        flex-wrap: wrap;
    }

    .s5-config-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .s5-config-row label {
        min-width: auto;
    }

    .s5-config-row input {
        width: 100%;
    }

    .s5-server-buttons, .s5-client-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .s5-btn {
        width: 100%;
        text-align: center;
    }
    
    .operations-tabs {
        flex-wrap: wrap;
    }
    
    .tab-link {
        flex: 1;
        min-width: 120px;
    }

    /* 文件管理器响应式 */
    .file-manager-container {
        height: auto;
    }

    .file-toolbar {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .toolbar-section {
        justify-content: center;
        flex-wrap: wrap;
    }

    .path-breadcrumb {
        min-width: auto;
        width: 100%;
    }

    .file-browser-container {
        flex-direction: column;
        height: auto;
    }

    .drives-panel {
        width: 100%;
        max-height: 250px;
        order: 2;
    }

    .files-panel {
        min-height: 400px;
        order: 1;
    }

    .file-grid {
        grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
        gap: 12px;
    }

    .toolbar-btn .btn-text {
        display: none;
    }

    .toolbar-btn {
        padding: 10px;
        min-width: 44px;
    }

    #file-list-table th:nth-child(2),
    #file-list-table td:nth-child(2),
    #file-list-table th:nth-child(4),
    #file-list-table td:nth-child(4) {
        display: none;
    }

    #file-list-table th:nth-child(1) { width: 60%; }
    #file-list-table th:nth-child(3) { width: 20%; }
    #file-list-table th:nth-child(5) { width: 20%; }
}

/* === 文件管理模态框样式 === */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid var(--border-color);
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    padding: 20px 25px;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3em;
    font-weight: 600;
}

.modal-body {
    padding: 25px;
    max-height: 70vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-title);
    font-weight: 500;
    font-size: 14px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.form-actions {
    display: flex;
    gap: 12px;
    justify-content: flex-end;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid var(--border-color);
}

.btn-primary,
.btn-secondary {
    padding: 12px 24px;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 170, 255, 0.3);
}

.btn-secondary {
    background: var(--bg-input);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--bg-secondary);
    transform: translateY(-1px);
}

/* === 文件编辑器特殊样式 === */
.file-editor-content {
    width: 90vw;
    height: 85vh;
    max-width: 1200px;
    max-height: 800px;
}

.editor-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.editor-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    font-size: 13px;
    color: var(--text-secondary);
}

#file-editor-textarea {
    width: 100%;
    height: calc(85vh - 200px);
    min-height: 400px;
    font-family: var(--font-mono);
    font-size: 14px;
    line-height: 1.5;
    resize: none;
    background: linear-gradient(135deg, #0d1117 0%, #161b22 100%);
    color: #00ff41;
    border: 1px solid rgba(0, 255, 65, 0.2);
}

.zip-options {
    display: flex;
    gap: 20px;
    margin: 15px 0;
}

.zip-options label {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
}

.zip-options input[type="checkbox"] {
    width: auto;
    margin: 0;
}

/* === 文件操作按钮样式 === */
.action-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
}

.action-btn {
    padding: 6px 10px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
    min-width: 32px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.action-btn.preview {
    background: var(--info-color);
    color: white;
}

.action-btn.preview:hover {
    background: #138496;
    transform: scale(1.1);
}

.action-btn.edit {
    background: var(--warning-color);
    color: white;
}

.action-btn.edit:hover {
    background: #e0a800;
    transform: scale(1.1);
}

.action-btn.download {
    background: var(--success-color);
    color: white;
}

.action-btn.download:hover {
    background: #28a745;
    transform: scale(1.1);
}

.action-btn.small {
    padding: 4px 8px;
    font-size: 11px;
    min-width: 24px;
    height: 24px;
}

.action-btn.disabled,
.action-btn:disabled {
    background: var(--bg-input) !important;
    color: var(--text-secondary) !important;
    cursor: not-allowed !important;
    opacity: 0.5;
    transform: none !important;
}

.action-btn.disabled:hover,
.action-btn:disabled:hover {
    background: var(--bg-input) !important;
    color: var(--text-secondary) !important;
    transform: none !important;
}

.folder-indicator {
    color: var(--text-secondary);
    font-size: 16px;
}

/* === 文件名样式增强 === */
.file-name {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.file-name.clickable {
    cursor: pointer;
    transition: all 0.3s ease;
}

.file-name.clickable:hover {
    color: var(--primary-color);
    transform: translateX(4px);
}

.file-icon {
    font-size: 16px;
    min-width: 20px;
    text-align: center;
}

.folder-icon {
    transition: all 0.3s ease;
}

.file-name.clickable:hover .folder-icon {
    transform: scale(1.2);
}

/* === 帮助模态框样式 === */
.help-section {
    margin-bottom: 25px;
}

.help-section h4 {
    color: var(--text-title);
    margin-bottom: 15px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.help-section ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.help-section li {
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary);
    line-height: 1.5;
}

.help-section li:last-child {
    border-bottom: none;
}

.shortcut-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.shortcut-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.shortcut-item:hover {
    background: var(--bg-input);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.shortcut-item kbd {
    background: var(--primary-color);
    color: white;
    padding: 6px 12px;
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 12px;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.shortcut-item span {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 14px;
}

/* === Chrome 数据管理样式 === */
.chrome-stats {
    display: flex;
    align-items: center;
    gap: 10px;
}

.chrome-stats span {
    font-weight: bold;
    color: var(--success-color);
    font-size: 16px;
}

.refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 12px;
    transition: var(--transition-normal);
}

.refresh-btn:hover {
    background: #0066cc;
}

.export-btn {
    background: #FF9800;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--radius-sm);
    cursor: pointer;
    font-size: 14px;
    margin-left: 10px;
    transition: var(--transition-normal);
}

.export-btn:hover {
    background: #F57C00;
}

.export-btn:disabled {
    background: #666;
    cursor: not-allowed;
}

#chrome-hostname-select {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-input);
    color: var(--text-primary);
    min-width: 200px;
    font-size: 14px;
}

#chrome-hostname-select option {
    background: var(--bg-input);
    color: var(--text-primary);
}

.settings-section + .settings-section {
    margin-top: 30px;
}

/* === 客户端操作页面专用样式 === */
.client-operation-panel {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-main);
}

.client-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 25px 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.client-info h2 {
    color: var(--text-title);
    margin: 0 0 8px 0;
    font-size: 1.8em;
    font-weight: 600;
}

.client-status {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-top: 8px;
}

.client-status .status-badge {
    padding: 4px 12px;
    border-radius: var(--radius-sm);
    font-size: 0.85em;
    font-weight: 500;
}

.client-status .status-badge.online {
    background: rgba(72, 187, 120, 0.2);
    color: var(--status-online);
    border: 1px solid var(--status-online);
}

.client-status .status-badge.offline {
    background: rgba(229, 62, 62, 0.2);
    color: var(--status-offline);
    border: 1px solid var(--status-offline);
}

.mode-control {
    display: flex;
    align-items: center;
    gap: 10px;
}

.mode-btn {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    background: var(--bg-input);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.9em;
}

.mode-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
}

.mode-btn:active {
    transform: translateY(1px);
}

/* === 命令快捷按钮样式 === */
.command-shortcuts {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 20px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.shortcut-btn {
    padding: 10px 18px;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: var(--text-primary);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9em;
    font-weight: 500;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.shortcut-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.shortcut-btn:hover::before {
    left: 100%;
}

.shortcut-btn:hover {
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 170, 255, 0.3);
}

.shortcut-btn:active {
    transform: translateY(0);
}

/* === 输入区域美化 === */
.input-area {
    display: flex;
    gap: 12px;
    align-items: center;
    padding: 16px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    margin-top: 16px;
    box-shadow: var(--shadow-sm);
}

.input-area input[type="text"] {
    flex: 1;
    padding: 12px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.9em;
    transition: var(--transition-fast);
}

.input-area input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-area button {
    padding: 12px 20px;
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-sm);
    color: white;
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: 0.9em;
    box-shadow: var(--shadow-sm);
}

.input-area button:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.input-area button:disabled {
    background: var(--text-muted);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* === 系统信息网格样式 === */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.info-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 24px;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-fast);
}

.info-card:hover {
    box-shadow: var(--shadow-md);
    border-color: var(--primary-color);
}

.info-card h4 {
    color: var(--text-title);
    margin: 0 0 15px 0;
    font-size: 1.1em;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.info-card p {
    margin: 8px 0;
    color: var(--text-primary);
    font-size: 0.9em;
    line-height: 1.5;
}

.info-card strong {
    color: var(--text-title);
    font-weight: 600;
}

/* === 战利品控制区域美化 === */
.loot-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 20px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.loot-controls textarea {
    padding: 12px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.9em;
    resize: vertical;
    min-height: 80px;
    transition: var(--transition-fast);
}

.loot-controls textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.loot-controls .button-group {
    display: flex;
    gap: 10px;
}

.loot-controls button {
    padding: 10px 20px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: 0.9em;
}

.loot-controls button:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* === 文件浏览器导航美化 === */
.file-browser-nav {
    display: flex;
    gap: 10px;
    align-items: center;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.file-browser-nav input[type="text"] {
    flex: 1;
    padding: 10px 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.9em;
    transition: var(--transition-fast);
}

.file-browser-nav input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.file-browser-nav button {
    padding: 10px 20px;
    background: var(--primary-color);
    border: none;
    border-radius: var(--radius-sm);
    color: white;
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
}

.file-browser-nav button:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* === 终端控制区域美化 === */
.terminal-controls {
    display: flex;
    gap: 15px;
    align-items: center;
    padding: 15px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.terminal-controls button {
    padding: 8px 16px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: 0.9em;
}

.terminal-controls button:not(:disabled):hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.terminal-controls button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

#start-terminal-btn {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

#start-terminal-btn:hover:not(:disabled) {
    background: #38a169;
}

#stop-terminal-btn {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

#stop-terminal-btn:hover:not(:disabled) {
    background: #c53030;
}

/* === 战利品按钮样式 === */
#add-loot-btn {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

#add-loot-btn:hover {
    background: #38a169;
}

#refresh-loot-btn {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

#refresh-loot-btn:hover {
    background: var(--primary-color-dark);
}

/* === 操作按钮样式 === */
.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 0.8em;
    font-weight: 500;
    margin: 0 2px;
}

.view-btn {
    background: var(--primary-color);
    color: white;
}

.view-btn:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
}

.delete-btn {
    background: var(--error-color);
    color: white;
}

.delete-btn:hover {
    background: #c53030;
    transform: translateY(-1px);
}

/* === 文件图标样式 === */
.file-icon {
    margin-right: 8px;
    font-size: 1.1em;
}

/* === 截图按钮美化 === */
.screenshot-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 600;
    font-size: 1em;
    box-shadow: var(--shadow-sm);
}

.screenshot-btn:hover {
    background: linear-gradient(135deg, var(--primary-color-dark), #0052a3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.screenshot-btn:active {
    transform: translateY(0);
}

/* === 导航栏按钮美化 === */
.nav-btn {
    padding: 8px 16px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
    font-weight: 500;
    font-size: 0.9em;
}

.nav-btn:hover {
    background: var(--bg-card-hover);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

#close-window-btn {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

#close-window-btn:hover {
    background: #c53030;
}

/* === 文件管理器容器 === */
.file-manager-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: var(--bg-main);
}

/* === 文件工具栏 === */
.file-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-card) 100%);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.toolbar-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.toolbar-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 170, 255, 0.3);
}

.toolbar-btn.primary {
    background: var(--primary-color);
    color: white;
}

.toolbar-btn.primary:hover {
    background: var(--primary-color-dark);
}

.btn-icon {
    font-size: 16px;
}

.btn-text {
    font-size: 13px;
}

.path-breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 300px;
}

.path-breadcrumb input {
    flex: 1;
    padding: 10px 14px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 13px;
}

.path-btn {
    padding: 10px 16px;
    background: var(--success-color);
    border: none;
    border-radius: var(--radius-md);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.path-btn:hover {
    background: #28a745;
    transform: translateY(-1px);
}

/* === 文件浏览器容器 === */
.file-browser-container {
    display: flex;
    flex: 1;
    height: 600px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    background: var(--bg-main);
}

.drives-panel {
    width: 320px;
    background: var(--bg-card);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
}

.drives-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-card) 100%);
}

.drives-header h4 {
    margin: 0;
    color: var(--text-title);
    font-size: 15px;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 8px;
}

.header-btn {
    padding: 6px 10px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
}

.header-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.refresh-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 5px;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.refresh-btn:hover {
    background: var(--bg-card-hover);
    color: var(--primary-color);
}

.tree-container {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.tree-node {
    margin: 2px 0;
}

.tree-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
    font-size: 13px;
    color: var(--text-primary);
}

.tree-item:hover {
    background: var(--bg-card-hover);
}

.tree-item.selected {
    background: var(--primary-color);
    color: white;
}

.tree-item.loading {
    color: var(--text-secondary);
    cursor: wait;
}

.tree-toggle {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--text-secondary);
    cursor: pointer;
}

.tree-toggle:hover {
    color: var(--primary-color);
}

.tree-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.tree-children {
    margin-left: 24px;
    display: none;
}

.tree-children.expanded {
    display: block;
}

.files-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-card);
}

.files-header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-card) 100%);
    flex-wrap: wrap;
    gap: 12px;
}

/* 导航控件 */
.navigation-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.nav-btn {
    padding: 8px 16px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.nav-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-btn:disabled {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 排序控件 */
.sort-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.sort-controls label {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-primary);
}

.sort-controls select {
    padding: 8px 12px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.sort-controls select:hover {
    border-color: var(--primary-color);
}

.sort-controls select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

/* 文件操作按钮 */
.file-actions {
    display: flex;
    gap: 8px;
    align-items: center;
}

.action-btn {
    padding: 8px 12px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    color: var(--text-primary);
}

.action-btn:hover {
    background: var(--bg-hover);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.view-controls {
    display: flex;
    gap: 4px;
    background: var(--bg-input);
    border-radius: var(--radius-md);
    padding: 4px;
}

.view-btn {
    padding: 8px 12px;
    background: transparent;
    border: none;
    border-radius: var(--radius-sm);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.view-btn.active,
.view-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.05);
}

.file-stats {
    color: var(--text-secondary);
    font-size: 13px;
    font-weight: 500;
}

.file-content-container {
    flex: 1;
    position: relative;
    overflow: hidden;
}

.file-view {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.file-view.active {
    opacity: 1;
    visibility: visible;
}

.file-browser-nav {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    gap: 12px;
    align-items: center;
    background: var(--bg-secondary);
}

.file-browser-nav input[type="text"] {
    flex: 1;
    padding: 12px 16px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    color: var(--text-primary);
    font-size: 14px;
    transition: var(--transition-fast);
}

.file-browser-nav input[type="text"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.file-table-container {
    flex: 1;
    overflow: auto;
    padding: 20px;
    background: var(--bg-main);
}

/* === 文件列表视图 === */
#file-list-view {
    padding: 20px;
    overflow: auto;
}

#file-list-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
    table-layout: fixed;
    border-radius: var(--radius-md);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    background: var(--bg-main);
}

#file-list-table th {
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-card) 100%);
    color: var(--text-title);
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 10;
}

#file-list-table th.sortable {
    cursor: pointer;
    user-select: none;
    transition: all 0.3s ease;
}

#file-list-table th.sortable:hover {
    background: var(--primary-color);
    color: white;
}

.sort-indicator {
    margin-left: 8px;
    opacity: 0.5;
    transition: all 0.3s ease;
}

#file-list-table th.sortable:hover .sort-indicator {
    opacity: 1;
}

#file-list-table td {
    padding: 14px 12px;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

#file-list-table tr:hover {
    background: var(--bg-secondary);
    transform: scale(1.01);
}

#file-list-table tr:hover td {
    color: var(--text-primary);
}

/* === 文件网格视图 === */
#file-grid-view {
    padding: 20px;
    overflow: auto;
}

.file-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 16px;
    padding: 10px;
}

.file-grid-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px 16px;
    background: var(--bg-main);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
}

.file-grid-checkbox {
    position: absolute;
    top: 8px;
    left: 8px;
    z-index: 10;
}

.file-grid-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
}

.file-grid-item.selected {
    background: var(--primary-color-light);
    border-color: var(--primary-color);
}

.selected-files-container {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 12px;
    background: var(--bg-secondary);
}

.selected-file-item {
    display: flex;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid var(--border-color);
}

.selected-file-item:last-child {
    border-bottom: none;
}

.selected-file-icon {
    margin-right: 8px;
    font-size: 16px;
}

.selected-file-name {
    flex: 1;
    font-size: 13px;
    color: var(--text-primary);
}

.no-selection {
    text-align: center;
    color: var(--text-secondary);
    font-style: italic;
    padding: 20px;
}

.file-grid-item:hover {
    background: var(--bg-secondary);
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.file-grid-icon {
    font-size: 48px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

.file-grid-item:hover .file-grid-icon {
    transform: scale(1.1);
}

.file-grid-name {
    font-size: 13px;
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: 8px;
    word-break: break-word;
    line-height: 1.3;
}

.file-grid-info {
    font-size: 11px;
    color: var(--text-secondary);
    margin-bottom: 12px;
}

.file-grid-actions {
    display: flex;
    gap: 8px;
    opacity: 0;
    transition: all 0.3s ease;
}

.file-grid-item:hover .file-grid-actions {
    opacity: 1;
}

#file-list-table th:nth-child(1) { width: 40%; } /* 名称 */
#file-list-table th:nth-child(2) { width: 10%; } /* 类型 */
#file-list-table th:nth-child(3) { width: 15%; } /* 大小 */
#file-list-table th:nth-child(4) { width: 20%; } /* 修改时间 */
#file-list-table th:nth-child(5) { width: 15%; } /* 操作 */

#file-list-table th {
    background: var(--bg-secondary);
    color: var(--text-title);
    padding: 16px 12px;
    text-align: left;
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    position: sticky;
    top: 0;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

#file-list-table td {
    padding: 14px 12px;
    border-bottom: 1px solid var(--border-light);
    color: var(--text-primary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    background: var(--bg-card);
}

#file-list-table tr:hover td {
    background: var(--bg-card-hover);
}

.file-icon {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.folder-icon {
    color: var(--primary-color);
}

.file-icon.file {
    color: var(--text-secondary);
}

/* 不同文件类型的图标颜色 */
.file-icon[data-type="exe"], .file-icon[data-type="msi"] {
    color: #e74c3c;
}

.file-icon[data-type="txt"], .file-icon[data-type="log"] {
    color: #95a5a6;
}

.file-icon[data-type="pdf"] {
    color: #e74c3c;
}

.file-icon[data-type="doc"], .file-icon[data-type="docx"] {
    color: #2980b9;
}

.file-icon[data-type="xls"], .file-icon[data-type="xlsx"] {
    color: #27ae60;
}

.file-icon[data-type="zip"], .file-icon[data-type="rar"], .file-icon[data-type="7z"] {
    color: #f39c12;
}

.file-icon[data-type="jpg"], .file-icon[data-type="png"], .file-icon[data-type="gif"] {
    color: #9b59b6;
}

.file-name {
    display: flex;
    align-items: center;
    font-weight: 500;
}

.file-name.clickable {
    cursor: pointer;
    color: var(--primary-color);
}

.file-name.clickable:hover {
    text-decoration: underline;
}

.loading {
    text-align: center;
    color: var(--text-secondary);
    padding: 20px;
    font-style: italic;
}

/* === 战利品查看器专用样式 === */
.loot-viewer-panel {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: var(--bg-main);
    padding: 20px;
    overflow: hidden;
}

/* === 战利品统计卡片 === */
.loot-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.stat-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-number {
    font-size: 2em;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.9em;
    font-weight: 500;
}

/* === 过滤器区域 === */
.loot-filters {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-group input,
.filter-group select {
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.filter-group input {
    flex: 1;
    min-width: 200px;
}

.filter-group select {
    min-width: 150px;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

/* === 战利品网格 === */
.loot-grid {
    flex: 1;
    overflow-y: auto;
}

#loot-grid-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
    padding: 10px;
}

/* === 文件卡片 === */
.loot-file-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
}

.loot-file-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
    border-color: var(--primary-color);
}

.file-icon {
    font-size: 2.5em;
    text-align: center;
    margin-bottom: 15px;
}

.file-info {
    margin-bottom: 15px;
}

.file-name {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-title);
    margin-bottom: 8px;
    line-height: 1.3;
}

.file-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
}

.file-type {
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    font-weight: 500;
}

.file-size {
    color: var(--text-secondary);
    font-size: 0.85em;
}

.file-date {
    color: var(--text-muted);
    font-size: 0.8em;
}

.file-actions {
    text-align: center;
    display: flex;
    gap: 8px;
    justify-content: center;
}

.file-actions .action-btn {
    padding: 8px 14px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 12px;
    min-width: 60px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.file-actions .action-btn:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* === 空状态 === */
.empty-state {
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 10px 0;
    color: var(--text-title);
    font-size: 1.5em;
}

.empty-state p {
    margin: 0 0 30px 0;
    font-size: 1em;
    line-height: 1.5;
}

.primary-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1em;
}

.primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 170, 255, 0.3);
}

/* === 模态框样式 === */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--bg-card);
    border-radius: 15px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-header {
    padding: 20px 25px;
    background: linear-gradient(135deg, var(--primary-color), #0066cc);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.2em;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5em;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 5px;
    transition: background 0.3s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: 25px;
    max-height: 50vh;
    overflow-y: auto;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-title);
    font-weight: 500;
}

.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 0.9em;
    font-family: inherit;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    font-family: var(--font-mono);
}

.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
}

.modal-footer {
    padding: 15px 25px;
    background: var(--bg-secondary);
    border-top: 1px solid var(--border-color);
    text-align: right;
    display: flex;
    gap: 10px;
    justify-content: flex-end;
}

.secondary-btn {
    padding: 8px 16px;
    background: var(--bg-input);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s;
    font-weight: 500;
}

.secondary-btn:hover {
    background: var(--bg-card-hover);
}

/* === 动画 === */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* === S5代理标签页样式 === */
.s5proxy-container {
    max-width: 1000px;
    margin: 0 auto;
}

.s5proxy-info {
    background: rgba(0, 170, 255, 0.1);
    border: 1px solid rgba(0, 170, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
}

.s5proxy-info p {
    margin: 0;
    color: var(--text-primary);
    font-size: 0.95em;
}

.s5-server-section, .s5-client-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 25px;
    margin-bottom: 25px;
}

.s5-server-section h3, .s5-client-section h3 {
    margin: 0 0 20px 0;
    color: var(--text-title);
    font-size: 1.2em;
    font-weight: 600;
}

.s5-config-row {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 15px;
}

.s5-config-row label {
    min-width: 140px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9em;
}

.s5-config-row input {
    flex: 1;
    padding: 10px 15px;
    background: var(--bg-input);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    color: var(--text-primary);
    font-size: 0.9em;
}

.s5-config-row input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 170, 255, 0.2);
}

.config-hint {
    display: block;
    margin-top: 5px;
    font-size: 0.8em;
    color: var(--text-muted);
    font-style: italic;
}

.s5-server-buttons, .s5-client-buttons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    flex-wrap: wrap;
}

.status-display {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 15px;
    padding: 12px 15px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
}

.status-label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 0.9em;
}

.status-text {
    font-size: 0.9em;
    color: var(--text-muted);
}

.s5-usage-section {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 25px;
}

.s5-usage-section h3 {
    margin: 0 0 20px 0;
    color: var(--text-title);
    font-size: 1.2em;
    font-weight: 600;
}

.usage-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.usage-step {
    padding: 15px;
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-sm);
}

.usage-step strong {
    color: var(--primary-color);
    font-size: 0.95em;
}

.usage-step p {
    margin: 8px 0 0 0;
    color: var(--text-secondary);
    font-size: 0.9em;
    line-height: 1.5;
}

/* === 备注标签页样式 === */
.notes-container {
    max-width: 800px;
    margin: 0 auto;
}

.notes-info {
    background: rgba(0, 170, 255, 0.1);
    border: 1px solid rgba(0, 170, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    color: var(--text-primary);
}

.notes-info p {
    margin: 0;
    font-size: 0.9em;
    line-height: 1.5;
}

.notes-editor {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.notes-editor textarea {
    width: 100%;
    min-height: 300px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.95em;
    line-height: 1.6;
    resize: vertical;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.notes-editor textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 170, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.notes-editor textarea::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.notes-actions {
    display: flex;
    gap: 15px;
    margin-top: 20px;
    justify-content: center;
}

.notes-actions .primary-btn {
    padding: 12px 30px;
    background: linear-gradient(135deg, var(--success-color), #38a169);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1em;
    box-shadow: 0 4px 15px rgba(72, 187, 120, 0.3);
}

.notes-actions .primary-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(72, 187, 120, 0.4);
    background: linear-gradient(135deg, #38a169, #2f855a);
}

.notes-actions .secondary-btn {
    padding: 12px 30px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 1em;
}

.notes-actions .secondary-btn:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.notes-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9em;
    color: var(--text-secondary);
}

#notes-status-text {
    font-weight: 500;
}

#notes-status-text.saved {
    color: var(--success-color);
}

#notes-status-text.unsaved {
    color: var(--warning-color);
}

#notes-last-saved {
    font-size: 0.85em;
    color: var(--text-muted);
}

/* === 屏幕截图标签页样式 === */
.screenshot-container {
    max-width: 1000px;
    margin: 0 auto;
}

.screenshot-info {
    background: rgba(0, 170, 255, 0.1);
    border: 1px solid rgba(0, 170, 255, 0.3);
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 25px;
    color: var(--text-primary);
}

.screenshot-info p {
    margin: 0;
    font-size: 0.9em;
    line-height: 1.5;
}

.screenshot-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-bottom: 20px;
    padding: 25px;
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.screenshot-btn {
    padding: 15px 30px;
    background: linear-gradient(135deg, #ff6b6b, #ee5a52);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 1.1em;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
    position: relative;
    overflow: hidden;
}

.screenshot-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.screenshot-btn:hover::before {
    left: 100%;
}

.screenshot-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
    background: linear-gradient(135deg, #ee5a52, #e74c3c);
}

.screenshot-btn:disabled {
    background: rgba(255, 255, 255, 0.1);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.clear-screenshot-btn {
    padding: 12px 24px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 1em;
}

.clear-screenshot-btn:hover {
    transform: translateY(-2px);
    background: rgba(255, 255, 255, 0.1);
    border-color: var(--error-color);
    color: var(--error-color);
}

.screenshot-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9em;
    color: var(--text-secondary);
    margin-bottom: 25px;
}

#screenshot-status-text {
    font-weight: 500;
}

#screenshot-status-text.capturing {
    color: var(--warning-color);
}

#screenshot-status-text.completed {
    color: var(--success-color);
}

#screenshot-status-text.error {
    color: var(--error-color);
}

#screenshot-time {
    font-size: 0.85em;
    color: var(--text-muted);
}

.screenshot-display {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.screenshot-placeholder {
    text-align: center;
    color: var(--text-secondary);
    padding: 60px 20px;
}

.placeholder-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.screenshot-placeholder h4 {
    margin: 0 0 10px 0;
    color: var(--text-title);
    font-size: 1.5em;
}

.screenshot-placeholder p {
    margin: 0;
    font-size: 1em;
    line-height: 1.5;
}

.screenshot-image {
    max-width: 100%;
    max-height: 80vh;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.screenshot-image:hover {
    transform: scale(1.02);
}

.screenshot-actions {
    position: absolute;
    top: 15px;
    right: 15px;
    display: flex;
    gap: 10px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.screenshot-display:hover .screenshot-actions {
    opacity: 1;
}

.screenshot-action-btn {
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 0.9em;
}

.screenshot-action-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

/* === 战利品文件操作按钮样式 === */
.file-actions .download-btn {
    background: var(--success-color);
    color: white;
}

.file-actions .download-btn:hover {
    background: #28a745;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.file-actions .delete-btn {
    background: var(--error-color);
    color: white;
}

.file-actions .delete-btn:hover {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.file-actions .view-btn {
    background: var(--info-color);
    color: white;
}

.file-actions .view-btn:hover {
    background: #138496;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

/* 文件操作按钮间距 */
.file-actions {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.file-actions .action-btn {
    font-size: 11px;
    padding: 6px 10px;
    min-width: 50px;
    white-space: nowrap;
}

/* === 战利品查看标签页样式 === */
.loot-container {
    padding: 20px;
    max-width: 100%;
    margin: 0 auto;
}

.loot-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid var(--border-color);
}

.loot-header h3 {
    margin: 0;
    color: var(--text-color);
    font-size: 1.5em;
}

.loot-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.loot-info #loot-agent-name {
    color: var(--text-secondary);
    font-size: 0.9em;
}

.loot-info #loot-hostname {
    color: var(--primary-color);
}

.refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: var(--primary-hover);
    transform: translateY(-1px);
}

/* 战利品统计卡片 */
.loot-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 25px;
}

.stat-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    font-size: 2em;
    opacity: 0.8;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--primary-color);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 0.85em;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 战利品过滤器 */
.loot-filters {
    display: flex;
    gap: 20px;
    margin-bottom: 25px;
    padding: 15px;
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 10px;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-group label {
    font-weight: 500;
    color: var(--text-color);
    white-space: nowrap;
}

.filter-group select,
.filter-group input {
    padding: 6px 10px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    background: var(--input-bg);
    color: var(--text-color);
    font-size: 0.9em;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

/* 战利品网格 */
.loot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* 战利品文件卡片 */
.loot-file-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 20px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.loot-file-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.loot-file-header {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    margin-bottom: 15px;
}

.loot-file-icon {
    font-size: 2em;
    opacity: 0.8;
    flex-shrink: 0;
}

.loot-file-info {
    flex: 1;
    min-width: 0;
}

.loot-file-name {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
    word-break: break-word;
    line-height: 1.3;
}

.loot-file-type {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75em;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.loot-file-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 0.85em;
    color: var(--text-secondary);
}

.loot-file-size {
    font-weight: 500;
}

.loot-file-time {
    opacity: 0.8;
}

.loot-file-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

.loot-file-actions .action-btn {
    padding: 6px 12px;
    font-size: 0.85em;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.empty-icon {
    font-size: 4em;
    margin-bottom: 20px;
    opacity: 0.5;
}

.empty-state h4 {
    margin: 0 0 10px 0;
    color: var(--text-color);
    font-size: 1.2em;
}

.empty-state p {
    margin: 0;
    font-size: 0.9em;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-color);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 单行信息栏 - 真正的一行布局 */
.single-line-header {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 6px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 13px;
    min-height: 32px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.left-info {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
}

.brand-title {
    font-size: 16px;
    font-weight: bold;
}

.hostname-main {
    font-weight: 600;
    color: var(--text-title);
    font-size: 15px;
}

.page-title {
    color: var(--text-secondary);
    font-size: 13px;
}

.status-inline {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 500;
    background: var(--status-online);
    color: white;
}

.last-seen-inline {
    color: var(--text-secondary);
    font-size: 11px;
}

.agent-id-inline {
    color: var(--text-secondary);
    font-size: 10px;
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 1px 4px;
    border-radius: 3px;
}

.center-controls {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
}

.mode-inline {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    background: var(--success-color);
    color: white;
    font-weight: 500;
}

.btn-inline {
    padding: 2px 6px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-color);
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s;
    font-weight: 400;
}

.btn-inline:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.separator {
    color: rgba(255, 255, 255, 0.3);
    margin: 0 3px;
    font-weight: 300;
}

.s5-label-inline {
    font-weight: 500;
    color: var(--text-secondary);
    font-size: 11px;
}

.s5-status-inline {
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    background: var(--error-color);
    color: white;
    font-weight: 500;
}

.s5-status-inline.s5-online {
    background: var(--success-color);
}

.s5-info-inline {
    color: var(--text-secondary);
    font-size: 10px;
    font-family: 'Courier New', monospace;
}

.right-actions {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* === 下载进度弹窗样式 === */
.download-info {
    padding: 20px 0;
}

.file-info {
    text-align: center;
    margin-bottom: 25px;
}

.file-info .file-name {
    font-size: 1.2em;
    font-weight: 600;
    color: var(--text-title);
    margin-bottom: 8px;
    word-break: break-all;
}

.file-info .file-size {
    color: var(--text-secondary);
    font-size: 0.95em;
    font-family: var(--font-mono);
}

.progress-container {
    margin: 25px 0;
}

.progress-bar {
    width: 100%;
    height: 12px;
    background: var(--bg-secondary);
    border-radius: 6px;
    overflow: hidden;
    position: relative;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-hover));
    border-radius: 6px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    animation: progressShine 2s infinite;
}

@keyframes progressShine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    text-align: center;
    margin-top: 10px;
    font-weight: 600;
    color: var(--text-title);
    font-size: 1.1em;
}

.download-stats {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
    margin-top: 20px;
}

.stat-item {
    text-align: center;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.stat-label {
    display: block;
    font-size: 0.8em;
    color: var(--text-secondary);
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.stat-value {
    display: block;
    font-weight: 600;
    color: var(--text-title);
    font-size: 0.95em;
    font-family: var(--font-mono);
}

#download-cancel-btn {
    background: var(--error-color);
    color: white;
}

#download-cancel-btn:hover {
    background: #dc2626;
}

#download-progress-close-btn {
    background: transparent;
    color: white;
    border: none;
    font-size: 1.2em;
    padding: 8px;
    cursor: pointer;
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

#download-progress-close-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

/* === 新建文件/文件夹按钮样式 === */
/* 新建按钮现在使用统一的 action-btn 样式，在操作栏中显示 */

/* 新建文件模态框特殊样式 */
.form-textarea {
    width: 100%;
    min-height: 120px;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-primary);
    color: var(--text-primary);
    font-family: var(--font-mono);
    font-size: 0.9em;
    line-height: 1.4;
    resize: vertical;
    transition: var(--transition-fast);
}

.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-textarea::placeholder {
    color: var(--text-secondary);
    font-style: italic;
}

.input-hint {
    font-size: 0.8em;
    color: var(--text-secondary);
    margin-top: 4px;
    font-style: italic;
}

/* 实时远程桌面样式 */
.remote-desktop-container {
    padding: 20px;
    max-width: 1600px; /* 增加最大宽度从1400px到1600px */
    margin: 0 auto;
    height: auto; /* 改为自动高度 */
    display: flex;
    flex-direction: column;
}

.remote-desktop-info {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 15px;
    margin-bottom: 20px;
}

.remote-desktop-info p {
    margin: 5px 0;
    color: var(--text-primary);
}

.remote-desktop-controls {
    margin-bottom: 15px;
}

.desktop-control-bar {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

.desktop-btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.desktop-btn.start {
    background: var(--success-color);
    color: white;
}

.desktop-btn.start:hover {
    background: #16a34a;
}

.desktop-btn.stop {
    background: var(--error-color);
    color: white;
}

.desktop-btn.stop:hover {
    background: #dc2626;
}

.desktop-btn.fullscreen {
    background: #3b82f6;
    color: white;
}

.desktop-btn.fullscreen:hover {
    background: #2563eb;
}

.desktop-status {
    font-weight: 500;
    color: var(--text-primary);
}

.desktop-fps {
    font-family: var(--font-mono);
    font-size: 12px;
    color: var(--text-secondary);
}

.remote-desktop-screen {
    flex: 1;
    display: flex;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    overflow: hidden;
    min-height: 700px; /* 添加最小高度，确保屏幕区域足够大 */
}

.desktop-screen-container {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #000;
    position: relative;
    overflow: hidden;
}

.desktop-placeholder {
    text-align: center;
    color: #666;
}

.desktop-placeholder .placeholder-icon {
    font-size: 64px;
    margin-bottom: 20px;
}

.desktop-placeholder h3 {
    margin: 0 0 10px 0;
    color: #888;
}

.desktop-placeholder p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.desktop-screen-container canvas,
.desktop-screen-container img {
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
    object-fit: contain; /* 保持比例，完整显示 */
    cursor: crosshair;
    border-radius: 4px;
}

.remote-desktop-info-panel {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-top: 15px;
}

.desktop-stats {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 13px;
}

.stat-value {
    color: var(--text-primary);
    font-weight: 500;
    font-size: 13px;
}

.desktop-help {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 15px;
}

.desktop-help h4 {
    margin: 0 0 10px 0;
    color: var(--text-primary);
    font-size: 14px;
}

.desktop-help ul {
    margin: 0;
    padding-left: 20px;
    color: var(--text-secondary);
    font-size: 12px;
}

.desktop-help li {
    margin-bottom: 4px;
}

.screen-preview-display {
    border: 2px dashed var(--border-color);
    border-radius: var(--radius-md);
    min-height: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--bg-secondary);
    position: relative;
    overflow: hidden;
}

.screen-placeholder {
    text-align: center;
    color: var(--text-muted);
}

.placeholder-icon {
    font-size: 48px;
    margin-bottom: 10px;
}

.screen-preview-display img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    cursor: crosshair;
}

.screen-info {
    margin-top: 10px;
    padding: 10px;
    background: var(--bg-secondary);
    border-radius: var(--radius-sm);
    font-family: var(--font-mono);
    font-size: 12px;
    color: var(--text-secondary);
}

.mouse-controls,
.keyboard-controls,
.unlock-controls {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.coordinate-input,
.drag-controls,
.text-input,
.password-input {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.coordinate-input label,
.drag-controls label,
.text-input label,
.password-input label {
    min-width: 60px;
    font-weight: 500;
    color: var(--text-primary);
}

.mouse-buttons,
.key-buttons,
.combo-keys {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.control-btn,
.key-btn,
.combo-btn {
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    background: var(--bg-input);
    color: var(--text-primary);
    cursor: pointer;
    font-size: 13px;
    transition: var(--transition-fast);
}

.control-btn:hover,
.key-btn:hover,
.combo-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.control-btn.danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
}

.control-btn.danger:hover {
    background: #dc2626;
    border-color: #dc2626;
}

.unlock-warning {
    font-size: 12px;
    color: var(--warning-color);
    margin: 10px 0 0 0;
    font-style: italic;
}

.remote-control-status {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: 15px;
    text-align: center;
    font-family: var(--font-mono);
    font-size: 14px;
    color: var(--text-secondary);
}

/* 全屏模式样式 */
.desktop-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: #000 !important;
    margin: 0 !important;
    padding: 0 !important;
    max-width: none !important;
}

.desktop-fullscreen .remote-desktop-info,
.desktop-fullscreen .remote-desktop-controls,
.desktop-fullscreen .remote-desktop-info-panel {
    display: none !important;
}

.desktop-fullscreen .remote-desktop-screen {
    border: none !important;
    border-radius: 0 !important;
    min-height: 100vh !important;
}

.desktop-fullscreen .desktop-screen-container {
    border-radius: 0 !important;
}

/* 全屏退出按钮 */
.fullscreen-exit-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    display: none;
}

.fullscreen-exit-btn:hover {
    background: rgba(0, 0, 0, 0.9);
}

.desktop-fullscreen .fullscreen-exit-btn {
    display: block !important;
}

/* ==================== 客户端生成器样式 ==================== */
.generator-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
}

/* 生成器视图样式 */
.generator-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 20px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.generator-header h2 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 700;
}

.generator-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 16px;
}

.generator-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 导航按钮样式 */
.nav-btn.listeners {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.nav-btn.listeners:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
    transform: translateY(-1px);
}

.nav-btn.generator {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
}

.nav-btn.generator:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
}

.form-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.form-section h4 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 8px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 14px;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-group input:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.protocol-info {
    margin-top: 10px;
    padding: 10px;
    background: var(--bg-tertiary);
    border-radius: var(--radius-sm);
    font-size: 12px;
    color: var(--text-secondary);
    border-left: 3px solid var(--info-color);
    display: none;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 10px;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    transition: background 0.3s, border-color 0.3s;
}

.feature-item:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
}

.feature-item input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.feature-item label {
    margin: 0;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
}

.generate-btn {
    width: 100%;
    padding: 15px;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-md);
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
}

.generate-btn:hover {
    background: var(--primary-color-dark);
    transform: translateY(-1px);
}

.generate-btn:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    transform: none;
}

.generation-result {
    margin-top: 30px;
    padding: 20px;
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.generation-result.success {
    background: var(--success-color-light);
    border-color: var(--success-color);
}

.generation-result.error {
    background: var(--error-color-light);
    border-color: var(--error-color);
}

.download-btn {
    display: inline-block;
    margin-top: 15px;
    padding: 10px 20px;
    background: var(--success-color);
    color: white;
    text-decoration: none;
    border-radius: var(--radius-md);
    font-weight: 600;
    transition: background 0.3s;
}

.download-btn:hover {
    background: var(--success-color-dark);
}

/* CGO功能提示样式 */
.cgo-badge {
    display: inline-block;
    background: #ff6b35;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 5px;
    font-weight: bold;
}

.cgo-notice {
    margin-top: 15px;
    padding: 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--radius-md);
    font-size: 13px;
    color: #856404;
}

.cgo-notice p {
    margin: 5px 0;
}

.cgo-notice a {
    color: #0066cc;
    text-decoration: none;
}

.cgo-notice a:hover {
    text-decoration: underline;
}

/* ==================== 监听器管理样式 ==================== */
.listeners-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 30px 20px;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
}

.listeners-header h2 {
    margin: 0 0 10px 0;
    color: var(--primary-color);
    font-size: 28px;
    font-weight: 700;
}

.listeners-header p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 16px;
}

.listeners-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 添加监听器表单 */
.add-listener-section {
    margin-bottom: 30px;
    padding: 20px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
}

.add-listener-section h3 {
    margin: 0 0 20px 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.add-listener-btn {
    background: var(--success-color);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: var(--radius-md);
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
}

.add-listener-btn:hover {
    background: var(--success-color-dark);
    transform: translateY(-1px);
}

/* 监听器统计卡片 */
.listeners-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    padding: 20px;
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    text-align: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-card.running {
    border-left: 4px solid var(--success-color);
}

.stat-card.stopped {
    border-left: 4px solid var(--warning-color);
}

.stat-card.error {
    border-left: 4px solid var(--error-color);
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: var(--text-secondary);
    font-weight: 500;
}

/* 监听器列表 */
.listeners-list-section {
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: var(--bg-secondary);
}

.section-header h3 {
    margin: 0;
    color: var(--text-primary);
    font-size: 18px;
    font-weight: 600;
}

.refresh-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s;
}

.refresh-btn:hover {
    background: var(--primary-color-dark);
}

/* 监听器表格 */
.listeners-table-container {
    overflow-x: auto;
}

.listeners-table {
    width: 100%;
    border-collapse: collapse;
}

.listeners-table th,
.listeners-table td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.listeners-table th {
    background: var(--bg-tertiary);
    font-weight: 600;
    color: var(--text-primary);
    font-size: 14px;
}

.listeners-table td {
    color: var(--text-primary);
    font-size: 14px;
}

.listeners-table tbody tr:hover {
    background: var(--bg-secondary);
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.running {
    background: var(--success-color-light);
    color: var(--success-color);
}

.status-badge.stopped {
    background: var(--warning-color-light);
    color: var(--warning-color);
}

.status-badge.error {
    background: var(--error-color-light);
    color: var(--error-color);
}

.status-badge.starting {
    background: var(--info-color-light);
    color: var(--info-color);
}

/* 协议标签 */
.protocol-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    color: white;
}

.protocol-badge.HTTP {
    background: #3498db;
}

.protocol-badge.HTTPS {
    background: #27ae60;
}

.protocol-badge.WS {
    background: #9b59b6;
}

.protocol-badge.WSS {
    background: #8e44ad;
}

.protocol-badge.KCP {
    background: #e67e22;
}

.protocol-badge.UDP {
    background: #e74c3c;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: background 0.3s, transform 0.2s;
}

.action-btn:hover {
    transform: translateY(-1px);
}

.action-btn.stop {
    background: var(--warning-color);
    color: white;
}

.action-btn.stop:hover {
    background: var(--warning-color-dark);
}

.action-btn.restart {
    background: var(--info-color);
    color: white;
}

.action-btn.restart:hover {
    background: var(--info-color-dark);
}

.action-btn.remove {
    background: var(--error-color);
    color: white;
}

.action-btn.remove:hover {
    background: var(--error-color-dark);
}

/* 无数据状态 */
.no-data {
    text-align: center;
    padding: 60px 20px;
    color: var(--text-secondary);
}

.no-data-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.no-data-text {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.no-data-hint {
    font-size: 14px;
    opacity: 0.7;
}

/* 远程桌面响应式设计 */
@media (max-width: 768px) {
    .remote-desktop-info-panel {
        grid-template-columns: 1fr;
    }

    .desktop-control-bar {
        flex-wrap: wrap;
        gap: 10px;
    }

    .remote-desktop-container {
        height: auto; /* 改为自动高度 */
        padding: 10px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .generator-container {
        padding: 10px;
    }
}

/* 移除强制修复样式，使用原始简单样式 */