package handlers

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"goc2/s5"
)

// HandleGetProxies 获取所有代理列表
func HandleGetProxies(w http.ResponseWriter, r *http.Request) {
	log.Printf("[SOCKS5 Manager] 收到获取代理列表请求")
	
	manager := s5.GetProxyManager()
	proxies := manager.GetAllProxies()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"proxies": proxies,
		"stats":   manager.GetStats(),
	})
}

// HandleCreateProxy 创建新代理
func HandleCreateProxy(w http.ResponseWriter, r *http.Request) {
	log.Printf("[SOCKS5 Manager] 收到创建代理请求")
	
	var req struct {
		AgentID   string `json:"agent_id"`
		Hostname  string `json:"hostname"`
		LocalPort int    `json:"local_port"`
		Username  string `json:"username"`
		Password  string `json:"password"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("[SOCKS5 Manager] 解析请求数据失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "请求数据格式错误"})
		return
	}

	// 验证必填字段
	if req.AgentID == "" || req.Hostname == "" || req.LocalPort == 0 {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少必填字段"})
		return
	}

	manager := s5.GetProxyManager()
	proxy, err := manager.CreateProxy(req.AgentID, req.Hostname, req.LocalPort, req.Username, req.Password)
	if err != nil {
		log.Printf("[SOCKS5 Manager] 创建代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理创建成功: %s", proxy.ID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message": "代理创建成功",
		"proxy":   proxy,
	})
}

// HandleStartProxy 启动代理
func HandleStartProxy(w http.ResponseWriter, r *http.Request) {
	proxyID := r.URL.Query().Get("proxy_id")
	if proxyID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少proxy_id参数"})
		return
	}

	log.Printf("[SOCKS5 Manager] 启动代理: %s", proxyID)
	
	manager := s5.GetProxyManager()
	if err := manager.StartProxy(proxyID); err != nil {
		log.Printf("[SOCKS5 Manager] 启动代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理启动成功: %s", proxyID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "代理启动成功"})
}

// HandleStopProxy 停止代理
func HandleStopProxy(w http.ResponseWriter, r *http.Request) {
	proxyID := r.URL.Query().Get("proxy_id")
	if proxyID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少proxy_id参数"})
		return
	}

	log.Printf("[SOCKS5 Manager] 停止代理: %s", proxyID)
	
	manager := s5.GetProxyManager()
	if err := manager.StopProxy(proxyID); err != nil {
		log.Printf("[SOCKS5 Manager] 停止代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理停止成功: %s", proxyID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "代理停止成功"})
}

// HandlePauseProxy 暂停代理
func HandlePauseProxy(w http.ResponseWriter, r *http.Request) {
	proxyID := r.URL.Query().Get("proxy_id")
	if proxyID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少proxy_id参数"})
		return
	}

	log.Printf("[SOCKS5 Manager] 暂停代理: %s", proxyID)
	
	manager := s5.GetProxyManager()
	if err := manager.PauseProxy(proxyID); err != nil {
		log.Printf("[SOCKS5 Manager] 暂停代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理暂停成功: %s", proxyID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "代理暂停成功"})
}

// HandleResumeProxy 恢复代理
func HandleResumeProxy(w http.ResponseWriter, r *http.Request) {
	proxyID := r.URL.Query().Get("proxy_id")
	if proxyID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少proxy_id参数"})
		return
	}

	log.Printf("[SOCKS5 Manager] 恢复代理: %s", proxyID)
	
	manager := s5.GetProxyManager()
	if err := manager.ResumeProxy(proxyID); err != nil {
		log.Printf("[SOCKS5 Manager] 恢复代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理恢复成功: %s", proxyID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "代理恢复成功"})
}

// HandleDeleteProxy 删除代理
func HandleDeleteProxy(w http.ResponseWriter, r *http.Request) {
	proxyID := r.URL.Query().Get("proxy_id")
	if proxyID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少proxy_id参数"})
		return
	}

	log.Printf("[SOCKS5 Manager] 删除代理: %s", proxyID)
	
	manager := s5.GetProxyManager()
	if err := manager.DeleteProxy(proxyID); err != nil {
		log.Printf("[SOCKS5 Manager] 删除代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	log.Printf("[SOCKS5 Manager] 代理删除成功: %s", proxyID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{"message": "代理删除成功"})
}

// HandleStopAllProxies 停止所有代理
func HandleStopAllProxies(w http.ResponseWriter, r *http.Request) {
	log.Printf("[SOCKS5 Manager] 停止所有代理")
	
	manager := s5.GetProxyManager()
	proxies := manager.GetAllProxies()
	
	stoppedCount := 0
	for _, proxy := range proxies {
		if proxy.Status == "running" || proxy.Status == "paused" {
			if err := manager.StopProxy(proxy.ID); err != nil {
				log.Printf("[SOCKS5 Manager] 停止代理 %s 失败: %v", proxy.ID, err)
			} else {
				stoppedCount++
			}
		}
	}

	log.Printf("[SOCKS5 Manager] 已停止 %d 个代理", stoppedCount)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":       fmt.Sprintf("已停止 %d 个代理", stoppedCount),
		"stopped_count": stoppedCount,
	})
}

// HandleGetProxyStats 获取代理统计信息
func HandleGetProxyStats(w http.ResponseWriter, r *http.Request) {
	manager := s5.GetProxyManager()
	stats := manager.GetStats()
	
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(stats)
}

// HandleCreateProxyForAgent 为特定Agent创建代理（从client.html调用）
func HandleCreateProxyForAgent(w http.ResponseWriter, r *http.Request) {
	agentID := r.URL.Query().Get("agent_id")
	if agentID == "" {
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "缺少agent_id参数"})
		return
	}

	var req struct {
		LocalPort int    `json:"local_port"`
		Username  string `json:"username"`
		Password  string `json:"password"`
		Hostname  string `json:"hostname"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		log.Printf("[SOCKS5 Manager] 解析请求数据失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusBadRequest)
		json.NewEncoder(w).Encode(map[string]string{"error": "请求数据格式错误"})
		return
	}

	log.Printf("[SOCKS5 Manager] 为Agent %s 创建代理: 端口=%d", agentID, req.LocalPort)
	
	manager := s5.GetProxyManager()
	proxy, err := manager.CreateProxy(agentID, req.Hostname, req.LocalPort, req.Username, req.Password)
	if err != nil {
		log.Printf("[SOCKS5 Manager] 创建代理失败: %v", err)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": err.Error()})
		return
	}

	// 自动启动代理
	log.Printf("[SOCKS5 Manager] 正在启动代理: %s", proxy.ID)
	if err := manager.StartProxy(proxy.ID); err != nil {
		log.Printf("[SOCKS5 Manager] 启动代理失败: %v", err)
		// 删除创建失败的代理
		manager.DeleteProxy(proxy.ID)
		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusInternalServerError)
		json.NewEncoder(w).Encode(map[string]string{"error": "启动代理失败: " + err.Error()})
		return
	}
	log.Printf("[SOCKS5 Manager] 代理启动成功: %s", proxy.ID)

	log.Printf("[SOCKS5 Manager] 代理创建并启动成功: %s", proxy.ID)
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"message":     "代理创建并启动成功",
		"proxy":       proxy,
		"tunnel_port": proxy.TunnelPort,
	})
}
