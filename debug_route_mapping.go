package main

import (
	"crypto/md5"
	"encoding/hex"
	"fmt"
	"strings"
	"time"
)

func main() {
	fmt.Println("=== 🔍 路由映射调试 ===")
	
	// 当前时间
	currentHour := time.Now().Truncate(time.Hour)
	fmt.Printf("当前时间: %s\n", currentHour.Format("2006-01-02 15:04:05"))
	
	// 路径模板
	normalPaths := []string{
		"/wp-content/uploads/%s/%s.jpg",
		"/wp-content/themes/%s/assets/%s.css",
		"/wp-content/plugins/%s/js/%s.js",
		"/wp-includes/js/%s/%s.min.js",
		"/wp-admin/css/%s.css",
		"/assets/images/%s/%s.png",
		"/static/css/%s.css",
		"/static/js/%s.js",
		"/media/uploads/%s.jpg",
		"/content/files/%s.pdf",
		"/api/v1/%s/%s",
		"/resources/%s/%s.xml",
		"/data/%s.json",
		"/cache/%s.tmp",
		"/logs/%s.log",
	}
	
	// 敏感路由
	sensitiveRoutes := []string{
		"/api/sync/profile",
		"/api/sync/bookmarks",
		"/api/backup/data",
		"/api/media/stream",
	}
	
	fmt.Println("\n=== 当前路由映射 ===")
	
	for _, realRoute := range sensitiveRoutes {
		obfuscatedRoute := generateTimedPath(realRoute, normalPaths, currentHour)
		fmt.Printf("真实路由: %s\n", realRoute)
		fmt.Printf("混淆路由: %s\n", obfuscatedRoute)
		fmt.Println("---")
	}
	
	// 测试反向映射
	fmt.Println("\n=== 反向映射测试 ===")
	testPaths := []string{
		"/wp-content/plugins/b6666186/js/7bf28bde.js",
		"/cache/1c79748a.tmp",
	}
	
	for _, testPath := range testPaths {
		realRoute := getRealRouteFromObfuscated(testPath, sensitiveRoutes, normalPaths, currentHour)
		fmt.Printf("混淆路径: %s\n", testPath)
		fmt.Printf("真实路由: %s\n", realRoute)
		fmt.Println("---")
	}
}

func generateTimedPath(realPath string, normalPaths []string, timeBase time.Time) string {
	seed := fmt.Sprintf("%s_%d", realPath, timeBase.Unix())
	hash := md5.Sum([]byte(seed))
	hashStr := hex.EncodeToString(hash[:])
	
	templateIndex := int(hash[0]) % len(normalPaths)
	template := normalPaths[templateIndex]
	
	param1 := hashStr[:8]
	param2 := hashStr[8:16]
	
	paramCount := strings.Count(template, "%s")
	switch paramCount {
	case 1:
		return fmt.Sprintf(template, param1)
	case 2:
		return fmt.Sprintf(template, param1, param2)
	default:
		return fmt.Sprintf(template, param1)
	}
}

func getRealRouteFromObfuscated(obfuscatedPath string, realRoutes []string, normalPaths []string, timeBase time.Time) string {
	for _, realRoute := range realRoutes {
		generatedPath := generateTimedPath(realRoute, normalPaths, timeBase)
		if generatedPath == obfuscatedPath {
			return realRoute
		}
	}
	return "未找到匹配"
}
