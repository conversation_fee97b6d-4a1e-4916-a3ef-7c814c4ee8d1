package ossbrow

import (
	"fmt"
	"golang.org/x/sys/windows"
	"os"
	"strconv"
	"strings"
	"unsafe"
)

// MEMORY_BASIC_INFORMATION、VirtualQueryExFuncWrapper、ReadBytesFromMemory
// 假设在其他地方已定义，这里只关注匹配逻辑示例

// parsePattern 解析模式字符串，支持部分通配符
// 例如：
//
//	"??" 表示任意一个字节（高低 nibble均通配，返回字节 0x00，掩码 0x00）
//	"A?" 表示高 nibble 固定为 A，低 nibble 任意（返回 0xA0，掩码 0xF0）
//	"?A" 表示低 nibble 固定为 A，高 nibble 任意（返回 0x0A，掩码 0x0F）
func parsePattern(pattern string) ([]byte, []byte, error) {
	parts := strings.Fields(pattern)
	patternBytes := make([]byte, 0, len(parts))
	maskBytes := make([]byte, 0, len(parts))

	for _, part := range parts {
		if len(part) != 2 {
			return nil, nil, fmt.Errorf("无效的模式字节长度: %s", part)
		}
		var b byte = 0
		var m byte = 0
		// 对每个 nibble 解析
		for _, ch := range part {
			b <<= 4
			m <<= 4
			if ch == '?' {
				// 通配符 nibble，不限制匹配，掩码该 nibble 为 0
				b |= 0x0
				m |= 0x0
			} else {
				// 非通配符，解析为十六进制数字，掩码为 0xF
				digit, err := strconv.ParseUint(string(ch), 16, 4)
				if err != nil {
					return nil, nil, fmt.Errorf("无效的十六进制字符 '%c' in %s", ch, part)
				}
				b |= byte(digit)
				m |= 0xF
			}
		}
		patternBytes = append(patternBytes, b)
		maskBytes = append(maskBytes, m)
	}

	return patternBytes, maskBytes, nil
}

// GetNextArray 构建 Next 数组，用于 Boyer-Moore 算法优化
// 仅对全匹配字节（掩码为 0xFF）参与优化
func GetNextArray(pattern []byte, mask []byte) []int {
	next := make([]int, 256)
	for i := 0; i < 256; i++ {
		next[i] = -1
	}
	for i, b := range pattern {
		if mask[i] == 0xFF {
			next[b] = i
		}
	}
	return next
}

// SearchMemoryBlock 在指定内存块中搜索匹配的特征码
func SearchMemoryBlock(hProcess windows.Handle, pattern []byte, mask []byte, next []int, startAddress uintptr, size uintptr, resultArray *[]uintptr) {
	// 读取内存块
	data, err := ReadBytesFromMemory(hProcess, startAddress, size)
	if err != nil {
		fmt.Fprintf(os.Stderr, "读取内存块失败，地址=0x%X，错误=%v\n", startAddress, err)
		return
	}

	patternLength := len(pattern)
	i := 0
	for i <= len(data)-patternLength {
		j := 0
		// 对每个字节进行匹配：只比较 mask 为1的 nibble
		for j < patternLength {
			if (data[i+j] & mask[j]) != (pattern[j] & mask[j]) {
				break
			}
			j++
		}

		if j == patternLength {
			// 匹配成功，记录地址
			matchAddr := startAddress + uintptr(i)
			*resultArray = append(*resultArray, matchAddr)
			// 如果启用了 Next 数组优化则跳跃，否则逐字节扫描
			if next != nil {
				lastByte := data[i+patternLength-1]
				nextVal := next[lastByte]
				skip := patternLength - nextVal
				if skip < 1 {
					skip = 1
				}
				i += skip
			} else {
				i++
			}
		} else {
			// 未匹配时，根据优化方案跳跃
			if next != nil && i+patternLength < len(data) {
				nextByte := data[i+patternLength]
				nextVal := next[nextByte]
				skip := patternLength - nextVal
				if skip < 1 {
					skip = 1
				}
				i += skip
			} else {
				i++
			}
		}
	}
}

// SearchMemory 遍历进程内存区域，查找所有匹配的特征码
func SearchMemory(hProcess windows.Handle, pattern []byte, mask []byte, next []int, startAddress uintptr, endAddress uintptr, resultArray *[]uintptr) {
	var mbi MEMORY_BASIC_INFORMATION

	for {
		ret, _ := VirtualQueryExFuncWrapper(hProcess, startAddress, &mbi, unsafe.Sizeof(mbi))
		if ret == 0 {
			break // 没有更多内存区域
		}

		// 只扫描已提交内存区域
		if mbi.State == windows.MEM_COMMIT &&
			(mbi.Protect == windows.PAGE_READWRITE ||
				mbi.Protect == windows.PAGE_EXECUTE_READWRITE ||
				mbi.Protect == windows.PAGE_READONLY ||
				mbi.Protect == windows.PAGE_EXECUTE) {
			blockSize := mbi.RegionSize
			currentAddress := uintptr(mbi.BaseAddress)

			// 分块扫描防止内存块过大
			for blockSize > 0 {
				var currentBlockSize uintptr = BLOCKMAXSIZE
				if blockSize < BLOCKMAXSIZE {
					currentBlockSize = blockSize
				}

				SearchMemoryBlock(hProcess, pattern, mask, next, currentAddress, currentBlockSize, resultArray)

				currentAddress += currentBlockSize
				blockSize -= currentBlockSize
			}
		}

		startAddress += uintptr(mbi.RegionSize)
		if endAddress != 0 && startAddress > endAddress {
			break
		}
	}
}

// SearchFeatures 解析模式并在进程内存中搜索特征码，返回匹配地址数组及匹配数量
func SearchFeatures(hProcess windows.Handle, patternStr string) ([]uintptr, int) {
	// 解析模式字符串
	pattern, mask, err := parsePattern(patternStr)
	if err != nil {
		fmt.Fprintf(os.Stderr, "解析特征码失败: %v\n", err)
		return nil, 0
	}

	// 若存在部分通配符（掩码不全为 0xFF），则禁用 Next 数组优化
	useOptimization := true
	for _, m := range mask {
		if m != 0xFF {
			useOptimization = false
			break
		}
	}

	var next []int
	if useOptimization {
		next = GetNextArray(pattern, mask)
		fmt.Printf("Next数组: %v\n", next)
	} else {
		next = nil
		fmt.Println("存在部分通配符，禁用 Next 数组优化。")
	}

	var resultArray []uintptr
	//var startAddr uintptr = 0x0
	//var endAddr uintptr = ^uintptr(0)
	//SearchMemory(hProcess, pattern, mask, next, startAddr, endAddr, &resultArray)

	// 搜索整个进程内存（起始地址和结束地址根据实际需要调整）
	SearchMemory(hProcess, pattern, mask, next, 0x0000000000000000, 0xFFFFFFFFFFFFFFFF, &resultArray)

	fmt.Printf("找到 %d 个匹配地址。\n", len(resultArray))

	return resultArray, len(resultArray)
}
