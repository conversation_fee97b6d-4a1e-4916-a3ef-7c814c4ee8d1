package finalshell

import (
	"bytes"
	"crypto/des"
	"crypto/md5"
	"encoding/base64"
	"encoding/binary"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strconv"
)

// 移除不可打印字符的函数
func removeNonPrintableChars(input string) string {
	re := regexp.MustCompile(`[\x00-\x1F\x7F-\x9F]`)
	return re.ReplaceAllString(input, "")
}

// 自定义随机数生成器，模拟Java的Random类
type Random struct {
	seed uint64
}

// 初始化随机数生成器
func NewRandom(seed int64) *Random {
	return &Random{
		seed: (uint64(seed) ^ 0x5DEECE66D) & ((1 << 48) - 1),
	}
}

// 生成下一个随机数
func (r *Random) next(bits int) int {
	r.seed = (r.seed*0x5DEECE66D + 0xB) & ((1 << 48) - 1)
	value := int(r.seed >> (48 - bits))
	if value < (1 << (bits - 1)) {
		return value
	}
	return value - (1 << bits)
}

// 生成下一个整数
func (r *Random) NextInt() int {
	return r.next(32)
}

// 生成下一个长整数
func (r *Random) NextLong() int64 {
	high := int64(r.next(32))
	low := int64(r.next(32))
	return (high << 32) + low
}

// 生成下一个浮点数
func (r *Random) NextFloat() float32 {
	return float32(r.next(24)) / float32(1<<24)
}

// 生成下一个双精度浮点数
func (r *Random) NextDouble() float64 {
	// 先进行整数位移操作，再转换为浮点数
	return (float64(r.next(26)<<27) + float64(r.next(27))) * (1.0 / float64(1<<53))
}

// DES解密（ECB模式）
func desDecode(data, key []byte) ([]byte, error) {
	block, err := des.NewCipher(key)
	if err != nil {
		return nil, err
	}

	// 确保数据长度是DES块大小的倍数
	if len(data)%des.BlockSize != 0 {
		return nil, fmt.Errorf("数据长度不是DES块大小的倍数")
	}

	decrypted := make([]byte, len(data))
	for start := 0; start < len(data); start += des.BlockSize {
		block.Decrypt(decrypted[start:start+des.BlockSize], data[start:start+des.BlockSize])
	}
	return decrypted, nil
}

// 计算MD5哈希
func md5Hash(data []byte) []byte {
	sum := md5.Sum(data)
	return sum[:]
}

// 根据头信息生成随机密钥
func randomKey(head []byte) ([]byte, error) {
	ilist := []int{
		24, 54, 89, 120, 19, 49, 85, 115, 14, 44, 80, 110, 9, 40, 75, 106, 43, 73, 109, 12, 38, 68, 104, 7, 33, 64,
		99, 3, 28, 59, 94, 125, 112, 16, 51, 82, 107, 11, 46, 77, 103, 6, 41, 72, 98, 1, 37, 67, 4, 35, 70, 101, 0,
		30, 65, 96, 122, 25, 61, 91, 117, 20, 56, 86, 74, 104, 13, 43, 69, 99, 8, 38, 64, 95, 3, 34, 59, 90, 125,
		29, 93, 123, 32, 62, 88, 119, 27, 58, 83, 114, 22, 53, 79, 109, 17, 48, 35, 66, 101, 5, 31, 61, 96, 0, 26,
		56, 92, 122, 21, 51, 87, 117, 55, 85, 120, 24, 50, 80, 116, 19, 45, 75, 111, 14, 40, 71, 106, 10, 50, 81,
		116, 20, 45, 76, 111, 15, 41, 71, 106, 10, 36, 66, 102, 5, 69, 100, 8, 39, 65, 95, 3, 34, 60, 90, 126, 29,
		55, 85, 121, 24, 12, 42, 78, 108, 7, 37, 73, 103, 2, 33, 68, 99, 124, 28, 63, 94, 31, 61, 97, 0, 26, 57,
		92, 123, 21, 52, 87, 118, 17, 47, 82, 113, 100, 4, 39, 70, 96, 126, 34, 65, 91, 121, 30, 60, 86, 116, 25,
		55, 120, 23, 58, 89, 115, 18, 54, 84, 110, 13, 49, 79, 105, 9, 44, 75, 62, 92, 1, 31, 57, 88, 123, 27, 52,
		83, 118, 22, 48, 78, 113, 17, 81, 112, 20, 51, 76, 107, 15, 46, 72, 102, 10, 41, 67, 97, 6, 36,
	}

	if len(head) < 8 { // 确保head长度为8字节
		return nil, fmt.Errorf("head长度小于8字节")
	}

	i := ilist[int(head[5])]
	ks := 3680984568597093857 / int64(i)
	random := NewRandom(int64(ks))
	t := head[0]
	for j := 0; j < int(t); j++ {
		random.NextLong()
	}
	n := random.NextLong()
	r2 := NewRandom(n)
	ld := []int64{
		int64(head[4]),
		r2.NextLong(),
		int64(head[7]),
		int64(head[3]),
		r2.NextLong(),
		int64(head[1]),
		random.NextLong(),
		int64(head[2]),
	}

	var byteStream bytes.Buffer
	for _, l := range ld {
		// 将int64转换为uint64以避免位操作溢出
		err := binary.Write(&byteStream, binary.BigEndian, uint64(l))
		if err != nil {
			return nil, err
		}
	}

	keyData := md5Hash(byteStream.Bytes())[:8]
	return keyData, nil
}

// 解码密码的函数
func decodePass(data string) (string, error) {
	if data == "" {
		return "", nil
	}

	decodedData, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return "", err
	}

	if len(decodedData) < 8 {
		return "", fmt.Errorf("解码后的数据长度小于8字节")
	}

	head := decodedData[:8]
	d := decodedData[8:]

	key, err := randomKey(head)
	if err != nil {
		return "", err
	}

	decrypted, err := desDecode(d, key)
	if err != nil {
		return "", err
	}

	rs := string(decrypted)
	rs = removeNonPrintableChars(rs)
	return rs, nil
}

// 用于表示JSON数据的结构体
type JsonData struct {
	ForwardingAutoReconnect bool                   `json:"forwarding_auto_reconnect"`
	CustomSize              bool                   `json:"custom_size"`
	DeleteTime              int64                  `json:"delete_time"`
	SecretKeyID             string                 `json:"secret_key_id"`
	UserName                string                 `json:"user_name"`
	RemotePortForwarding    map[string]interface{} `json:"remote_port_forwarding"`
	ConectionType           int                    `json:"conection_type"`
	SortTime                int64                  `json:"sort_time"`
	Description             string                 `json:"description"`
	ProxyID                 interface{}            `json:"proxy_id"` // <--- 修改为 interface{}
	AuthenticationType      int                    `json:"authentication_type"`
	DriveStoreRedirect      bool                   `json:"drivestoredirect"`
	DeleteKeySequence       int                    `json:"delete_key_sequence"`
	Password                string                 `json:"password"`
	ModifiedTime            int64                  `json:"modified_time"`
	Host                    string                 `json:"host"`
	Accelerate              bool                   `json:"accelerate"`
	ID                      string                 `json:"id"`
	Height                  int                    `json:"height"`
	Order                   int                    `json:"order"`
	CreateTime              int64                  `json:"create_time"`
	PortForwardingList      []interface{}          `json:"port_forwarding_list"`
	ParentUpdateTime        int64                  `json:"parent_update_time"`
	RenameTime              int64                  `json:"rename_time"`
	BackspaceKeySequence    int                    `json:"backspace_key_sequence"`
	Fullscreen              bool                   `json:"fullscreen"`
	Port                    int                    `json:"port"`
	TerminalEncoding        string                 `json:"terminal_encoding"`
	ParentID                string                 `json:"parent_id"`
	ExecChannelEnable       bool                   `json:"exec_channel_enable"`
	Width                   int                    `json:"width"`
	Name                    string                 `json:"name"`
	AccessTime              int64                  `json:"access_time"`
}

// 从JSON文件中获取主机、用户名、密码、端口及其他有用信息
func getUserAndPass(filePath string) (string, string, string, int, int, string, string, error) {
	fileBytes, err := ioutil.ReadFile(filePath)
	if err != nil {
		return "", "", "", 0, 0, "", "", err
	}

	var jsonData JsonData
	if err = json.Unmarshal(fileBytes, &jsonData); err != nil {
		return "", "", "", 0, 0, "", "", err
	}

	// 把 interface{} → string
	var proxyIDStr string
	switch v := jsonData.ProxyID.(type) {
	case string:
		proxyIDStr = v
	case float64: // JSON 数字默认反序列化成 float64
		proxyIDStr = strconv.FormatInt(int64(v), 10)
	case nil:
		proxyIDStr = ""
	default:
		proxyIDStr = fmt.Sprintf("%v", v)
	}

	return jsonData.Host, jsonData.UserName, jsonData.Password,
		jsonData.Port, jsonData.AuthenticationType,
		proxyIDStr, // 已转换成 string
		jsonData.Description, nil
}

// 处理JSON文件的函数
func decodeJSONFiles(srcPath string) error {
	fmt.Printf("开始解码路径: %s\n", srcPath)

	// 创建或打开输出 CSV 文件
	outputFile, err := os.Create("finalshell_decoded.csv")
	if err != nil {
		return fmt.Errorf("无法创建输出文件: %v", err)
	}
	defer outputFile.Close()

	writer := csv.NewWriter(outputFile)
	defer writer.Flush()

	// 写入 CSV 标题行
	header := []string{"Host", "Username", "Password", "Port", "AuthenticationType", "ProxyID", "Description"}
	if err := writer.Write(header); err != nil {
		return fmt.Errorf("无法写入标题行: %v", err)
	}

	fileInfo, err := os.Stat(srcPath)
	if err != nil {
		return err
	}

	// 定义一个内部函数来处理单个文件
	processFile := func(filePath string) {
		host, username, password, port, authType, proxyID, description, err := getUserAndPass(filePath)
		if err != nil {
			fmt.Printf("读取文件 %s 时出错: %v\n", filePath, err)
			return
		}
		if username != "" && password != "" {
			decodedPass, err := decodePass(password)
			if err != nil {
				fmt.Printf("解码文件 %s 中的密码时出错: %v\n", filePath, err)
				return
			}
			fmt.Printf("[+] %s:%s:%s:%d:%d:%s:%s\n", host, username, decodedPass, port, authType, proxyID, description)

			// 写入 CSV 行
			record := []string{
				host,
				username,
				decodedPass,
				strconv.Itoa(port),
				strconv.Itoa(authType),
				proxyID,
				description,
			}
			if err := writer.Write(record); err != nil {
				fmt.Printf("无法写入记录 %v: %v\n", record, err)
			}
		}
	}

	if fileInfo.Mode().IsRegular() && filepath.Ext(srcPath) == ".json" {
		processFile(srcPath)
	} else if fileInfo.IsDir() {
		files, err := ioutil.ReadDir(srcPath)
		if err != nil {
			return err
		}

		for _, file := range files {
			if !file.IsDir() && filepath.Ext(file.Name()) == ".json" {
				filePath := filepath.Join(srcPath, file.Name())
				processFile(filePath)
			}
		}
	} else {
		return fmt.Errorf("提供的路径既不是文件也不是目录")
	}

	fmt.Println("解码完成，结果已保存到 decoded_output.csv")
	return nil
}

// 主函数
func FinalShell_Decode() {
	// 解析命令行参数
	//srcPath := flag.String("s", "./conn", "源文件或目录路径")
	//flag.Parse()

	err := decodeJSONFiles("./conn")
	if err != nil {
		log.Fatalf("错误: %v\n", err)
	}
}
